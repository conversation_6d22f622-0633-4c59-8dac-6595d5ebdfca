/*
 * @Author: chenting<PERSON> <EMAIL>
 * @Date: 2025-05-09 14:43:33
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-06-10 11:40:51
 * @FilePath: \fe-dcrg-admin\src\main.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue';
import pinia from '/@/stores/index';
import App from './App.vue';
import router from './router';
import { directive } from '/@/directive';
import Components from '/@/components';
import Design from '/@/plugins/design';
import { i18n } from '/@/i18n';
import { properties } from '/@/utils/globalProperties';
import { initAntiDebug } from '/@/utils/anti-debug';
import { registerIcon } from '/@/views/vform/utils/el-icons'
import 'virtual:svg-icons-register'

import ContainerWidgets from '/@/views/vform/components/form-designer/form-widget/container-widget/index'
import ContainerItems from '/@/views/vform/components/form-render/container-item/index'

import { addDirective } from '/@/views/vform/utils/directive'
import { installI18n } from '/@/views/vform/utils/i18n'
import { loadExtension } from '/@/views/vform/extension/extension-loader'
// 注册插件
import plugins from './plugins/index'; // plugins

// Initialize anti-debug protection
initAntiDebug();
// 导入全局样式
import '/@/theme/tailwind.css';
import '/@/theme/index.scss';

const app = createApp(App);

// 全局自定方法挂载
properties(app);
// 全局自定义指令挂载
directive(app);

// 定义全局函数
app.config.globalProperties.translateRouteName = (name: string) => {
	const [m] = name.split('-')
	return m
};
registerIcon(app)
addDirective(app)
installI18n(app)

app
	.use(pinia) // pinia 存储
	.use(router) // 路由
	.use(Components) // 全局引入自定义的组件&第三方的组件
	.use(Design) // 全局引入ylz-material的组件
	.use(i18n) // 国际化
	.use(plugins)
	.mount('#app');
app.use(ContainerWidgets)
app.use(ContainerItems)
loadExtension(app)