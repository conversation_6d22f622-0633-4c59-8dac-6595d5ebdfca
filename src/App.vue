<template>
  <el-config-provider :size="getGlobalComponentSize" :locale="getGlobalI18n">
    <router-view v-show="setLockScreen" v-loading="appLoading" />
    <LockScreen v-if="themeConfig.isLockScreen" />
    <Setings ref="settingsRef" v-show="themeConfig.lockScreenTime > 1" />
    <CloseFull v-if="!themeConfig.isLockScreen" />
    <!-- <SystemBarTrigger
      v-if="showSystemBar"
      v-model="bottomBarVisible"
    /> -->
    <!-- <SystemBar     
      v-if="showSystemBar"
      v-model="systemBarVisible" 
    /> -->
  </el-config-provider>
</template>

<script setup lang="ts" name="app">
import { useI18n } from 'vue-i18n';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import { useThemeConfig } from '/@/stores/themeConfig';
import other from '/@/utils/other';
import { appLoading } from '/@/hooks/useAppLoading';
import { Local, Session } from '/@/utils/storage';
import mittBus from '/@/utils/mitt';
import setIntroduction from '/@/utils/setIconfont';
import SystemBar from '/@/components/system-bar.vue';
import SystemBarTrigger from '/@/components/system-bar-trigger.vue';

// 引入组件
const LockScreen = defineAsyncComponent(() => import('/@/layout/lockScreen/index.vue'));
const Setings = defineAsyncComponent(
  () => import('/@/layout/navBars/breadcrumb/setings.vue')
);
const CloseFull = defineAsyncComponent(
  () => import('/@/layout/navBars/breadcrumb/closeFull.vue')
);

// 定义变量内容
const { messages, locale } = useI18n();
const settingsRef = ref();
const route = useRoute();
const stores = useTagsViewRoutes();
const storesThemeConfig = useThemeConfig();
const bottomBarVisible = ref(false);
const systemBarVisible = ref(true);
const { themeConfig } = storeToRefs(storesThemeConfig);
// 设置锁屏时组件显示隐藏
const setLockScreen = computed(() => {
  // 防止锁屏后，刷新出现不相关界面
  // https://gitee.com/lyt-top/vue-next-admin/issues/I6AF8P
  return themeConfig.value.isLockScreen
    ? themeConfig.value.lockScreenTime > 1
    : themeConfig.value.lockScreenTime >= 0;
});

// 获取全局组件大小
const getGlobalComponentSize = computed(() => {
  return other.globalComponentSize();
});
// 获取全局 i18n
const getGlobalI18n = computed(() => {
  return messages.value[locale.value];
});
// 设置初始化，防止刷新时恢复默认
onBeforeMount(() => {
  // 设置批量第三方 icon 图标
  setIntroduction.cssCdn();
  // 设置批量第三方 js
  setIntroduction.jsCdn();
});
// 页面加载时
onMounted(() => {
  document.addEventListener('submit', (e) => {
    e.preventDefault();
  });

  // 全局处理回车键
  document.addEventListener('keyup', (e: any) => {
    if (e.key === 'Enter' && e.target?.tagName === 'INPUT') {
      e.preventDefault();
    }
  });
  nextTick(() => {
    // 监听布局配'置弹窗点击打开
    mittBus.on('openSetingsDrawer', () => {
      settingsRef.value.openDrawer();
    });
    // 获取缓存中的布局配置
    if (Local.get('themeConfig')) {
      storesThemeConfig.setThemeConfig({ themeConfig: Local.get('themeConfig') });
      document.documentElement.style.cssText = Local.get('themeConfigStyle');
    }
    // 获取缓存中的全屏配置
    if (Session.get('isTagsViewCurrenFull')) {
      stores.setCurrenFullscreen(Session.get('isTagsViewCurrenFull'));
    }
  });
});
// 页面销毁时，关闭监听布局配置/i18n监听
onUnmounted(() => {
  mittBus.off('openSetingsDrawer', () => {});
});
// 监听路由的变化，设置网站标题
watch(
  () => route.path,
  () => {
    other.useTitle();
  },
  {
    deep: true,
  }
);
watch(
  bottomBarVisible,
  (v) => {
    if (!v) {
      systemBarVisible.value = true;
    }
  },
  {
    deep: true,
  }
);
watch(
  systemBarVisible,
  (v) => {
    if (!v) {
      bottomBarVisible.value = true;
    }
  },
  {
    deep: true,
  }
);

const showSystemBar = computed(() => false);
const systemBarState = ref<'normal' | 'up'>('normal');
function handleBarEnter() {
  systemBarState.value = 'up';
}
function handleBarLeave() {
  // 只要鼠标离开 system-bar 区域就下移
  systemBarState.value = 'normal';
}
</script>
<style lang="scss">
.verify-sub-block {
  left: 0;
}
.is-message-box {
  z-index: 9999 !important;
}
</style>
