import request from '/@/utils/request';

/**
 *  分页查询租户下所有设备告警列表
 */
export function warehouseListApi(data?: Object, params?: Object) {
  return request({
    url: '/admin/sysMessage/user/page',
    method: 'get',
    data,
    params,
  });
}

/**
 *  分页查询租户下所有采购计划列表
 */
export function planListApi(data?: Object, params?: Object) {
  return request({
    url: '/admin/srmProcurementPlan/page',
    method: 'post',
    data,
    params,
  });
}

/**
 *  查询采购计划详情
 */
export function getPlanDetail(planCode?: string) {
  return request({
    url: `/admin/srmProcurementPlan/byPlanCode`,
    method: 'get',
    params: { code:  planCode },
  })
}

/**
 *  查询物料分类树
 */
export function getMaterialCategory() {
  return request({
    url: '/admin/baseMaterialCategory/tree',
    method: 'get'
  })
}

/**
 *  查询物料分页
 */
export function getMaterialPage(data?: Object, params?: Object) {
  return request({
    url: '/admin/baseMaterialInfo/page',
    method: 'post',
    data,
    params,
  })
}

/**
 *  新增采购计划
 */
export function procurementPlanAdd(data?: Object) {
  return request({
    url: '/admin/srmProcurementPlan/entire/save',
    method: 'post',
    data,
  })
}

/**
 *  删除采购计划
 */
export function procurementPlanRemove(ids: String[]) {
  return request({
    url: '/admin/srmProcurementPlan/remove',
    method: 'get',
    params: { ids:  ids },
  })
}

/**
 *  查询用户列表
 */
export function getUserListApi(deptIdList: string[]) {
  return request({
    url: '/admin/user/getUserIdListByDeptIdList/detail',
    method: 'get',
    params: { deptIdList: deptIdList}
  })
}

/**
 *  查询代理机构列表
 */
export function getAgentListApi() {
  return request({
    url: '/admin/baseAgentOrg/list',
    method: 'post',
    data: {}
  })
}

/**
 *  委托采购
 */
export function delegatePurchase(data?: Object) {
  return request({
    url: '/admin/srmProcurementPlan/update/only',
    method: 'post',
    data,
  })
}

/**
 *  作废
 */
export function terminatePlan(data: Object) {
  return request({
    url: '/admin/processManagement/startProcess',
    method: 'post',
    data,
  })
}

/**
 *  撤销审批
 */
export function cancelApprovePlan(data: Object) {
  return request({
    url: '/admin/processManagement/stopProcess',
    method: 'post',
    data,
  })
}

/**
 *  设备列表分页
 */
export function warehouseDeleteApi(obj?: Object, params?: Object) {
  return request({
    url: '/saas_iot_biz/tenant/device/devicePage',
    method: 'post',
    data: obj,
    params,
  });
}


/**
 *  省市区接口(高德)
 */

const jsonToUrl = (json) =>
  Object.keys(json)
    .map((key) => `${key}=${json[key]}`)
    .join('&');
const gaode_web_server_key = '90960c45bad658ba8b9c7d17a6ffddd3'; // 高德web服务key

export async function getCityList () {
  const params = jsonToUrl({
    key: gaode_web_server_key,
    subdistrict: 3,
    extensions: 'base',
  });
  try {
    const res = await fetch(`https://restapi.amap.com/v3/config/district?${params}`, {
      method: 'get',
    });
    return res.json();
  } catch (error) {
    return null;
  }
}
