<script setup lang="ts">
import { rendererMap, type RendererType } from '.';

interface TableColumn {
	prop: string;
	label: string;
	type: string;
	attrs?: Record<string, any>;
}

interface Props {
	value: Array<Record<string, any>>;
	column: {
		attrs?: {
			showIndex?: boolean;
			columns?: TableColumn[];
		};
	};
}

defineProps<Props>();

function getTypeComponent(type: string) {
	return rendererMap[type as RendererType] || rendererMap.text;
}
</script>

<template>
	<el-table :data="value || []" border>
		<el-table-column
			v-if="column.attrs?.showIndex"
			type="index"
			label="序号"
			width="60"
			align="center"
		/>
		<el-table-column
			v-for="col in column.attrs?.columns"
			:key="col.prop"
			:prop="col.prop"
			:label="col.label"
		>
			<template #default="scope">
				<component
					:is="getTypeComponent(col.type)"
					:value="scope.row[col.prop]"
					:column="col"
				/>
			</template>
		</el-table-column>
	</el-table>
</template>

<style scoped>
.el-table {
	width: 100%;
}
</style>
