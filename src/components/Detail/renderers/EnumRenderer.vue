<script setup lang="ts">
import { computed, ref } from 'vue';
import { useDict } from '@/hooks/dict';
import { useApiOption } from '@/views/preview/hooks/useOption';

const { getApiOptions, apiDataMap, getOptions, casProps, } = useApiOption();
interface Option {
	label: string;
	value: string | number;
}

interface Props {
	value: string | number | Array<string | number>;
	column: {
		attrs?: {
			options?: Option[];
			dataSourceType?: 'custom' | 'dict' | 'api';
			dictType?: string;
			apiId?: string;
		};
	};
}

const props = defineProps<Props>();

// 获取字典仓库
const dictOptions = computed(() => {
	const dictType = props.column.attrs?.dictType;
	if (!dictType) return [];
	const value = useDict(dictType)?.[dictType]?.value;
	return value || [];
});

// 合并所有来源的选项
const options = computed(() => {
	// 如果是字典类型
	if (props.column.attrs?.dictType) {
		return dictOptions.value;
	}
	// 如果是API数据源
	if (props.column.attrs?.dataSourceType === 'api') {
		return getOptions.value({
      type: 'select',
      options: props.column.attrs
    });
	}
	// 如果是自定义选项
	return props.column.attrs?.options || [];
});

function getDisplayValue(value: string | number) {
  if (typeof value === 'string') {
    const config = apiDataMap.value[props.column.attrs?.apiId] || {};
		const { labelField, valueField, childrenField } = config;
    const path = value.split(',');
    if (path.length > 1) {
      let currentOptions = options.value;
      const labels = [];
    
      // 遍历路径找到对应的标签
      for (const val of path) {
        const option = currentOptions.find((opt) => opt[valueField || 'value'] == val);
        if (option) {
          labels.push(option[labelField || 'label']);
          currentOptions = option[childrenField || 'children'] || [];
        }
      }
      return labels.join(' / ');
    } else {
      const findPath = (options, targetValue, path = []) => {
        for (const option of options) {
          // 如果找到目标值，返回完整路径
          if (option[valueField || 'value'] == targetValue) {
            return [...path, option[labelField || 'label']];
          }
          // 如果有子节点，递归查找
          if (option[childrenField || 'children'] && option[childrenField || 'children'].length > 0) {
            const result = findPath(option[childrenField || 'children'], targetValue, [...path, option[labelField || 'label']]);
            if (result) return result;
          }
        }
        return null;
      };

      // 查找完整路径
      const path = findPath(options.value, value);
      return path ? path.join(' / ') : '--';
    }
  }
	const option = options.value.find((opt: Option) => opt.value == value);
	return option?.label || (value || value === 0 ? value : '--');
}
</script>

<template>
	<span>
		<template v-if="Array.isArray(value)">
			{{ value.map(v => getDisplayValue(v)).join(',') || '--' }}
		</template>
		<template v-else>
			{{ getDisplayValue(value) }}
		</template>
	</span>
</template>
