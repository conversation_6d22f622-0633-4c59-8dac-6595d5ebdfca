<script setup lang="ts">
interface Props {
  value: number | [number, number];
  column: {
    prop: string;
    label: string;
    type: string;
    attrs?: {
      min?: number;
      max?: number;
      step?: number;
      showInput?: boolean;
      range?: boolean;
      showStops?: boolean;
      formatTooltip?: (val: number) => string;
    };
  };
}

const props = defineProps<Props>();

// 格式化显示值
function formatValue(value: number | [number, number]): string {
  if (Array.isArray(value)) {
    return value.join(' - ');
  }
  if (props.column.attrs?.formatTooltip) {
    return props.column.attrs.formatTooltip(value);
  }
  return String(value);
}
</script>

<template>
  <div class="slider-wrapper">
    <div class="slider-container">
    <el-slider
      :model-value="value"
      disabled
      :min="column.attrs?.min"
      :max="column.attrs?.max"
      :step="column.attrs?.step"
      :show-input="column.attrs?.showInput"
      :range="column.attrs?.range"
      :show-stops="column.attrs?.showStops"
      :format-tooltip="column.attrs?.formatTooltip"
    />
    <span class="slider-value">{{ formatValue(value) }}</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.slider-wrapper {
  display: inline-block;
  width: 200px;
  .slider-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding-right: 1rem;

    :deep(.el-slider) {
      flex: 1;
      margin-right: 0;

      .el-slider__runway {
        margin: 0;
      }

      &.is-vertical {
        margin-right: 1rem;
      }
    }

    .slider-value {
      text-align: right;
      color: var(--el-text-color-secondary);
      font-size: var(--el-font-size-small);
    }
  } 
}
</style> 