<script setup lang="ts">
defineProps<{
	value: Array<{ url: string; name: string }>;
}>();

function getFileUrl(file: { url: string; name: string }) {
	return file.url;
	// const url = file.url;
	// return url.startsWith('http') ? url : `${import.meta.env.VITE_SAAS_PROXY_PATH.slice(0, -1)}${url}`;
}
</script>

<template>
	<div class="file-row-grid">
		<el-link
			v-for="(file, index) in value"
			:key="index"
			class="file-item"
			icon="Document"
			:href="getFileUrl(file)"
			target="_blank"
			type="primary"
		>
			<span class="ml-1">{{ file.name }}</span>
		</el-link>
	</div>
</template>

<style scoped>
.file-row-grid {
	display: inline-grid;
	grid-template-columns: 1fr;
	gap: 8px;
	justify-content: flex-start;
}
.file-item {
	display: flex;
	justify-content: start !important;
	align-items: center !important;
}
</style>
