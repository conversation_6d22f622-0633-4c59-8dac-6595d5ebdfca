<script setup lang="ts">
import dayjs from 'dayjs';

interface Props {
	value: string | number | Date | Array<string | number | Date>;
	column: {
		attrs?: {
			format?: string;
			range?: boolean;
		};
	};
}

const props = defineProps<Props>();

function formatDateTime(value: string | number | Date): string {
	if (!value) return '--';
	
	const format = props.column.attrs?.format || 'YYYY-MM-DD HH:mm:ss';
	
	// 如果是数字或Date类型，使用dayjs转换
	if (typeof value === 'number' || value instanceof Date) {
		return dayjs(value).format(format);
	}
	
	// 字符串类型直接返回
	return value;
}

function formatDateTimeRange(value: Array<string | number | Date>): string {
	if (!Array.isArray(value) || value.length !== 2) return '--';
	
	return `${formatDateTime(value[0])} - ${formatDateTime(value[1])}`;
}
</script>

<template>
	<span>
		<template v-if="column.attrs?.range">
			{{ formatDateTimeRange(value as Array<string | number | Date>) }}
		</template>
		<template v-else>
			{{ formatDateTime(value as string | number | Date) }}
		</template>
	</span>
</template>
