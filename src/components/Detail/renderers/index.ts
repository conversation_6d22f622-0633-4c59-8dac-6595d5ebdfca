import { Component } from 'vue';
import ImageRenderer from './ImageRenderer.vue';
import FileRenderer from './FileRenderer.vue';
import EnumRenderer from './EnumRenderer.vue';
import TagRenderer from './TagRenderer.vue';
import DateTimeRenderer from './DateTimeRenderer.vue';
import MoneyRenderer from './MoneyRenderer.vue';
import PercentRenderer from './PercentRenderer.vue';
import EditorRenderer from './EditorRenderer.vue';
import SwitchRenderer from './SwitchRenderer.vue';
import TableRenderer from './TableRenderer.vue';
import TextRenderer from './TextRenderer.vue';
import SliderRenderer from './SliderRenderer.vue';

export const rendererMap: Record<string, Component> = {
  images: ImageRenderer,
  files: FileRenderer,
  enums: EnumRenderer,
  tag: TagRenderer,
  datetime: DateTimeRenderer,
  money: MoneyRenderer,
  percent: Percent<PERSON><PERSON><PERSON>,
  editor: Editor<PERSON><PERSON><PERSON>,
  switch: SwitchRenderer,
  table: TableRenderer,
  text: TextRenderer,
  slider: SliderRenderer,
  'date-range': DateTimeRenderer,
};

export type RendererType = keyof typeof rendererMap;

export {
  ImageRenderer,
  FileRenderer,
  EnumRenderer,
  TagRenderer,
  DateTimeRenderer,
  MoneyRenderer,
  PercentRenderer,
  EditorRenderer,
  SwitchRenderer,
  TableRenderer,
  TextRenderer,
  SliderRenderer,
}; 