<script setup lang="ts">
import { ElImage } from 'element-plus';

defineProps<{
	value: Array<any>;
}>();

function getResourceUrl(resource: string | { url: string }) {
	const url = typeof resource === 'string' ? resource : resource.url;
	return url;
	// return url.startsWith('http') ? url : `${import.meta.env.VITE_SAAS_PROXY_PATH.slice(0, -1)}${url}`;
}
function getImagePreviewList(images: Array<string | { url: string; name?: string }>) {
	return images.map((img) => getResourceUrl(img));
}
</script>

<template>
	<div class="image-row-grid">
		<ElImage
			v-for="(img, index) in value"
			:key="index"
			:src="getResourceUrl(img)"
			:preview-src-list="getImagePreviewList(value)"
			fit="cover"
			preview-teleported
			class="image-item"
			:z-index="9999"
		/>
	</div>
</template>

<style scoped>
.image-row-grid {
	display: inline-grid;
	grid-template-columns: repeat(3, 100px);
	gap: 8px;
	justify-content: flex-start;
}
.image-item {
	width: 100px;
	height: 100px;
	object-fit: cover;
	border-radius: 6px;
	transition: all 0.2s;
}
.image-item:hover {
	transform: scale(1.05);
}
</style>
