<template>
	<div>
		<div
			v-for="group in groups"
			:key="group.title"
			class="detail-group"
		>
			<div
				v-if="group.title"
				class="detail-group__title"
			>
				{{ group.title }}
			</div>
			<el-descriptions
				:column="3"
				:label-position="labelPosition"
				:direction="direction"
				:size="size"
				:border="false"
			>
				<el-descriptions-item
					v-for="column in group.columns"
					:key="column.prop"
					:label="column.label"
					:span="column.span || 1"
					:label-class-name="['table'].includes(column.type) ? 'hidden' : ''"
				>
					<component
						:is="getTypeComponent(column.type)"
						:value="getColumnValue(column)"
						:column="column"
					/>
				</el-descriptions-item>
			</el-descriptions>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { rendererMap, type RendererType } from './renderers';

interface Props {
	groups: any[];
	data: any;
}

const props = defineProps<Props>();

const labelPosition = ref<'top' | 'left'>('left');
const direction = ref<'horizontal' | 'vertical'>('horizontal');
const size = ref<'default' | 'small' | 'large'>('default');

function getTypeComponent(type: string) {
	return rendererMap[type as RendererType] || rendererMap.text;
}

/**
 * 根据列的类型获取对应的数据值
 * @param column 列配置
 * @returns 对应的数据值
 */
function getColumnValue(column: any) {
  const { prop } = column;
	if(column.formatter){
		return column.formatter(props.data);
	}
  // 子表添加List
  return props.data[prop] || props.data[prop + 'List'];
}
</script>

<style scoped lang="scss">
.detail-group {
	&__title {
		margin-bottom: 16px;
		padding-left: 8px;
		font-size: 16px;
		font-weight: 500;
		border-left: 4px solid var(--el-color-primary);
	}
}

.el-image {
	border-radius: 4px;
	overflow: hidden;
	border: 1px solid var(--el-border-color-light);

	&:hover {
		border-color: var(--el-color-primary);
	}
}

.image-error {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 100%;
	height: 100%;
	background-color: var(--el-fill-color-light);
	color: var(--el-text-color-secondary);
}

.file-list {
	display: flex;
	flex-direction: column;
	gap: 8px;
}

.file-item {
	display: flex;
	align-items: center;
}

.file-link {
	display: flex;
	align-items: center;

	&:hover {
		color: var(--el-color-primary);
	}
}

:deep(.el-descriptions__cell) {
	padding: 12px 16px;

	// 目前文字太多会挤压，所以设置为自适应
	width: auto;
	word-break: break-word;
	vertical-align: top;
}

:deep(.el-descriptions__table) {
	table-layout: fixed;
	.el-descriptions__cell {
		padding: 0;
		.el-descriptions__label.hidden {
			display: none;
		}
	}
}

:deep(.el-tag) {
	margin-right: 8px;
}

:deep(.el-table) {
	--el-table-border-color: var(--el-border-color-lighter);
	--el-table-header-bg-color: var(--el-fill-color-light);
}

:root {
	--detail-image-size: 5rem;
	--detail-gap: 0.5rem;
	--detail-border-color: var(--el-border-color);

	&.dark {
		--detail-border-color: var(--el-border-color-darker);
	}
}

.image-grid {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(var(--detail-image-size), 1fr));
	gap: var(--detail-gap);
}

.image-item {
	aspect-ratio: 1;
	border-radius: var(--el-border-radius-base);
	transition: all var(--el-transition-duration);

	&:hover {
		transform: scale(1.05);
	}
}

:deep(.el-descriptions__label) {
	padding-top: 0 !important;
	vertical-align: top !important;
}
</style>
