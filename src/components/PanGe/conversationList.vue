<template>
	<div
		:class="[
			'amn',
			{
				hid: !show,
			},
		]"
	>
		<aside class="aside">
			<div style="position: sticky; top: 0">
				<slot name="title"></slot>
			</div>
			<div class="group">
				<div
					v-for="group in groupList"
					:key="group.name"
				>
					<h3 class="name">{{ group.name }}</h3>
					<ul>
						<li
							v-for="item in group.list"
							:key="item.id"
							:class="[
								'list-item',
								{
									active: item.id === props.modelValue,
								},
							]"
							@click="handleSelectItem(item)"
						>
							{{ item.name }}
						</li>
					</ul>
				</div>
			</div>
		</aside>
	</div>
</template>
<script setup lang="ts">
import { computed, withDefaults } from 'vue';
import dayjs from 'dayjs';
import { List } from './conversationList';

const props = withDefaults(
	defineProps<{
		modelValue: string;
		list: List[];
		show: boolean;
	}>(),
	{}
);
const emits = defineEmits(['update:modelValue', 'change']);

const listEnum = {
	today: '今天',
	yesterday: '昨天',
	week: '7天内',
	month: '30天内',
};
const groupList = computed(() => {
	const group = (props.list || []).reduce(
		(prev, curr) => {
			const time = (curr.updated_at || curr.created_at) * 1000;
			if (dayjs().isSame(time, 'day')) {
				prev.today.push(curr);
			} else if (dayjs().subtract(1, 'days').isSameOrBefore(time, 'day')) {
				prev.yesterday.push(curr);
			} else if (dayjs().subtract(7, 'days').isSameOrBefore(time, 'day')) {
				prev.week.push(curr);
			} else if (dayjs().subtract(30, 'days').isSameOrBefore(time, 'day')) {
				prev.month.push(curr);
			}
			return prev;
		},
		{
			today: [] as List[],
			yesterday: [] as List[],
			week: [] as List[],
			month: [] as List[],
		}
	);
	return Object.entries(group)
		.filter(([, list]) => list.length)
		.map(([key, list]) => {
			return {
				name: listEnum[key as keyof typeof listEnum],
				list,
			};
		});
});
function handleSelectItem(item: List) {
	if (item.id !== props.modelValue) {
		emits('update:modelValue', item.id);
		emits('change', item.id);
	}
}
</script>
<style lang="scss" scoped>
.amn {
	width: 260px;
	transition: width 0.5s;
	overflow-y: auto;
	overflow-x: hidden;
	&.hid {
		width: 0px;
	}
	&::-webkit-scrollbar {
		display: none;
	}
	-ms-overflow-style: none;
	scrollbar-width: none;
}
.aside {
	padding: 0 24px;
	width: 260px;
}
.group {
	.name {
		color: #0d0d0d;
		font-weight: 600;
		padding: 12px 8px 8px;
	}
	.list-item {
		padding: 6px 16px;
		cursor: pointer;
		&.active,
		&:hover {
			background: var(--el-color-primary-light-9);
			color: var(--el-color-primary);
			transition: 0.2s ease;
		}
	}
}
</style>
