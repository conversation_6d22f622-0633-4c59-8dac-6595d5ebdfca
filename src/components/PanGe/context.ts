import { ref, computed } from 'vue';
import dayjs from 'dayjs';
import { marked } from 'marked';
import { ElMessage } from 'yun-design';
import pinia from '/@/stores/index';
import { useAiChat } from '/@/stores/aiChat';
import { useUserInfo } from '/@/stores/userInfo';
import { fetchChatMessages, setMessages, Rating, chatMessagesApiData, File } from '/@/api/admin/chart';

const stores = useUserInfo(pinia);
const storesAI = useAiChat(pinia);

export type Answer = {
	flag: 'answer';
	status: 'loading' | 'running' | 'succeeded' | 'error';
	rating?: Rating;
	content: string;
	messageId?: string;
	popFlag?: boolean;
};
type Item =
	| {
			flag: 'question';
			content: string;
			files?: File[];
	  }
	| Answer;
const cid = ref(sessionStorage.getItem('conversationId'));
const conversationId = computed({
	get: () => {
		return cid.value || undefined;
	},
	set: (id?: string) => {
		cid.value = id || null;
		if (id) {
			sessionStorage.setItem('conversationId', id);
		} else {
			sessionStorage.removeItem('conversationId');
		}
	},
});
function markdownToJson(html: string) {
	const parser = new DOMParser();
	const doc = parser.parseFromString(html, 'text/html');
	// 从doc中解析数据表名
	const tableTitle = doc.querySelector('.markdown-body.answer')?.textContent?.trim();
	const tableName = tableTitle?.match(/数据表名：(.*)\n/)?.[1] || '';
	if (tableName) {
		storesAI.updateAiDataFormTableName(tableName);
	}
	// 从doc中解析表格数据
	const headers = Array.from(doc.querySelectorAll('table thead th')).map((th) => th?.textContent?.trim() || '');
	const rows = Array.from(doc.querySelectorAll('table tbody tr')).map((tr) => {
		const cells = Array.from(tr.querySelectorAll('td')).map((td) => td?.textContent?.trim() || '');
		return cells;
	});

	// 将表格数据转换为 JSON 格式
	const jsonData = rows.map((row) => {
		const obj: Record<string, string> = {};
		row.forEach((cell, index) => {
			obj[headers[index]] = cell;
		});
		return obj;
	});
	if (jsonData.length) {
		storesAI.updateAiDataFormResult(jsonData);
	}
}
const chatList = ref<Item[]>([]);

export default () => {
	
	const { userInfos } = storeToRefs(stores);
	// if (!storesAI.aiDataForm.chatId) {
		storesAI.setChatId(`${userInfos.value.user?.userId}_${Date.now()}`);
	// }

	function toJSON(eventData: string) {
		const jsonData = JSON.parse(eventData);
		if (jsonData.event === 'message') {
			chatList.value[chatList.value.length - 1] = {
				flag: 'answer',
				status: 'running',
				content: chatList.value[chatList.value.length - 1].content + (jsonData.answer || ''),
				messageId: jsonData.message_id,
				time: dayjs().format('M月D日 HH:mm'),
			};
		}
		if (!conversationId.value) {
			conversationId.value = jsonData.conversation_id;
		}
	}
	function parseEventStream(text: string) {
		// SSE 事件格式：event: xxx\ndata: xxx\n\n
		const events = text.split('\n\n');

		events.forEach((line) => {
			let eventData = '';
			if (line.startsWith('data:')) {
				eventData = line.replace('data: ', '').trim();
			}
			if (eventData) {
				try {
					toJSON(eventData);
				} catch (error) {}
			}
		});
	}
	let controllerFetch: () => void;
	function handleController() {
		controllerFetch && controllerFetch();
	}
	async function sendRequest(data: chatMessagesApiData, cb: (type: 'running' | 'success' | 'error') => void) {
		const { response, error } = await fetchChatMessages(data, (abort) => {
			controllerFetch = abort;
		});
		if (error) {
			(chatList.value[chatList.value.length - 1] as Answer).status = 'error';
			chatList.value[chatList.value.length - 1].content = error;
			cb('error');
			return;
		}
		const reader = response!.body!.getReader();
		const decoder = new TextDecoder();

		while (true) {
			try {
				const { value, done } = await reader.read();
				if (done) {
					(chatList.value[chatList.value.length - 1] as Answer).status = 'succeeded';
					markdownToJson(marked(chatList.value[chatList.value.length - 1].content, { async: false }));
					cb('success');
					break;
				}
				const chunk = decoder.decode(value, { stream: true });
				parseEventStream(chunk);
				markdownToJson(marked(chatList.value[chatList.value.length - 1].content, { async: false }));
				cb('running');
			} catch (error: any) {
				(chatList.value[chatList.value.length - 1] as Answer).status = 'succeeded';
				if (error.name === 'AbortError') {
					if (!(chatList.value[chatList.value.length - 1] as Answer).content) {
						(chatList.value[chatList.value.length - 1] as Answer).content = '已停止响应';
					}
					cb('success');
				}

				break;
			}
		}
	}

	async function onThumbs(rating: Rating, item: Answer) {
		if (!item.messageId) {
			throw new Error('ID 为空');
		}
		await setMessages(item.messageId, {
			rating,
			user: userInfos.value.user?.userId,
		});
		item.rating = rating;
	}
	async function handleThumbsUp(item: Answer) {
		const rating = item.rating === 'like' ? null : 'like';
		await onThumbs(rating, item);
		if (rating === 'like') {
			ElMessage.success('感谢你的支持');
		} else {
			ElMessage.success('已取消反馈');
		}
	}
	async function handleThumbsDown(item: Answer) {
		const rating = item.rating === 'dislike' ? null : 'dislike';
		await onThumbs(rating, item);
		if (rating !== 'dislike') {
			ElMessage.success('已取消反馈');
		}
	}
	return {
		conversationId,
		chatList,
		sendRequest,
		handleThumbsUp,
		handleThumbsDown,
		handleController,
	};
};
