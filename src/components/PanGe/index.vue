<template>
	<yun-chat
		ref="chatRef"
		v-model="request"
		v-model:files="files"
		:user="user"
		:list="list"
		:loading="loading"
		:share-url="shareUrl"
		@share="shareConfirm"
		@uploadImage="handleUploadImage"
		@sendRequest="handleSendRequest"
		@refresh="handleRefresh"
		@thumbsUp="handleThumbsUp"
		@thumbsDown="handleThumbsDown"
		@feedback="handleFeedback"
		@reset="handleReset"
	>
		<template #empty>
			<slot name="empty" />
		</template>
		<!-- <template #yuyin>
			<YuYin
				:disabled="loading"
				@start="request = ''"
				@change="handleChangeVoice"
			/>
		</template> -->
	</yun-chat>
</template>

<script setup lang="ts" name="yunChat">
import { ref, watch, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'yun-design';
import { storeToRefs } from 'pinia';
import { Session } from '/@/utils/storage';
import { getMessages, chatMessagesApiData, sysFileUpload, dialogueRecordsSave } from '/@/api/admin/chart';
import { useUserInfo } from '/@/stores/userInfo';
import { useAiChat } from '/@/stores/aiChat';
import YunChat from './components/index.vue';
import useContext, { Answer } from './context';
import { getList } from './conversationList';

const props = defineProps({
	isAside: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['reset']);

const chatRef = ref();
const storesAI = useAiChat();
const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);
const files = ref([]);
const loading = ref(false);

const { chatList: list, sendRequest, conversationId, handleThumbsUp, handleThumbsDown, handleController } = useContext();
const request = ref('');

function focus() {
	chatRef.value?.focus();
}

function scrollbarBt() {
	chatRef.value?.scrollbarBt();
}

function fetcList(data) {
	loading.value = true;
	sendRequest(data, (type) => {
		if (['success', 'error'].includes(type)) {
			loading.value = false;
			focus();
			if (type === 'success') {
				getList();
			}
			nextTick(() => {
				scrollbarBt();
			});
		}
		if (type === 'running') {
			scrollbarBt();
		}
	});
}

function handleSendRequest() {
	const tenantId = Session.getTenant();
	const data: chatMessagesApiData = {
		inputs: {
			modelName: 'DeepSeek_R1',
		},
		response_mode: 'streaming',
		user: userInfos.value.user?.userId,
		conversation_id: conversationId.value || undefined,
		query: request.value,
	};
	fetcList(data);
}

function handleRefresh(item) {
	const tenantId = Session.getTenant();
	const data: chatMessagesApiData = {
		inputs: {
			modelName: 'DeepSeek_R1',
		},
		response_mode: 'streaming',
		user: userInfos.value.user?.userId,
		conversation_id: conversationId.value || undefined,
		query: item.content,
	};
	fetcList(data);
}

// 分享
const shareUrl = ref('');
const user = computed(() => {
	return {
		username: userInfos.value.user?.username || '',
		// avatar: userInfos.value.user?.avatar
		// 	? `${import.meta.env.VITE_ADMIN_PROXY_PATH}${import.meta.env.VITE_API_URL.substring(1)}${userInfos.value.user?.avatar}`
		// 	: '',
		avatar: '',
	};
});
async function shareConfirm(data) {
	try {
		shareUrl.value = '';
		const arr = data.map((item) => {
			const { _share, ...rest } = item;
			return {
				...rest,
			};
		});
		const res = await dialogueRecordsSave({
			userId: userInfos.value.user?.userId,
			dialogueContent: JSON.stringify({
				user: user.value,
				content: arr,
			}),
			dialogueType: 'SHARE',
		});
		shareUrl.value = `${location.origin}/#/share/${res.data}`;
	} catch (err) {
		Promise.reject('分享失败');
	}
}

async function handleFeedback(data, content) {
	try {
		await dialogueRecordsSave({
			...data,
			dialogueContent: JSON.stringify(content),
			dialogueType: 'THUMBS_DOWN',
		});
		ElMessage.success('反馈功能');
	} catch (err) {
		Promise.reject('反馈失败');
	}
}

function handleReset() {
	conversationId.value = '';
}

async function histort() {
	if (conversationId.value) {
		const { data } =
			(await getMessages({
				user: userInfos.value.user?.userId,
				conversation_id: conversationId.value,
			})) || {};
		const chatId = data?.data?.[0]?.inputs?.chart_id;
		if (chatId) {
			storesAI.setChatId(`${chatId}`);
		}
		list.value = (data?.data || []).flatMap((item: any) => {
			console.log('answer', item.answer);
			return [
				{
					flag: 'question',
					content: item.query,
					files: item.message_files,
				},
				{
					flag: 'answer',
					status: 'succeeded',
					content: item.answer,
					rating: item.feedback?.rating || null,
					messageId: item.id,
				},
			];
		});
	} else {
		list.value = [];
	}
	scrollbarBt();
}

async function handleUploadImage(formData) {
	const { data } = await sysFileUpload(formData);
	console.log('data', data);
	// files.value.push({
	// 	type: 'image',
	// 	transfer_method: 'remote_url',
	// 	url: data.url,
	// });
}

async function handleChangeVoice(text) {
	console.log('handleChangeVoice', text);
	request.value = text;
}

const route = useRoute();
watch(
	() => route.path, // 监听路由路径变化
	(newPath, old) => {
		const collapse = old === '/home';
		if ('/home' === newPath || '/home' === old) {
			storesAI.toggleCollapse(collapse);
			// nextTick(() => {
			// 	histort();
			// 	request.value = '';
			// });
		}
		// if (!list.value.length) {
		// 	nextTick(() => {
		// 		histort();
		// 	});
		// }
		scrollbarBt();
	},
	{
		immediate: true,
	}
);

defineExpose({
	histort,
	focus,
});
</script>
<style lang="scss" scoped>
:deep(.el-textarea__inner) {
	border: none;
}
</style>
