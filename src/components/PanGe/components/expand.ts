import { Ren<PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'marked';
import hljs from 'highlight.js';
import ClipboardJS from 'clipboard';
import { ElMessage } from 'yun-design';

const copyInnerHTML = `
  <svg aria-hidden="true" focusable="false" role="img" class="octicon octicon-copy" viewBox="0 0 16 16" width="12" height="12" fill="currentColor" style="display: inline-block; user-select: none; vertical-align: text-bottom; overflow: visible;"><path d="M0 6.75C0 5.784.784 5 1.75 5h1.5a.75.75 0 0 1 0 1.5h-1.5a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-1.5a.75.75 0 0 1 1.5 0v1.5A1.75 1.75 0 0 1 9.25 16h-7.5A1.75 1.75 0 0 1 0 14.25Z"></path><path d="M5 1.75C5 .784 5.784 0 6.75 0h7.5C15.216 0 16 .784 16 1.75v7.5A1.75 1.75 0 0 1 14.25 11h-7.5A1.75 1.75 0 0 1 5 9.25Zm1.75-.25a.25.25 0 0 0-.25.25v7.5c0 .*************.25h7.5a.25.25 0 0 0 .25-.25v-7.5a.25.25 0 0 0-.25-.25Z"></path></svg>
`;

const customRenderer = new Renderer();
customRenderer.code = function ({ text, lang }: Tokens.Code) {
	const lan = lang ? lang : 'plaintext';
	const highlighted = hljs.highlightAuto(text).value;
	// 生成代码块HTML + 复制按钮
	return `
		<div style="position: relative;">
			<span class="markdown-code-copy" 
			data-clipboard-text="${text}" style="position: absolute;right: 1rem;cursor: pointer;">
            	${copyInnerHTML}
          	</span>
	  		<pre><code style="padding: 1rem" class="language-${lan}">${highlighted}</code></pre>
	    </div>
	`;
};
const clipboard = new ClipboardJS('.markdown-code-copy');

clipboard.on('success', () => {
	ElMessage.success('复制成功！');
});

export default {
	renderer: customRenderer,
};
