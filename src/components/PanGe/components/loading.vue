<script setup lang="tsx">
const props = defineProps({
	// 显示加载动画
	size: {
		type: Number,
		default: 40,
	},
});
</script>
<template>
	<div class="loader" :style="{ width: `${props.size}px` }"></div>
</template>
<style scoped lang="scss">
.loader {
	width: 60px;
	aspect-ratio: 2;
	--_g: no-repeat radial-gradient(circle closest-side, #000 90%, #0000);
	background: var(--_g) 0% 50%, var(--_g) 50% 50%, var(--_g) 100% 50%;
	background-size: calc(100% / 3) 50%;
	animation: l3 1s infinite linear;
}
@keyframes l3 {
	20% {
		background-position: 0% 0%, 50% 50%, 100% 50%;
	}
	40% {
		background-position: 0% 100%, 50% 0%, 100% 50%;
	}
	60% {
		background-position: 0% 50%, 50% 100%, 100% 0%;
	}
	80% {
		background-position: 0% 50%, 50% 50%, 100% 100%;
	}
}
</style>
