<template>
	<yun-dialog v-model="visible2" :title="'分享对话'" size="large">
		<Chat :data="list" :user="user" :is-share="true"></Chat>
		<template #footer>
			<el-input v-model="url" disabled>
				<template #append>
					<el-button type="primary" class="primary" @click="handleCopy">
						<el-icon><Link /></el-icon>{{ text }}
					</el-button>
				</template>
			</el-input>
		</template>
	</yun-dialog>
</template>
<script setup lang="ts">
import { ref, computed, toRefs, inject, watch } from 'vue';
import { Link } from '@element-plus/icons-vue';
import commonFunction from './utils/commonFunction';
import Chat from './presentation.vue';
import pangeD from './assets/pange-def.gif';
import pangeE from './assets/pange-error.gif';

const props = defineProps({
	visible: {
		type: Boolean,
		default: false,
	},
	shareUrl: {
		type: String,
		default: '',
	},
	data: {
		type: Object,
		default: () => [],
	},
});

const emit = defineEmits(['update:visible']);

const visible2 = computed({
	get: () => {
		return props.visible;
	},
	set: (value) => {
		emit('update:visible', value);
	},
});

const { data: list, shareUrl: url } = toRefs(props);

const user = inject('user');

const text = ref('复制链接');
const { copyText } = commonFunction();
async function handleCopy() {
	if (!url.value) {
		return;
	}
	await copyText(url.value);
	text.value = '复制成功';
}

watch(visible2, (v) => {
	if (v) {
		url.value = '';
		text.value = '复制链接';
	}
});
</script>
<style lang="scss" scoped>
.primary {
	color: var(--el-color-white) !important;
	background-color: var(--el-color-primary) !important;
}
</style>
