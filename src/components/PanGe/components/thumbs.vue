<template>
	<i class="icon" :style="`transform: rotate(${rotate}deg);`">
		<svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="1em" height="1em">
			<path
				d="M84.924461 441.197617a42.664889 42.664889 0 0 0-42.451564-43.006208c-23.423024 0-42.4089 19.412524-42.4089 43.006208v539.710845C0.021332 1004.758135 18.921878 1023.957335 42.686221 1023.957335c23.551019 0 42.664889-19.412524 42.622224-43.006208l-0.426648-539.710845zM256.863964 938.627557h569.021624c6.911712 0 18.132578-9.087621 19.497854-15.786009l90.065581-431.683346c8.106329-38.910379-12.330153-63.997333-52.435149-63.912004h-20.137827c-19.28453 0.08533-38.825049 0.08533-57.170951 0.08533-30.846715 0-54.184409-0.127995-67.410525-0.298654l-124.240157-1.877255 46.33407-115.28053c14.378068-35.795842 22.484396-73.170285 22.484396-110.075414 0-80.423316-33.875922-114.512562-70.055747-114.085913-27.689513 0.341319-43.944836 15.914004-43.731512 58.664222-4.778468 171.086205-131.919837 323.570518-312.221657 338.4179V938.627557zM739.574518 341.703096l90.662889 0.298654c28.7988 0 52.520478-0.08533 52.520478-0.08533 94.460064-0.127995 155.42819 74.748885 136.228991 166.691721l-90.022916 431.683347C919.236365 986.497563 873.20095 1023.957335 825.842923 1023.957335H214.497729a43.091538 43.091538 0 0 1-42.963543-42.963543V441.581601c0-23.721678 19.11387-42.963543 42.750219-42.963543h13.652764c160.889296 0 252.576143-137.423607 255.861339-256.629308-0.597308-97.531936 64.637307-140.879463 127.994667-141.647431 63.400025-0.767968 156.409483 50.771218 156.409483 199.415691 0 19.625849-1.706596 38.611725-4.693138 56.744302l-23.935002 85.159119z"
				fill="currentColor"
			></path>
		</svg>
	</i>
</template>
<script setup lang="tsx">
defineProps({
	rotate: {
		type: Number,
		default: 0,
	},
});
</script>
<style scoped>
.icon {
	transform-origin: center;
}
</style>
