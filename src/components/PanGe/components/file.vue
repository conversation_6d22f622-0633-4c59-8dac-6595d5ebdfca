<script setup lang="tsx">
import { ElMessage } from 'yun-design';
import { FolderOpened } from '@element-plus/icons-vue';

const fileInputRef = ref<HTMLInputElement>();
const props = defineProps({
	disabled: {
		type: Boolean,
		default: false,
	},
});
const emits = defineEmits<{
	before: [];
	success: [];
	error: [];
	finally: [];
	change: [value: string];
}>();
const loading = ref(false);

async function uploadFile(event: Event) {
	const file = (event?.target as HTMLInputElement)?.files?.[0];
	if (file) {
		// 检查是否是图片
		const fileType = file.type.split('/')[0];
		if (fileType === 'image') {
			const formData = new FormData();
			formData.append('file', file);
			try {
				loading.value = true;
				emits('before');
				emits('change', formData);
			} finally {
				emits('finally');
				loading.value = false;
				fileInputRef.value!.value = '';
			}
		} else {
			alert('请上传图片文件');
		}
	}
}
function handleClick() {
	if (props.disabled) {
		return;
	}
	if (loading.value) {
		ElMessage.info('上传中，请稍后');
		return;
	}
	fileInputRef.value!.click();
}
</script>
<template>
	<div
		class="cursor-pointer"
		v-loading="loading"
	>
		<input
			class="input"
			type="file"
			ref="fileInputRef"
			@change="uploadFile"
			accept="image/png,image/jpeg,image/jpg"
		/>
		<el-icon
			:size="24"
			@click="handleClick"
			><FolderOpened
		/></el-icon>
	</div>
</template>
<style scoped lang="scss">
input {
	display: none;
}
</style>
