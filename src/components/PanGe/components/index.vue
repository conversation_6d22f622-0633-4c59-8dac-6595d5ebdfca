<template>
  <div class="aside">
    <div>
      <slot name="title"> </slot>
    </div>
    <div class="flex flex-column flex-grow-1 content" style="padding-top: 8px">
      <el-scrollbar ref="scrollbarRef" class="flex-auto">
        <div class="width" style="padding: 12px; box-sizing: border-box">
          <template v-if="!list.length">
            <slot name="empty">
              <div class="default">
                <el-image class="pange" :src="pange"></el-image>
                <p>我是您贴心私人小助理：小盘，聊的了业务，也干得了研发。</p>
                <p>我可以帮你查数据、看报表、做分析，也可以帮你写需求、撸代码。</p>
                <p>您有需求随时Call我，随叫随到！</p>
              </div>
            </slot>
          </template>
          <!-- <presentation-box
						ref="innerRef"
						:data="list"
						:avatar="user.avatar"
						@share="handleShare"
						@checked="shareChecked"
						@thumbsDown="handleThumbsDown"
						@refresh="handleRefresh"
						@thumbsUp="handleThumbsUp"
					></presentation-box> -->
        </div>
      </el-scrollbar>
      <div v-show="!showShare" class="width" style="padding: 12px">
        <div class="flex jc-center" v-if="list.length">
          <el-button type="text" v-show="!loading" @click="reset">重新开始会话</el-button>
          <el-button type="text" v-show="loading" @click="handleAbort"
            >停止响应</el-button
          >
        </div>
        <div class="composer">
          <div v-if="files.length" class="flex gap-8 padding-lr-14">
            <el-image
              v-for="(file, index) in files"
              :key="index"
              :src="file.url"
              :preview-src-list="files.map((file) => file.url)"
              style="width: 64px; height: 64px"
              fit="contain"
              :initial-index="index"
            />
          </div>
          <el-input
            type="textarea"
            ref="textareaRef"
            :disabled="loading"
            class="input"
            resize="none"
            autosize
            autofocus
            placeholder="给 小盘 发送消息"
            v-model="request"
            @keyup.enter.stop="handleSub"
            style="background: transparent"
          >
          </el-input>
          <div class="flex gap-8 padding-lr-14 jc-end align-c">
            <div class="flex gap-8 align-c">
              <slot name="yuyin"> </slot>
              <!-- <YuYin :disabled="loading" @start="request = ''" @change="handleChangeVoice" /> -->
              <!-- <FileUpload class="flex" @change="handleUploadImage" :disabled="loading" /> -->
            </div>
            <div class="line"></div>
            <div>
              <el-button
                @click="handleSub"
                type="primary"
                size="small"
                circle
                :disabled="!request || loading"
              >
                <el-icon> <ArrowRightBold /> </el-icon
              ></el-button>
            </div>
          </div>
        </div>
      </div>
      <div v-show="showShare" class="width" style="height: 136px; padding: 12px">
        <div class="flex jc-center">
          <el-button type="info" @click="cancelShare">取消</el-button>
          <el-button type="primary" @click="showShareDialog">分享</el-button>
        </div>
      </div>
    </div>
    <ShareDialog
      v-model:visible="shareVisible"
      :data="shareList"
      :shareUrl="shareUrl"
    ></ShareDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, toRefs, provide, nextTick } from 'vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { ElMessage } from 'yun-design';
import { ArrowRightBold } from '@element-plus/icons-vue';
import pange from './assets/pange.gif';
import { useChat } from './store/useChat';
import PresentationBox from './presentation.vue';
import FileUpload from './file.vue';
import YuYin from './yuyin.vue';
import ShareDialog from './share.vue';

const props = defineProps({
  user: {
    type: Object,
    default: () => ({}),
  },
  modelValue: {
    type: String,
    default: '',
  },
  list: {
    type: Array,
    default: () => [],
  },
  files: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
  shareUrl: {
    type: String,
    default: '',
  },
});

const { user, list, loading } = toRefs(props);

const emit = defineEmits([
  'update:modelValue',
  'update:files',
  'uploadImage',
  'changeVoice',
  'sendRequest',
  'refresh',
  'thumbsUp',
  'thumbsDown',
  'feedback',
  'reset',
  'abort',
  'share',
]);

const request = computed({
  get: () => {
    return props.modelValue;
  },
  set: (value) => {
    emit('update:modelValue', value);
  },
});
const files = computed({
  get: () => {
    return props.files || [];
  },
  set: (value) => {
    emit('update:files', value);
  },
});
provide('list', list);
provide('loading', loading);
provide('user', user);

function handleThumbsDown(item) {
  emit('thumbsDown', item);
}

function handleThumbsUp(item) {
  emit('thumbsUp', item);
}

function handleFeedback(data, content) {
  emit('feedback', data, content);
}

provide('handleFeedback', handleFeedback);

const showShare = ref(false);
provide('showShare', showShare);

const textareaRef = ref();
const innerRef = ref();

function focus() {
  textareaRef.value?.focus();
}
const scrollbarRef = ref();

function scrollbarBt() {
  nextTick(() => {
    const clientHeight = innerRef.value?.innerRef?.clientHeight || 0;
    if (scrollbarRef.value && clientHeight) {
      scrollbarRef.value.scrollTo(0, clientHeight);
    }
  });
}

function resetForm() {
  files.value = [];
  request.value = '';
  focus();
}

function reset() {
  list.value.splice(0, list.value.length);
  resetForm();
  emit('reset');
}

function handleUploadImage(formData) {
  emit('uploadImage', formData);
}

function handleChangeVoice(formData) {
  emit('changeVoice', formData);
}

function handleSub(e: any) {
  // if (!e.shiftKey && e.keyCode === 13) {
  list.value.push(
    {
      flag: 'question',
      content: request.value,
      files: cloneDeep(files.value),
      time: dayjs().format('M月D日 HH:mm'),
    },
    {
      flag: 'answer',
      status: 'loading',
      content: '',
    }
  );
  emit('sendRequest');
  scrollbarBt();
  resetForm();
  // }
}

function handleRefresh(index: number) {
  list.value.splice(index, 1);
  const [item] = list.value.slice(index - 1, index);
  list.value.push({
    flag: 'answer',
    status: 'loading',
    content: '',
  });
  emit('refresh', item);
}

function handleShare(item, index) {
  console.log('handleShare');
  showShare.value = true;
  list.value[index]._share = true;
  const prev = list.value[index - 1];
  if (prev && prev.flag === 'question') {
    prev._share = true;
  }
}

function shareChecked(value, item, index) {
  list.value[index]._share = value;
}

function cancelShare() {
  showShare.value = false;
  list.value.forEach((item) => {
    item._share = false;
  });
}

const shareVisible = ref(false);
const shareList = ref([]);
function showShareDialog() {
  const hasShare = list.value.some((item) => item._share);
  if (!hasShare) {
    ElMessage.warning('请选择分享内容');
    return;
  }
  shareList.value = list.value.filter((item) => item._share);
  emit('share', shareList.value);
  shareVisible.value = true;
}

function handleAbort() {
  emit('abort');
}

defineExpose({
  focus,
  scrollbarBt,
});
</script>
<style lang="scss" scoped>
.width {
  width: 100%;
  min-width: 320px;
  max-width: 896px;
  margin: 0 auto;
}

.align-c {
  align-items: center;
}

@each $gap in(4, 8) {
  .gap-#{$gap} {
    gap: #{$gap}px;
  }
}

.jc-center {
  justify-content: center;
}

.jc-end {
  justify-content: flex-end;
}

$flex-ai: (
  start: flex-start,
  end: flex-end,
  center: center,
  stretch: stretch,
);

@each $key, $value in $flex-ai {
  .ai-#{$key} {
    align-items: $value;
  }
}

.padding-lr-14 {
  padding-left: 14px;
  padding-right: 14px;
}

.padding-24 {
  padding: 0 24px;
}

.bg-fff {
  background-color: #fff;
}

.flex-column {
  flex-direction: column;
}

.flex-grow-1 {
  flex: 1;
}

.question {
  .action {
    display: none;
  }

  .message {
    padding: 0.625rem 1.25rem;
    max-width: 80%;
    background-color: rgba(232, 232, 232, 0.5);
    border-radius: 1.5rem;
  }

  &:hover .action {
    display: block;
  }
}

.content {
  overflow: hidden;
}

.aside {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .composer {
    background-color: #fff;
    border-radius: 24px;
    padding-bottom: 14px;
    border: 1px solid var(--el-border-color-light);
    box-shadow: 0 0 #0000, 0 0 #0000, 0 9px 9px 0px rgba(0, 0, 0, 0.01),
      0 2px 5px 0px rgba(0, 0, 0, 0.06);

    .input {
      width: 100%;
      height: min-content;
      background-color: unset;
      border: none;
      outline: none;

      :deep(textarea) {
        box-shadow: none;
        height: auto !important;
        background-color: unset;
      }
    }

    :deep(.el-textarea.is-disabled .el-textarea__inner) {
      background-color: unset;
    }
  }
}

.answer {
  --bgColor-muted: #ececec;
  background: unset;
}

.default {
  padding-top: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 12px;
  color: #999;

  .pange {
    width: 100px;
    margin-bottom: 24px;
  }
}

.small {
  z-index: 9999999;
  cursor: pointer;
  position: fixed;
  right: 0;
  top: 60%;
  width: 48px;
}

.line {
  width: 1px;
  height: 20px;
  background-color: #ccc;
}

.drag {
  position: absolute;
  top: 50%;
  left: 0px;
  transform: translateY(-50%);
  cursor: col-resize;
  width: 9px;
  height: 100px;
  background: linear-gradient(to right, #333 10%, transparent 60%) 0 / calc(100% / 3) 100%
    repeat;
}

.layout-navbars-breadcrumb-user {
  display: flex;
  align-items: center;
  justify-content: flex-end;

  &-link {
    height: 100%;
    display: flex;
    align-items: center;
    white-space: nowrap;

    &-photo {
      width: 36px;
      height: 36px;
      border-radius: 100%;
    }
  }

  &-icon {
    padding: 0 10px;
    cursor: pointer;
    color: var(--next-bg-topBarColor);
    height: 50px;
    line-height: 50px;
    display: flex;
    align-items: center;

    &:hover {
      background: var(--next-color-user-hover);

      i {
        display: inline-block;
        animation: logoAnimation 0.3s ease-in-out;
      }

      :deep(img) {
        animation: logoAnimation 0.3s ease-in-out;
      }
    }
  }

  :deep(.el-dropdown) {
    color: var(--next-bg-topBarColor);
  }

  :deep(.el-badge) {
    height: 40px;
    line-height: 40px;
    display: flex;
    align-items: center;
  }

  :deep(.el-badge__content.is-fixed) {
    top: 12px;
  }
}
</style>
<style lang="scss">
.markdown-code-copy {
  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

.el-message {
  z-index: 9999 !important;
}
</style>
