interface Rect {
	top: number;
	left: number;
	width: number;
	height: number;
}
type OnMousedown = (rect: Rect) => void;
type OnMouseup = () => void;
type OnMousemove = (x: number, y: number) => void;
interface DragOptions {
	onMousedown?: OnMousedown;
	onMouseup?: OnMouseup;
	onMousemove?: OnMousemove;
}
export default class Drag {
	protected node;
	protected rect;
	private top = 0;
	private left = 0;
	private options;
	private flag = false;
	constructor(node: Element, options?: DragOptions) {
		if (!node) {
			throw new Error('error');
		}
		this.node = node;
		this.options = options;
		this.rect = Drag.getRect(this.node);
		this.onMousedown = this.onMousedown.bind(this);
		this.onMouseMove = this.onMouseMove.bind(this);
		this.onMouseup = this.onMouseup.bind(this);
		this.bind();
	}
	static getBoundingClientRect(node: Element): Rect {
		return node.getBoundingClientRect();
	}
	static getRect(node: Element): Rect {
		const rect = Drag.getBoundingClientRect(node);
		return {
			top: rect.top + window.scrollY,
			left: rect.left + window.scrollX,
			width: rect.width,
			height: rect.height,
		};
	}
	static getView() {
		const width = document.documentElement.clientWidth;
		const height = document.documentElement.clientHeight;
		return {
			width,
			height,
		};
	}
	private onMousedown(event: Event) {
		this.flag = true;
		this.rect = Drag.getBoundingClientRect(this.node);
		const mouseEvent = event as MouseEvent;
		this.top = mouseEvent.clientY;
		this.left = mouseEvent.clientX;
		if (this.options?.onMousedown) {
			this.options.onMousedown(this.rect);
		}
	}
	private onMouseMove(event: MouseEvent) {
		if (!this.flag) return;
		const x = Math.floor(event.clientX);
		const y = Math.floor(event.clientY);
		const moveY = y - this.top;
		const moveX = x - this.left;
		this.top = y;
		this.left = x;
		if (this.options?.onMousemove) {
			this.options.onMousemove(moveX, moveY);
		}
	}
	private onMouseup() {
		if (!this.flag) return;
		this.flag = false;
		if (this.options?.onMouseup) {
			this.options.onMouseup();
		}
	}
	protected bind() {
		this.node.addEventListener('mousedown', this.onMousedown);
		window.addEventListener('mousemove', this.onMouseMove);
		window.addEventListener('mouseup', this.onMouseup);
	}
	protected remove() {
		this.node.removeEventListener('mousedown', this.onMousedown);
		window.removeEventListener('mousemove', this.onMouseMove);
		window.removeEventListener('mouseup', this.onMouseup);
	}
}

export type Style =
	| {
			position: 'relative';
	  }
	| {
			position: 'fixed';
			'z-index': number;
			top?: string;
			left?: string;
			bottom?: string;
			right?: string;
	  };
export class Position extends Drag {
	private range = 100;
	private parentRect;
	private parentNode;
	private style: Style;
	constructor(
		nodes: {
			parent: Element;
			current: Element;
		},
		options: {
			style?: Style;
			mousemove: (style: Style) => void;
			mousedown: (style: Style) => void;
			mouseup: (style: Style) => void;
		}
	) {
		super(nodes.current, {
			onMousedown: (obj) => {
				const style = this.handleMousedown(obj);
				if (options?.mousedown) {
					options.mousedown(style);
				}
			},
			onMouseup: () => {
				const style = this.handleMouseup();
				if (options?.mouseup) {
					options.mouseup(style);
				}
			},
			onMousemove: (x, y) => {
				const style = this.handleMousemove(x, y);
				if (options?.mousemove) {
					options.mousemove(style);
				}
			},
		});
		if (!nodes.parent) {
			throw new Error('error');
		}
		this.style = options.style || {
			position: 'relative',
		};
		this.parentNode = nodes.parent;
		this.parentRect = Drag.getRect(this.parentNode);
	}
	protected handleMousedown(obj: Rect): Style {
		return {
			position: 'fixed',
			top: `${obj.top}px`,
			left: `${obj.left}px`,
			'z-index': 9999999,
		};
	}
	protected handleMouseup(): Style {
		this.parentRect = Drag.getRect(this.parentNode);
		const rect = Drag.getBoundingClientRect(this.node);
		const view = Drag.getView();
		let style: Style = {
			position: 'relative',
		};
		if (
			!(
				rect.top < this.parentRect.top + this.parentRect.height + this.range &&
				rect.top + rect.height > this.parentRect.top - this.range &&
				rect.left < this.parentRect.left + this.parentRect.width + this.parentRect.width &&
				rect.left + rect.width > this.parentRect.left - this.range
			)
		) {
			const currentRect = Drag.getBoundingClientRect(this.node);
			if (currentRect.top < this.range) {
				const left = `${(currentRect.left / view.width) * 100}%`;
				style = {
					position: 'fixed',
					top: '0px',
					left: left,
					'z-index': 9999999,
				};
			} else if (currentRect.top + currentRect.height > view.height - this.range) {
				const left = `${(currentRect.left / view.width) * 100}%`;
				style = {
					position: 'fixed',
					bottom: '0px',
					left: left,
					'z-index': 9999999,
				};
			} else if (currentRect.left < this.range) {
				const top = `${(currentRect.top / view.height) * 100}%`;
				style = {
					position: 'fixed',
					top,
					left: '0px',
					'z-index': 9999999,
				};
			} else if (currentRect.left + currentRect.width > view.width - this.range) {
				const top = `${(currentRect.top / view.height) * 100}%`;
				style = {
					position: 'fixed',
					top,
					right: '0px',
					'z-index': 9999999,
				};
			} else {
				style = {
					...this.style,
				};
			}
		}
		return style;
	}
	protected handleMousemove(x: number, y: number): Style {
		this.rect = {
			...this.rect,
			top: this.rect.top + y,
			left: this.rect.left + x,
		};
		return {
			position: 'fixed',
			left: `${this.rect.left}px`,
			top: `${this.rect.top}px`,
			'z-index': 9999999,
		};
	}
	public updateStyle(style: Style) {
		this.style = {
			...style,
		};
	}
}
