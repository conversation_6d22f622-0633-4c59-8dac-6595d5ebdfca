<script setup lang="tsx">
import { CopyDocument } from '@element-plus/icons-vue';
import commonFunction from './utils/commonFunction';

const props = defineProps({
	type: {
		type: String,
		default: 'default',
	},
	text: {
		type: String,
		default: '',
	},
});

const { copyText } = commonFunction();
async function handleCopy() {
	if (props.type === 'default') {
		await copyText(props.text);
	}
}
</script>
<template>
	<el-tooltip effect="dark" content="复制" placement="top">
		<el-button @click="handleCopy" link>
			<el-icon><CopyDocument /></el-icon>
		</el-button>
	</el-tooltip>
</template>
<style scoped lang="scss"></style>
