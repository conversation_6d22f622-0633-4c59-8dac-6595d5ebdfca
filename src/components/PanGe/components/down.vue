<template>
	<el-popover v-model:visible="visible" :fallback-placements="['right', 'left']" :width="400" trigger="click" title="进一步反馈">
		<template #reference>
			<slot name="reference"></slot>
		</template>
		<div>
			<yun-pro-form ref="formRef" :form="formData" :form-props="{ labelPosition: 'top' }" :columns="COLUMNS" />
			<div class="container__footer">
				<el-button type="primary" :loading="loading" :disabled="disabled" @click="submitForm">提交</el-button>
			</div>
		</div>
	</el-popover>
</template>
<script setup lang="ts">
import { ref, inject, computed } from 'vue';
import { cloneDeep } from 'lodash';

const props = defineProps({
	content: {
		type: Array,
		required: true,
	},
});

const loading = ref(false);
const visible = ref(false);
const formData = ref({});
const formRef = ref<any>(null);
const disabled = computed(() => {
	return (
		!Object.keys(formData.value).length ||
		!Object.values(formData.value).some((val) => {
			if (Array.isArray(val)) {
				return val.length;
			}
			return val;
		})
	);
});

const COLUMNS = [
	{
		prop: 'feedbackQuestion',
		label: '针对问题',
		colProps: { span: 24 },
		type: 'checkbox',
		enums: [
			{ label: '不理解问题', value: '不理解问题' },
			{ label: '上下文错误', value: '上下文错误' },
			{ label: '未发现问题中的错误', value: '未发现问题中的错误' },
		],
	},
	{
		prop: 'feedbackAnswer',
		label: '针对回答',
		type: 'checkbox',
		colProps: { span: 24 },
		enums: [
			{ label: '事实性错误', value: '事实性错误' },
			{ label: '格式错误', value: '格式错误' },
			{ label: '逻辑混乱', value: '逻辑混乱' },
			{ label: '时效性差', value: '时效性差' },
			{ label: '内容重复', value: '内容重复' },
			{ label: '字数不符合要求', value: '字数不符合要求' },
		],
	},
	{
		prop: 'feedbackContent',
		label: '您认为更理想的回答是什么?',
		type: 'input',
		colProps: { span: 24 },
		attrs: { type: 'textarea' },
	},
];

const handleFeedback = inject('handleFeedback');

async function submitForm() {
	try {
		loading.value = true;
		if (handleFeedback) {
			handleFeedback(cloneDeep(formData.value), props.content)
		}
		formRef.value?.elForm?.resetFields();
		visible.value = false;
	} finally {
		loading.value = false;
	}
}
</script>
<style lang="scss" scoped>
.container__footer {
	text-align: right;
}
</style>
