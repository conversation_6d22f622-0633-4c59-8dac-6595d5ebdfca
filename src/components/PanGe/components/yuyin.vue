<script setup lang="tsx">
import { ref } from 'vue';
import yuyin from './assets/yuyin.png';
import yuyinL from './assets/yuyin-l.gif';
import { ElMessage } from 'yun-design';
import Recorder from 'js-audio-recorder'
import { convertToMp3 } from './utils/yuyin';

const flag = ref(false);
const props = defineProps({
	disabled: {
		type: Boolean,
		default: false,
	},
});
const emits = defineEmits<{ 
  start: []
  change: [value: string]
}>()

const recorder = new Recorder({
  sampleBits: 16,
  sampleRate: 16000,
  numChannels: 1,
  compiling: false,
});

async function uploadAudio(file:File) {
  const formData = new FormData();
  formData.append('file', file);
  emits('change', formData)
}
async function startRecording() {
  try {
    await recorder.start();
    emits('start');
    flag.value = true;
  }
  catch (e) {
  //   onCancel()
  }
}
function stopRecording() {
  flag.value = false;
  recorder.stop();
  const mp3Blob = convertToMp3(recorder);
  const mp3File = new File([mp3Blob], 'temp.mp3', { type: 'audio/mp3' })
  uploadAudio(mp3File)
}
function handleClick() {
  if(props.disabled) return;
  if(!flag.value) {
      (Recorder as any).getPermission().then(() => {
          startRecording();
      }, () => {
          ElMessage.error('不支持浏览器录音')
      })
  } else {
      stopRecording()
  }
}
</script>
<template>
	<div>
    <img class="cursor-pointer" :src="flag? yuyinL: yuyin" @click="handleClick" width="24"></img>
  </div>
</template>
<style scoped lang="scss"></style>
