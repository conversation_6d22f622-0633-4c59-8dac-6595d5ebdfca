<template>
	<div class="presentation" ref="innerRef">
		<article v-for="(item, key) in list" :key="key" style="padding-top: 12px; padding-bottom: 12px">
			<div v-if="item.flag === 'question'" class="flex question" style="flex-direction: column; align-items: end">
				<div v-if="user.avatar || user.username" class="flex jc-end ai-end"
					style="align-items: center; margin-bottom: 0.5em">
					<img v-if="user.avatar" :src="user.avatar" class="layout-navbars-breadcrumb-user-link-photo"
						style="margin-right: 1em" />
					<h3 v-if="user.username" style="font-weight: 600">{{ user.username }}</h3>
				</div>
				<div style="width: 100%; text-align: right; box-sizing: border-box">
					<div style="display: flex; align-items: center" :class="{ 'jc-between': show, 'jc-end': !show }">
						<el-checkbox v-if="show" v-model="item._share" @change="(value) => shareChecked(value, item, key)"
							style="flex: 1; margin-right: 0.5em"></el-checkbox>
						<div style="display: flex; align-items: center">
							<div v-if="!showShare && !props.isShare && (item.content || '').trim()" class="action">
								<copy-l :text="item.content"></copy-l>
							</div>
							<div class="message" style="flex: 1">
								<div v-if="item.files" class="flex gap-8">
									<el-image class="bg-fff" v-for="(file, index) in item.files" :key="index" :src="file.url"
										:preview-src-list="item.files.map((file) => file.url)" style="width: 64px; height: 64px"
										fit="contain" :initial-index="index" />
								</div>
								<p style="word-wrap: break-word">{{ item.content }}</p>
							</div>
							<div v-if="!props.isShare" style="margin-left: 4px">
								<img :src="avatar" class="layout-navbars-breadcrumb-user-link-photo" />
							</div>
						</div>
					</div>
				</div>
			</div>
			<div v-else>
				<div class="flex ai-end" style="margin-bottom: 8px">
					<el-checkbox v-if="show" v-model="item._share"
						@change="(value) => shareChecked(value, item, key)"></el-checkbox>
					<img :src="questionPange[item.status]" class="layout-navbars-breadcrumb-user-link-photo" />
					<h3 style="font-weight: 600">小盘</h3>
				</div>
				<Loading-l v-if="item.status === 'loading'"></Loading-l>
				<div v-else>
					<div v-if="item.status === 'error'">{{ item.content }}</div>
					<div v-else v-html="marked(item.content)" class="markdown-body answer"></div>
					<div v-show="!showShare && !props.isShare" class="flex" style="padding-top: 8px; padding-left: 12px">
						<copy-l v-if="item.status === 'succeeded'" :text="item.content"></copy-l>
						<el-tooltip v-if="key === list.length - 1 && !loading" effect="dark" content="重新生成" placement="top">
							<el-button @click="handleRefresh(key)" link>
								<el-icon>
									<Refresh />
								</el-icon>
							</el-button>
						</el-tooltip>
						<el-tooltip v-if="!loading" effect="dark" content="分享" placement="top">
							<el-button @click="handleShare(item, key)" link>
								<el-icon>
									<Share />
								</el-icon>
							</el-button>
						</el-tooltip>
						<el-button v-if="!loading" @click="handleThumbsUp(item)" :type="item.rating === 'like' ? 'primary' : ''"
							link>
							<Thumbs />
						</el-button>
						<down-form v-if="!loading" :content="[list[key - 1], item]">
							<template #reference>
								<el-button @click="handleThumbsDown(item)" :type="item.rating === 'dislike' ? 'danger' : ''" link>
									<Thumbs :rotate="180" />
								</el-button>
							</template>
						</down-form>
					</div>
				</div>
			</div>
		</article>
	</div>
</template>

<script setup lang="ts">
import { ref, toRefs, inject, computed } from 'vue';
import { marked } from 'marked';
import { Refresh, Share } from '@element-plus/icons-vue';
import pangeD from './assets/pange-def.gif';
import pangeE from './assets/pange-error.gif';
import avatarD from './assets/avatar.png';
import expand from './expand';
import Thumbs from './thumbs.vue';
import DownForm from './down.vue';
import CopyL from './copy.vue';
import LoadingL from './loading.vue';

const props = defineProps({
	data: {
		type: Array,
		default: () => [],
	},
	user: {
		type: Object,
		default: () => ({}),
	},
	avatar: {
		type: String,
		default: '',
	},
	isShare: {
		type: Boolean,
		default: false,
	},
});

const emit = defineEmits(['share', 'checked', 'thumbsUp', 'thumbsDown', 'refresh']);

const avatar = computed(() => {
	return props.avatar || avatarD;
});

const { data: list, user } = toRefs(props);
const loading = inject('loading');

const showShare = inject('showShare');

const show = computed(() => {
	return showShare.vlaue && !props.isShare;
});

const questionPange = new Proxy(
	{
		default: pangeD,
		error: pangeE,
	} as Record<string, string>,
	{
		get (target, key) {
			if (Reflect.has(target, key)) {
				return Reflect.get(target, key);
			}
			return Reflect.get(target, 'default');
		},
	}
);

marked.use(expand);
const textareaRef = ref();

const innerRef = ref<Element>();

function handleRefresh (index: number) {
	emit('refresh', index);
}

function handleThumbsUp (item) {
	emit('thumbsUp', item);
}

function handleShare (item, index) {
	emit('share', item, index);
}

function shareChecked (value, item, index) {
	emit('checked', value, item, index);
}

function handleThumbsDown (item) {
	emit('thumbsDown', item);
}

function handleBeforeEnter (item) {
	item.popFlag = item.rating === 'dislike';
}
function handleAfter (item) {
	item.popFlag = false;
}

defineExpose({
	innerRef,
});
</script>
<style lang="scss" scoped>
.flex {
	display: flex;
}

@each $gap in(4, 8) {
	.gap-#{$gap} {
		gap: #{$gap}px;
	}
}

.jc-end {
	justify-content: flex-end;
}

.jc-between {
	justify-content: space-between;
}

$flex-ai: (
	start: flex-start,
	end: flex-end,
	center: center,
	stretch: stretch,
);

@each $key, $value in $flex-ai {
	.ai-#{$key} {
		align-items: $value;
	}
}

.bg-fff {
	background-color: #fff;
}

.flex-grow-1 {
	flex: 1;
}

.question {
	.action {
		display: none;
	}

	.message {
		padding: 0.625rem 1.25rem;
		// max-width: 100%;
		background-color: rgba(232, 232, 232, 0.5);
		border-radius: 1.5rem;
	}

	&:hover .action {
		display: block;
	}
}

.content {
	overflow: hidden;
}

.answer {
	--bgColor-muted: #ececec;
	background: unset;
}

.default {
	padding-top: 40px;
	display: flex;
	flex-direction: column;
	align-items: center;
	font-size: 12px;
	color: #999;

	.pange {
		width: 100px;
		margin-bottom: 24px;
	}
}

.layout-navbars-breadcrumb-user {
	display: flex;
	align-items: center;
	justify-content: flex-end;

	&-link {
		height: 100%;
		display: flex;
		align-items: center;
		white-space: nowrap;

		&-photo {
			width: 36px;
			height: 36px;
			border-radius: 100%;
		}
	}

	&-icon {
		padding: 0 10px;
		cursor: pointer;
		color: var(--next-bg-topBarColor);
		height: 50px;
		line-height: 50px;
		display: flex;
		align-items: center;

		&:hover {
			background: var(--next-color-user-hover);

			i {
				display: inline-block;
				animation: logoAnimation 0.3s ease-in-out;
			}

			:deep(img) {
				animation: logoAnimation 0.3s ease-in-out;
			}
		}
	}

	:deep(.el-dropdown) {
		color: var(--next-bg-topBarColor);
	}

	:deep(.el-badge) {
		height: 40px;
		line-height: 40px;
		display: flex;
		align-items: center;
	}

	:deep(.el-badge__content.is-fixed) {
		top: 12px;
	}
}
</style>
