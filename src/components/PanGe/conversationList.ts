import { ref } from 'vue';
import { useUserInfo } from '/@/stores/userInfo';
import { conversationsList } from '/@/api/admin/chart';

export interface List {
	updated_at: number;
	created_at: number;
	id: string;
	introduction: string;
	name: string;
	inputs: {
		chart_id: string;
		tenant_id: string;
		user_id: string;
	};
}
const list = ref<List[]>([]);
async function getList() {
	const stores = useUserInfo();
	const { userInfos } = storeToRefs(stores);
	const { data } =
		(await conversationsList({
			user: userInfos.value.user?.userId,
			limit: 100,
		})) || {};
	list.value = data.data || [];
}
export {
	list,
	getList
}
