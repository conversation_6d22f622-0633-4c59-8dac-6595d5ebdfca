<script setup lang="ts">
import { Document, Delete } from '@yun-design/icons-vue';


defineProps<{
	value: Array<{ url: string; name: string }>;
	allowEdit: {
    type: Boolean,
    default: true,
  },
}>();
function getFileUrl(file: { url: string; name: string }) {
	return file.url;
	// const url = file.url;
	// return url.startsWith('http') ? url : `${import.meta.env.VITE_API_URL.slice(0, -1)}${url}`;
}
</script>

<template>
	<div class="file-row-grid">
		<el-link
			v-for="(file, index) in value"
			:key="index"
			class="file-item"
			:icon="Document"
			:href="getFileUrl(file)"
			target="_blank"
			type="primary"
		>
			<div class="file-name">{{ file.name }}</div>

			<el-icon class="delete-icon" v-if="allowEdit">
				<Delete />
			</el-icon>
		</el-link>
	</div>
</template>

<style scoped>
.file-row-grid {
	display: inline-grid;
	grid-template-columns: 1fr;
	gap: 8px;
	justify-content: flex-start;
	width: 100%;
}
.file-item {
	display: flex;
	justify-content: start !important;
	align-items: center !important;
	margin: 6px 0;
	gap: 8px;
	.file-name {
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		word-break: break-all;
		min-width: 0;
	}
	.delete-icon {
		cursor: pointer;
	}
}
</style>
