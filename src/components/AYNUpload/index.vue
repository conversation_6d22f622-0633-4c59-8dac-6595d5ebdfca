<!-- 爱养牛定制化的上传组件样式 -->
<template>
  <div class="AYNUpload-Wrapper">
    <ElUpload
      ref="upload"
      :file-list="fileList"
      :headers="headers"
      :action="action"
      :limit="limit"
      :show-file-list="showFileList"
      v-bind="$attrs"
      :multiple="false"
      :on-success="onSuccess"
      :on-remove="handleRemove"
      :on-preview="handlePreview"
      :on-change="handleChange"
      :on-exceed="handleExceed"
    >
      <template v-if="displayType === 'outer'">
        <el-button
          type="primary"
          border
          :icon="Upload"
          >{{ buttonText }}</el-button
        >
        <div
          class="el-upload__tip"
          v-if="tip"
        >
          {{ tip }}
        </div>
      </template>
      <template v-else-if="displayType === 'inner'">
        <el-button
          type="action"
          border
          :icon="Upload"
          >{{ buttonText }}</el-button
        >
        <div
          class="el-upload__tip"
          v-if="tip"
        >
          {{ tip }}
        </div>
      </template>
      <template v-else-if="$slots.default">
        <slot />
      </template>
    </ElUpload>
  </div>
</template>
<script setup lang="ts">
import { ElUpload } from 'element-plus';
import { defineEmits, defineProps } from 'vue';
import { Upload } from '@yun-design/icons-vue';
import { Session } from '/@/utils/storage';
import { ref, watch } from 'vue';

const upload = ref();

const headers = computed(() => {
  return {
    Authorization: 'Bearer ' + Session.get('token'),
    'TENANT-ID': Session.getTenant(),
  };
});
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  action: {
    type: String,
    default: `${import.meta.env.VITE_API_URL}/admin/sys-file/upload`,
  },
  showFileList: {
    type: Boolean,
    default: false,
  },
  limit: {
    type: Number,
    default: 10,
  },
  tip: String,
  displayType: String, // outer | inner
  buttonText: {
    type: String,
    default: '点击上传',
  },
});
const emit = defineEmits(['update:modelValue', 'change']);
// 不使用双向
// const fileList = computed<any[]>({
//   get() {
//     return props.modelValue || [];
//   },
//   set(val) {
//     emit('update:modelValue', val);
//   },
// });
const fileList = ref<any[]>([]);

// 如果使用了绑定modelValue, 则更新 fileList
watch(() => props.modelValue, (val) => {
  fileList.value = val || [];
});

function handleExceed(files: any[]) {
  upload.value?.clearFiles();
  upload.value?.handleStart(files[0]);
}
function handleChange(file: any, fileList: any) {
  // eslint-disable-next-line no-console
  console.log('handleChange', file, fileList);
}
function handleAPIUpload() {
  const api = import.meta.env.VITE_API_URL;
  return api?.endsWith('/') ? api.slice(0, -1) : api;
}
function onSuccess(response: any, file: any, list: any) {
  // eslint-disable-next-line no-console
  console.log('onSuccess', response, file, list);
  const fileListData = list
    .map((e: any) => {
      const fileData = {
        url: e.response?.data?.url ? `${handleAPIUpload()}${e.response?.data?.url}` : e.url,
        name: e.name || e.response?.data?.name,
        uuid: e.uid,
      };
      return fileData;
    })
    .filter((e: any) => e.url);
  fileList.value = fileListData;
  emit('change', fileList.value, file);
  emit('update:modelValue', fileList.value);
}
function handleRemove(file: any, list: any) {
  // eslint-disable-next-line no-console
  console.log('handleRemove', file, list, fileList.value);
  emit('change', fileList.value, file);
  emit('update:modelValue', fileList.value);
}
function handlePreview(file: any) {
  if (file?.url) {
    window.open(file.url);
  }
}
</script>
<style lang="scss">
.AYNUpload-Wrapper {
  font-family: PingFang SC;
}
</style>
