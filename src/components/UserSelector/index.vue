<template>
  <el-select
    :model-value="props.modelValue"
    :placeholder="placeholder"
    :multiple="multiple"
    filterable
    remote
    :remote-method="handleSearch"
    :loading="loading"
    style="width: 100%"
    @update:model-value="handleUserSelect"
    clearable
    collapse-tags
  >
    <el-option
      v-for="user in userList"
      :key="user.userId"
      :label="user.username"
      :value="user.userId"
    >
      <div style="display: flex; align-items: center; gap: 4px">
        <div>{{ user.username }}</div>
        <div
          v-if="user.departmentPath"
          style="font-size: 12px; color: #999"
        >
          {{ user.departmentPath || '--' }}
        </div>
      </div>
    </el-option>
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { getUserListApi } from '@/api/purchasing/proposal';

interface User {
  userId: string;
  username: string;
  deptName?: string;
  departmentPath?: string;
}

interface Props {
  modelValue?: string | string[]; // userId 或 userId 数组
  placeholder?: string;
  multiple?: boolean; // 是否多选
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  placeholder: '选择用户',
  multiple: false,
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string | string[]): void;
  (e: 'change', user: User | User[]): void;
}>();

// 响应式数据
const loading = ref(false);
const userList = ref<User[]>([]);
const searchKeyword = ref('');



// 获取用户列表
const fetchUserList = async (username?: string) => {
  try {
    loading.value = true;
    const response = await getUserListApi(username);
    if (response.code === 0) {
      userList.value = response.data?.records || [];
    }
  } catch (error) {
    console.error('获取用户列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = (query: string) => {
  searchKeyword.value = query;
  fetchUserList(query);
};

// 用户选择处理
const handleUserSelect = (value: string | string[] | null) => {
  if (props.multiple) {
    // 多选模式
    const userIds = Array.isArray(value) ? value : [];
    emit('update:modelValue', userIds);
    const selectedUsers = userList.value.filter(user => userIds.includes(user.userId));
    emit('change', selectedUsers);
  } else {
    // 单选模式
    const userId = typeof value === 'string' ? value : '';
    emit('update:modelValue', userId);
    if (userId) {
      const selectedUser = userList.value.find(user => user.userId === userId);
      if (selectedUser) {
        emit('change', selectedUser);
      }
    }
  }
};

// 监听 modelValue 变化，如果当前用户不在列表中，重新获取数据
watch(
  () => props.modelValue,
  (newValue) => {
    if (props.multiple) {
      // 多选模式
      const userIds = Array.isArray(newValue) ? newValue : [];
      const missingUsers = userIds.some(userId => !userList.value.find(user => user.userId === userId));
      if (missingUsers) {
        fetchUserList();
      }
    } else {
      // 单选模式
      if (newValue && !userList.value.find((user) => user.userId === newValue)) {
        fetchUserList();
      }
    }
  }
);

// 组件挂载时获取初始数据
onMounted(() => {
  fetchUserList();
});
</script>

<style lang="scss" scoped>
:deep(.el-select-dropdown__item) {
  color: #303133 !important;
  
  &:hover {
    color: #409eff !important;
  }
  
  &.selected {
    color: #409eff !important;
  }
}
</style>
