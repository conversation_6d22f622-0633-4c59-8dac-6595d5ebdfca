<template>
  <el-dialog v-model="visible" :title="title" width="1120px" @close="handleCancel">
    <div>
      <el-form :inline="true" class="mb-4">
        <el-form-item
          v-for="field in filterFields"
          :key="field.fieldName"
          :label="field.listLabel"
        >
          <component
            v-if="field.queryFormType === 'select'"
            :is="getQueryComponent(field.queryFormType)"
            v-model="filterModel[field.fieldName]"
            v-bind="getQueryComponentProps(field)"
            :placeholder="'请输入' + field.listLabel"
            style="min-width: 180px"
          >
            <el-option
              v-for="opt in field.options"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </component>
          <component
            v-else
            :is="getQueryComponent(field.queryFormType)"
            v-model="filterModel[field.fieldName]"
            v-bind="getQueryComponentProps(field)"
            :placeholder="'请输入' + field.listLabel"
            style="min-width: 180px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">查询</el-button>
          <el-button @click="handleReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="tableData"
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="margin-top: 16px"
        :row-key="getRowKey"
        :height="400"
        :default-sort="{ prop: tableFields[0]?.fieldName, order: 'descending' }"
        :highlight-current-row="true"
        :show-summary="false"
        :summary-method="undefined"
        :empty-text="loading ? '加载中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="50" :reserve-selection="true" />
        <el-table-column
          v-for="field in tableFields"
          :key="field.fieldName"
          :prop="field.fieldName"
          :label="field.listLabel"
          :min-width="getColumnWidth(field.listLabel)"
        >
          <template #default="{ row }">
            <span>{{ formatCellValue(row[field.fieldName]) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="total > 0"
        class="mt-2 text-right"
        background
        layout="total, prev, pager, next, sizes"
        :total="total"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100]"
        @current-change="handlePageChange"
        @size-change="handleSizeChange"
      />
    </div>
    <template #footer>
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, computed, onMounted } from 'vue';
import { useTableQueryApi } from '/@/api/gen/table';

const props = withDefaults(
  defineProps<{
    modelValue: boolean;
    dataSource: any;
    title?: string;
    selectedValues?: (string | number)[];
  }>(),
  {
    title: '数据源筛选配置',
    selectedValues: () => [],
  }
);

const emit = defineEmits(['update:modelValue', 'confirm']);

const visible = ref(props.modelValue);
watch(
  () => props.modelValue,
  (v) => (visible.value = v)
);
watch(visible, (v) => emit('update:modelValue', v));

// 过滤出筛选项字段
const filterFields = computed(() =>
  (props.dataSource?.fields || []).filter((f: any) => f.queryItem === '1')
);
// 过滤出表格字段
const tableFields = computed(() =>
  (props.dataSource?.fields || []).filter((f: any) => f.gridItem === '1')
);

// 分页与表格数据
const tableData = ref<any[]>([]);
const total = ref(0);
const page = ref(1);
const pageSize = ref(100); // 默认每页100条
const loading = ref(false);
const selectedRows = ref<any[]>([]);

// 查询表单模型
const filterModel = ref<Record<string, any>>({});

// 计算列宽
function getColumnWidth(label: string) {
  const baseWidth = 40; // 基础宽度
  const charWidth = 14; // 每个字符的宽度
  const width = baseWidth + label.length * charWidth;
  return Math.min(width, 300); // 最大宽度300px
}

// 格式化单元格值
function formatCellValue(value: any) {
  try {
    value = JSON.parse(value);
  } catch (error) {}
  if (Array.isArray(value)) {
    return value.join('、');
  }
  return value;
}

// 查询组件类型映射
function getQueryComponent(type: string) {
  switch (type) {
    case 'text':
      return 'el-input';
    case 'select':
      return 'el-select';
    case 'date':
      return 'el-date-picker';
    case 'daterange':
      return 'el-date-picker';
    default:
      return 'el-input';
  }
}

function getQueryComponentProps(field: any) {
  if (field.queryFormType === 'date') {
    return { type: 'date', clearable: true };
  }
  if (field.queryFormType === 'daterange') {
    return {
      type: 'datetimerange',
      clearable: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      'range-separator': '至',
      'start-placeholder': '开始时间',
      'end-placeholder': '结束时间',
      'default-time': [new Date(2000, 0, 1, 0, 0, 0), new Date(2000, 0, 1, 23, 59, 59)],
    };
  }
  if (field.queryFormType === 'select') {
    return { clearable: true };
  }
  return { clearable: true };
}

// 查询条件组装
function buildConditions() {
  const conditions = filterFields.value
    .map((f: any) => {
      const val = filterModel.value[f.fieldName];
      if (val === undefined || val === null || val === '') return null;
      if (f.queryFormType === 'daterange' && Array.isArray(val) && val.length === 2) {
        const [start, end] = val;
        let startStr = '';
        let endStr = '';
        if (start instanceof Date) {
          startStr = formatDateTime(start);
        } else {
          startStr = start;
        }
        if (end instanceof Date) {
          endStr = formatDateTime(new Date(end.setHours(23, 59, 59, 0)));
        } else if (/^\d{4}-\d{2}-\d{2}$/.test(end)) {
          endStr = end + ' 23:59:59';
        } else if (/^\d{4}-\d{2}-\d{2} 00:00:00$/.test(end)) {
          endStr = end.replace('00:00:00', '23:59:59');
        } else if (/^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$/.test(end)) {
          endStr = end;
        }
        return {
          field: f.fieldName,
          operator: f.queryType || 'BETWEEN',
          value: [startStr, endStr],
        };
      }
      return {
        field: f.fieldName,
        operator: f.queryType || '=',
        value: val,
      };
    })
    .filter(Boolean);

  // 如果有已选值，添加条件
  if (props.selectedValues?.length > 0 && props.dataSource?.valueField) {
    conditions.push({
      field: props.dataSource.valueField,
      operator: 'IN',
      value: props.selectedValues,
    });
  }

  return conditions;
}

// 日期转字符串
function formatDateTime(date: Date): string {
  const pad = (n: number) => n.toString().padStart(2, '0');
  return (
    date.getFullYear() +
    '-' +
    pad(date.getMonth() + 1) +
    '-' +
    pad(date.getDate()) +
    ' ' +
    pad(date.getHours()) +
    ':' +
    pad(date.getMinutes()) +
    ':' +
    pad(date.getSeconds())
  );
}

async function fetchTableData() {
  if (!props.dataSource?.tableName) return;
  loading.value = true;
  try {
    const res = await useTableQueryApi({
      dsName: props.dataSource.dsName || 'master',
      tableName: props.dataSource.tableName,
      page: page.value,
      pageSize: pageSize.value,
      conditions: buildConditions(),
      needTotal: true,
    });
    tableData.value = res.data?.data || [];
    total.value = res.data?.total || 0;

    // 设置已选行
    if (props.selectedValues?.length > 0 && props.dataSource?.valueField) {
      const selectedRows = tableData.value.filter((row) =>
        props.selectedValues.includes(row[props.dataSource.valueField])
      );
      selectedRows.forEach((row) => {
        const rowKey = getRowKey(row);
        const tableRef = document.querySelector('.el-table__body-wrapper table');
        if (tableRef) {
          const checkbox = tableRef.querySelector(
            `tr[data-row-key="${rowKey}"] input[type="checkbox"]`
          );
          if (checkbox) {
            (checkbox as HTMLInputElement).checked = true;
          }
        }
      });
    }
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  page.value = 1;
  fetchTableData();
}

function handleReset() {
  for (const key in filterModel.value) {
    filterModel.value[key] = undefined;
  }
  handleSearch();
}

function handlePageChange(val: number) {
  page.value = val;
  fetchTableData();
}

function handleSizeChange(val: number) {
  pageSize.value = val;
  page.value = 1;
  fetchTableData();
}

function handleSelectionChange(selection: any[]) {
  selectedRows.value = selection;
}

const getRowKey = (row: any) => row.id || row[tableFields.value[0]?.fieldName];

function handleConfirm() {
  emit('confirm', {
    filters: filterModel.value,
    fields: tableFields.value,
    selectedRows: selectedRows.value,
  });
  visible.value = false;
}

function handleCancel() {
  visible.value = false;
}

onMounted(() => {
  fetchTableData();
  filterFields.value.forEach((f: any) => {
    const val = filterModel.value[f.fieldName];
    if (
      f.queryFormType === 'daterange' &&
      Array.isArray(val) &&
      typeof val[0] === 'string'
    ) {
      filterModel.value[f.fieldName] = val.map((v: string) => new Date(v));
    }
  });
});
</script>

<style lang="scss" scoped>
.el-table {
  :deep(.el-table__header) {
    th {
      white-space: nowrap;
      background-color: var(--el-bg-color);
    }
  }
}
</style>
