<!--
 * @Author: chenting<PERSON> <EMAIL>
 * @Date: 2025-05-09 14:43:33
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-06-03 15:57:41
 * @FilePath: \fe-dcrg-admin\src\components\YunUpload\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <yun-upload
    v-model="fileList"
    :headers="headers"
    v-bind="$attrs"
    :key="key"
    :action="action"
    :on-success="onSuccess"
    :remove-image="handleRemove"
    :download-image="handleDownload"
    :on-remove="handleRemoveFile"
    name="file"
  />
</template>

<script setup>
import { computed, ref, watch } from 'vue';
import { Session } from '/@/utils/storage';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  action: {
    type: String,
    default: `${import.meta.env.VITE_API_URL}/admin/sys-file/upload`,
  },
});
const emit = defineEmits(['update:modelValue', 'change']);
const key = ref(0);
// const attrs = useAttrs();
const headers = computed(() => {
  return {
    Authorization: 'Bearer ' + Session.get('token'),
    'TENANT-ID': Session.getTenant(),
  };
});
const fileList = computed({
  get() {
    return props.modelValue || [];
  },
  set(val) {
    let value = val;
    value = val.map((item) => {
      return item.response?.data
        ? {
            url: item.response?.data?.url ? `${import.meta.env.VITE_API_URL}${item.response?.data?.url}` : item.url,
            name: item.name || item.response?.data?.name,
            uuid: item.uid,
          }
        : item;
    });
    emit('update:modelValue', value);
  },
});

function onSuccess(response, file, list) {
  const fileListData = list.map((e) => {
    const fileData = {
      url: e.response?.data?.url ? `${import.meta.env.VITE_API_URL}${e.response?.data?.url}` : e.url,
      name: e.name || e.response?.data?.name,
      uuid: e.uid,
    };
    return fileData;
  });
  fileList.value = fileListData;
  // emit('update:modelValue', fileListData);
  emit('change', fileListData);
}
const handleRemove = (file) => {
  fileList.value = fileList.value.filter((i) => i.uuid !== file.uuid);
  emit('change', fileList.value);
};
const handleRemoveFile = (file, f) => {
  fileList.value = fileList.value.filter((i) => i.uuid !== file.uuid);
  emit('change', f);
};
const handleDownload = (file) => {
  window.open(file.url);
};
watch(
  () => props.modelValue?.length,
  (v) => {
    key.value++;
  }
);
</script>

<style scoped lang="scss"></style>
