<template>
  <yun-upload
    v-model="fileList"
    :headers="headers"
    v-bind="$attrs"
    :action="action"
    :on-success="onSuccess"
    :remove-image="handleRemove"
    :download-image="handleDownload"
    name="file"
  />
</template>

<script setup>
import { computed } from 'vue';
import { Session } from '/@/utils/storage';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  action: {
    type: String,
    default: `${import.meta.env.VITE_API_URL}/admin/sys-file/upload`,
  },
});
const emit = defineEmits(['update:modelValue']);
// const attrs = useAttrs();
const headers = computed(() => {
  return {
    Authorization: 'Bearer ' + Session.get('token'),
    'TENANT-ID': Session.getTenant(),
  };
});
const fileList = computed({
  get() {
    return props.modelValue || [];
  },
  set(val) {
    let value = val;
    value = val.map((item) => {
      return item.response?.data
        ? {
            url: item.response?.data?.url ? `${import.meta.env.VITE_API_URL}${item.response?.data?.url}` : item.url,
            name: item.name || item.response?.data?.name,
            uuid: item.uid,
          }
        : item;
    });
    emit('update:modelValue', value);
  },
});

function onSuccess(response, file, list) {
  const fileListData = list.map((e) => {
    const fileData = {
      url: e.response?.data?.url ? `${import.meta.env.VITE_API_URL}${e.response?.data?.url}` : e.url,
      name: e.name || e.response?.data?.name,
      uuid: e.uid,
    };
    return fileData;
  });
  fileList.value = fileListData;
  emit('update:modelValue', fileListData);
}
const handleRemove = (file) => {
  fileList.value = fileList.value.filter((i) => i.uuid !== file.uuid);
};
const handleDownload = (file) => {
  window.open(file.url);
};
</script>
<style lang="scss">
.title-progress {
  display: inline-block;
  width: 70px !important
}
</style>
<style scoped lang="scss"></style>
