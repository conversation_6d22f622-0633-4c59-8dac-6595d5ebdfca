<template>
	<div class="table-container">
		<yun-table
			:columns="tableColumns"
			:data="data"
			:show-table-setting="false"
			:stripe="true"
		>
			<template
				v-for="column of columns"
				#[column.prop]="{ row }"
				:key="column.prop"
			>
				<div v-if="typeof column.render === 'function'">
					<component
						:is="column.render(row, column)"
						:disabled="disabled"
					></component>
				</div>
				<div v-else-if="column.type === 'textarea'">
					<el-input
						v-model="row[column.prop]"
						type="textarea"
						:rows="1"
						:placeholder="`请输入${column.label}`"
						clearable
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
				<div v-else-if="column.type === 'input-number'">
					<el-input-number
						v-model="row[column.prop]"
						:type="column.type"
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
				<div v-else-if="column.type === 'select'">
					<el-select
						v-model="row[column.prop]"
						:placeholder="`请选择${column.label}`"
						clearable
						filterable
						:multiple="false"
						:disabled="disabled"
            v-bind="column.attrs"
					>
						<template v-if="column.dict">
							<el-option
								v-for="item in getDict(column.dict)"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</template>
						<template v-else-if="column.options">
							<el-option
								v-for="item in column.options"
								:key="item.value"
								:label="item.label"
								:value="item.value"
							/>
						</template>
					</el-select>
				</div>
				<div v-else-if="column.type === 'checkbox'">
					<el-checkbox-group v-model="row[column.prop]" v-bind="column.attrs">
						<el-checkbox
							v-for="item in getDict(column.dict)"
							:key="item.value"
							:label="item.value"
							:disabled="disabled"
						>
							{{ item.label }}
						</el-checkbox>
					</el-checkbox-group>
				</div>
				<div v-else-if="column.type === 'radio'">
					<el-radio-group v-model="row[column.prop]" v-bind="column.attrs">
						<el-radio
							v-for="item in getDict(column.dict)"
							:key="item.value"
							:label="item.value"
							:disabled="disabled"
						>
							{{ item.label }}
						</el-radio>
					</el-radio-group>
				</div>
				<div v-else-if="column.type === 'date'">
					<el-date-picker
						v-model="row[column.prop]"
						type="date"
						:placeholder="`请选择${column.label}`"
						clearable
						format="YYYY-MM-DD"
						value-format="YYYY-MM-DD"
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
				<div v-else-if="column.type === 'datetime'">
					<el-date-picker
						v-model="row[column.prop]"
						type="datetime"
						:placeholder="`请选择${column.label}`"
						clearable
						format="YYYY-MM-DD HH:mm:ss"
						value-format="YYYY-MM-DD HH:mm:ss"
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
				<div v-else-if="column.type === 'switch'">
					<el-switch
						v-model="row[column.prop]"
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
				<div v-else>
					<el-input
						v-model="row[column.prop]"
						:placeholder="`请输入${column.label}`"
						clearable
						:disabled="disabled"
            v-bind="column.attrs"
					/>
				</div>
			</template>
			<template
				v-if="!disabled"
				#action="{ row }"
			>
				<el-button
					v-if="data.length > 1"
					type="action"
					@click="deleteRow(row)"
				>
					删除
				</el-button>
			</template>
		</yun-table>
		<el-button
			v-if="!disabled"
			style="width: 100%; margin-top: 5px"
			:icon="Plus"
			@click="addAttr"
		>
			添加
		</el-button>
	</div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Plus } from '@yun-design/icons-vue';
import { useDict } from '@/hooks/dict';

const props = defineProps({
  disabled: {
    type: Boolean,
    default: false,
  },
  columns: {
    type: Array,
    default: () => [],
  },
});
const data = defineModel();
const getDict = computed(() => (str) => {
  if (!str) return [];
  const value = useDict(str)?.[str]?.value;
  return value;
});
if (!data.value) data.value = [];

const actionColumn = {
  label: '操作',
  prop: 'action',
  width: '60px',
};

const tableColumns = computed(() => {
  const columns = props.columns.map((item) => ({
    ...item,
    showOverflowTooltip: false,
    minWidth: 150,
  }));
  if (!props.disabled) {
    columns.push(actionColumn);
  }
  return columns;
});
function deleteRow(row) {
  const index = data.value.indexOf(row);
  if (index > -1) {
    data.value.splice(index, 1);
  }
}
function addAttr() {
  data.value.push({});
}
watch(
  data,
  () => {
    if (!data.value?.length && !props.disabled) {
      data.value = [{}];
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.table-container {
  position: sticky;
  top: 80px;
}
</style>
