<template>
  <div class="identity-selector">
    <!-- 身份选择行 -->
    <div 
      v-for="(item, index) in identityList" 
      :key="item._key || index"
      class="identity-row"
    >
      <!-- 身份选择器 -->
      <el-select
        v-if="isMutiIdentity"
        v-model="item.identityId"
        placeholder="请输入身份名称搜索"
        clearable
        filterable
        remote
        reserve-keyword
        :remote-method="remoteSearchIdentity"
        :loading="searchLoading"
        class="identity-select"
        @change="(value: string | number) => handleIdentityChange(value, index)"
      >
        <el-option
          v-for="identity in getAvailableIdentities(index)"
          :key="identity.id"
          :label="`${identity.identityName} (${identity.identityCode})`"
          :value="identity.id"
        />
      </el-select>
      <el-select
        v-model="item.roleIds"
        placeholder="请选择角色"
        clearable
        class="identity-select"
        :disabled="!item.identityId"
        multiple
      >
        <el-option
          v-for="role in roleOptions"
          :key="role.value"
          :label="role.label"
          :value="role.value"
        />
      </el-select>
      
      <!-- 删除按钮容器 -->
      <div class="delete-btn-wrapper">
        <el-button
          v-if="identityList.length > 1"
          icon="Delete"
          type="text"
          class="delete-btn"
          @click="removeIdentity(index)"
        />
      </div>
    </div>
    
    <!-- 新增行按钮 -->
    <el-button
      type="text"
      icon="Plus"
      class="add-btn"
      @click="addIdentity"
      v-if="isMutiIdentity"
    >
      新增一行
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { computed, nextTick, onMounted, ref, watch } from 'vue';
import type { IdentityItem, RoleOption } from './types';
import { fetchList } from '/@/api/admin/identity';
import { useMessage } from '/@/hooks/message';
import { pageList } from '/@/api/admin/role';
import { Session } from '/@/utils/storage';
import { useUserInfo } from '/@/stores/userInfo';


const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const getCurTenantInfo = ()=>{
  const tenantId = ref(Session.getTenant()?.toString());
  const tenantInfos = userInfos.value.tenantInfos || []
  return tenantInfos.find(item=>{
    return item.id?.toString() === tenantId.value
  }) ||null
}
const isMutiIdentity = ref()
onMounted(()=>{
  const curTenantInfo = getCurTenantInfo()
  if(curTenantInfo){
    isMutiIdentity.value = curTenantInfo.multipleIdentities
  }
})



const roleOptions = ref<RoleOption[]>([])
const getRoleList = async () => {
  try {
    const res = await pageList({ page: 1, size: 1000 });
    const records = res?.data?.records || [];
    roleOptions.value = records.map((item: any) => ({
      label: item.roleName,
      value: item.roleId
    }));
  } catch (error) {
    // 可根据需要使用 useMessage().error('获取角色列表失败')
    console.error('获取角色列表失败', error);
    roleOptions.value = [];
  }
}


// ===========================
// 类型定义
// ===========================

/** 扩展的身份项接口，添加内部唯一标识 */
interface ExtendedIdentityItem extends IdentityItem {
  _key?: string;
}

/** 组件 Props 接口 */
interface Props {
  modelValue?: IdentityItem[];
}

/** 组件 Emits 接口 */
interface Emits {
  (e: 'update:modelValue', value: IdentityItem[]): void;
}

// ===========================
// Props & Emits
// ===========================

const props = withDefaults(defineProps<Props>(), {
  modelValue: () => [],
});

const emit = defineEmits<Emits>();

// ===========================
// 响应式数据
// ===========================

/** 身份选项列表 */
const identityOptions = ref<any[]>([]);

/** 搜索加载状态 */
const searchLoading = ref(false);

/** 内部身份列表 */
const identityList = ref<ExtendedIdentityItem[]>([{ 
  identityId: '', 
  roleIds:[],
  _key: generateKey() 
}]);

/** 防止循环更新的标志 */
const isInternalUpdate = ref(false);

// ===========================
// 工具函数
// ===========================

/**
 * 生成唯一键值
 * @returns {string} 唯一标识符
 */
function generateKey(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// ===========================
// 计算属性
// ===========================

/**
 * 获取已选择的身份ID集合
 */
const selectedIdentityIds = computed(() => {
  return identityList.value
    .map(item => item.identityId)
    .filter(id => id !== '');
});

// ===========================
// 业务逻辑函数
// ===========================

/**
 * 获取当前行可用的身份选项（排除已选择的）
 * @param {number} currentIndex - 当前行索引
 * @returns {any[]} 可用的身份选项
 */
const getAvailableIdentities = (currentIndex: number) => {
  const currentItemId = identityList.value[currentIndex]?.identityId;
  
  return identityOptions.value.filter(identity => {
    // 如果是当前行已选中的身份，保留它
    if (identity.id === currentItemId) {
      return true;
    }
    
    // 否则过滤掉已被其他行选中的身份
    return !selectedIdentityIds.value.includes(identity.id);
  });
};

/**
 * 身份变化处理
 * @param {string | number} value - 选择的身份ID
 * @param {number} index - 当前行索引
 */
const handleIdentityChange = (value: string | number, index: number) => {
  // 检查是否与其他行重复
  const isDuplicate = identityList.value.some((item, i) => 
    i !== index && item.identityId === value && value !== ''
  );
  
  if (isDuplicate) {
    // 如果重复，清空当前选择并提示
    identityList.value[index].identityId = '';
    useMessage().warning('该身份已被选择，请选择其他身份');
    return;
  }
  
  // 如果没有重复，正常处理
  identityList.value[index].identityId = value;
};

/**
 * 根据身份ID列表加载身份详细信息（用于回显）
 * @param {IdentityItem[]} selectedItems - 已选择的身份项
 */
const loadSelectedIdentities = async (selectedItems: IdentityItem[]) => {
  if (!selectedItems || selectedItems.length === 0) return;
  
  const selectedIds = selectedItems
    .map(item => item.identityId)
    .filter(id => id);
    
  if (selectedIds.length === 0) return;
  
  try {
    // 检查已选身份是否在当前选项中
    const missingIds = selectedIds.filter(id => 
      !identityOptions.value.some(option => option.id === id)
    );
    
    if (missingIds.length > 0) {
      // 如果有缺失的身份信息，需要加载
      const promises = missingIds.map(async (id) => {
        try {
          // 通过搜索API获取身份信息
          const selectedItem = selectedItems.find((item: any) => item.identityId === id);
          const response = await fetchList({
            size: 100,
            current: 1,
          });
          
          const identities = (response.data.records || response.data || []).filter((item: any) => item.identityCode !== selectedItem?.identityCode).concat({
            id: selectedItem?.identityId,
            identityCode: selectedItem?.identityCode,
            identityName: selectedItem?.identityName,
          });
          return identities.find((identity: any) => identity.id === id);
        } catch (error) {
          // Handle error silently
          return null;
        }
      });
      
      const loadedIdentities = await Promise.all(promises);
      const validIdentities = loadedIdentities.filter(identity => identity !== null);
      
      // 将加载到的身份添加到选项中，避免重复
      validIdentities.forEach((identity: any) => {
        if (!identityOptions.value.some(option => option.id === identity.id)) {
          identityOptions.value.push(identity);
        }
      });
    }
  } catch (error) {
    // Handle error silently
  }
};

/**
 * 远程搜索身份数据
 * @param {string} query - 搜索关键词
 */
const remoteSearchIdentity = async (query: string) => {
  if (query) {
    searchLoading.value = true;
    
    try {
      const response = await fetchList({
        identityName: query,
        current: 1,
        size: 100,
      });
      
      const searchResults = response.data.records || response.data || [];
      
      // 合并搜索结果和已有选项，避免重复
      const newOptions = [...identityOptions.value];
      searchResults.forEach((result: any) => {
        if (!newOptions.some(option => option.id === result.id)) {
          newOptions.push(result);
        }
      });
      
      identityOptions.value = newOptions;
    } catch (error) {
      // 搜索失败时保持现有选项
    } finally {
      searchLoading.value = false;
    }
  }
  // 不清空选项，保持已选择的身份可见
};

/**
 * 加载默认身份数据
 */
const loadDefaultIdentities = async () => {
  try {
    const response = await fetchList({
      current: 1,
      size: 100,
    });
    
    const defaultIdentities = response.data.records || response.data || [];
    
    // 合并默认身份和现有选项
    defaultIdentities.forEach((identity: any) => {
      if (!identityOptions.value.some(option => option.id === identity.id)) {
        identityOptions.value.push(identity);
      }
    });
  } catch (error) {
    // 加载失败时保持空数组
  }
};

/**
 * 添加身份行
 */
const addIdentity = () => {
  identityList.value.push({ 
    identityId: '', 
    roleIds:[],
    _key: generateKey() 
  });
};

/**
 * 删除身份行
 * @param {number} index - 要删除的行索引
 */
const removeIdentity = (index: number) => {
  identityList.value.splice(index, 1);
};

// ===========================
// 监听器
// ===========================

/**
 * 监听父组件传入的值变化
 */
watch(
  () => props.modelValue,
  async (newValue) => {
    if (isInternalUpdate.value) {
      return; // 如果是内部更新触发的，忽略
    }
    
    if (newValue && newValue.length > 0) {
      // 回显时需要确保已选身份在选项中
      await loadSelectedIdentities(newValue);
      
      identityList.value = newValue.map(item => ({
        ...item,
        _key: generateKey()
      }));
    } else {
      identityList.value = [{ identityId: '', _key: generateKey() }];
    }
  },
  { immediate: true }
);

/**
 * 监听内部数据变化，向父组件发射
 */
watch(
  identityList,
  async (newValue) => {
    // 设置内部更新标志
    isInternalUpdate.value = true;
    let filteredValue = []
    // 过滤并发射数据（移除内部标识）
    if(isMutiIdentity.value){
       filteredValue = newValue
      .filter((item) => item.identityId !== '')
      .map((item) => ({ identityId: item.identityId,roleIds:item.roleIds }));
    }else{
      if(newValue.length){
        Session.getIdentityId()
        filteredValue = [{...newValue[0],identityId:Session.getIdentityId()}]
      }
    }
    emit('update:modelValue', filteredValue);
    
    // 下一个tick重置标志
    await nextTick();
    isInternalUpdate.value = false;
  },
  { deep: true }
);

// ===========================
// 生命周期
// ===========================

/**
 * 组件挂载时加载默认数据
 */
onMounted(() => {
  loadDefaultIdentities();
  getRoleList() 
});
</script>

<style scoped lang="scss">
.identity-selector {
  width: 100%;

  .identity-row {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    &:last-of-type {
      margin-bottom: 0;
    }

    .identity-select {
      flex: 1;
      min-width: 200px;
      margin-right: 12px;
    }

    .delete-btn-wrapper {
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
    }

    .delete-btn {
      color: #f56c6c;
      padding: 0;
      width: 24px;
      height: 24px;
      min-height: 24px;

      &:hover {
        color: #f78989;
      }
    }
  }

  .add-btn {
    width: 100%;
    margin-top: 8px;
    color: var(--Color-Text-text-color-regular, #4E5969);
    padding: 0;
    font-size: 14px;
    border: 1px solid var(--el-border-color-base, #E4E7ED);
    border-radius: var(--border-radius-base, 4px);
    background: var(--Color-Fill-fill-color-light, #F5F7FA);

    &:hover { 
      color: var(--el-color-primary);
      border-color: var(--el-color-primary);
    }
  }
}
</style> 