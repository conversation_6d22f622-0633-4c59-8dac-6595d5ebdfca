<template>
    <el-dialog
      custom-class="code-editor-dialog"
      :title="title"
      :width="width"
      v-model="previewModelVisible"
      append-to-body
      destroy-on-close>
        <MonacoEditor
          v-model="currentValue"
          :language="language"
          :scrollDom="codeScrollElement"
          class="code-editor-wrapper"
          :disabled="disabled"
          :options="options"
        />
        <template #footer>
          <el-button @click="previewModelVisible = false">取消</el-button>
          <el-button v-if="!disabled" type="primary" @click="validateAndUpdateXml">保存</el-button>
        </template>
    </el-dialog>
    <el-input v-if="type === 'input'" @click.stop="inputShow" v-model="inputValue" />
    <el-input v-if="type === 'textarea'" resize="vertical" :autosize="{ minRows: 2, maxRows: 4 }" @click.stop="inputShow" v-model="inputValue" />
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed } from 'vue';
import MonacoEditor from '/@/components/MonacoEditor/index.vue';
import { ElMessage } from 'yun-design';

const emits = defineEmits(['change', 'update:modelValue'])
const props = defineProps({
  width: {
    type: String,
    default: '60%'
  },
  type: {
    type: String,
    default: ''
  },
  modelValue: {
    type: String,
    default: ''
  }
})
// const codes = defineModel()
// const code = computed({
//   get: () => props.modelValue,
//   set: (val) => {
//     // codes.value = val
//   }
// })
// 不需要双向绑定 点击确定以后 在同步更新 modelValue
const options = ref({
  lineNumbers: true, // 显示行号
  smartIndent: true, // 智能缩进
  indentUnit: 2, // 智能缩进单位为4个空格长度
  foldGutter: true, // 启用行槽中的代码折叠
  styleActiveLine: true // 显示选中行的样式
})
const previewModelVisible = ref(false)
const language = ref('xml')
const disabled = ref(false)
const needValid = ref(true)
const codeScrollElement = ref(null)
const text = ref(null)
const title = computed(() => {
  return text.value || `${disabled.value ? '预览' : '编辑'}${language.value}`
})
const currentValue = ref(props.modelValue);
const inputValue = ref(props.modelValue);
watch([() => props.modelValue, () => previewModelVisible.value], (vals) => {
  currentValue.value = vals[0] || null;
  inputValue.value = vals[0] || null;
})



watch(() => previewModelVisible.value, (val) => {
  if (val) {
    setCodeScrollElement()
  }
})

function setCodeScrollElement() {
  setTimeout(() => {
    codeScrollElement.value = document.querySelector('.code-editor-dialog .el-dialog__body');
  }, 100);
}

function validateAndUpdateXml() {
  try {
    if (language.value === 'xml' && needValid.value) {
      // 校验XML格式
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(currentValue.value, "text/xml");
      if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
        throw new Error('XML格式错误，请检查修正');
      }
    }

    emits('update:modelValue', currentValue.value)
    nextTick(() => {
      emits('change', currentValue.value)
    })
    previewModelVisible.value = false
  } catch (error) {
    ElMessage.error(error.message || 'XML格式校验失败');
  }
}

function show(rows = {}) {
  needValid.value = true
  language.value = rows.language || 'xml'
  disabled.value = rows.disabled || false
  text.value = rows.text || null;
  previewModelVisible.value = true
  nextTick(() => {
    setCodeScrollElement()
  })
}

function inputShow() {
  needValid.value = false
  language.value = 'java'
  disabled.value = false
  text.value = '脚本';
  previewModelVisible.value = true
  nextTick(() => {
    setCodeScrollElement()
  })
}

onMounted(() => {
  nextTick(() => {})
});
onUnmounted(() => {
})
defineExpose({
  show
})
</script>

<style scoped lang='scss'>

</style>