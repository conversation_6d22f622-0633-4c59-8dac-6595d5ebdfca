<template>
  <div class="project-info">
    <div class="project-content">
      <div class="project-title">{{ projectDetail?.projectName || '-' }}</div>
      <div class="project-meta">
        <span class="meta-label">采购业务类型</span><span class="meta-value">{{ projectDetail?.serviceTypeName || '-' }}</span>
        <span class="meta-label">寻源方式</span><span class="meta-value">{{ sourcingTypeName || '-' }}</span>
        <span class="meta-label">项目负责人</span><span class="meta-value">{{ projectLeaderName || '-' }}</span>
        <span class="meta-label">采购部门</span><span class="meta-value">{{ projectDetail?.buyerDeptName || '-' }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted } from 'vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores/bidding-store';
import { useDict } from '@/hooks/dict';
import { getUserListApi } from '@/api/purchasing/proposal';

const biddingStore = useBiddingStore();
const projectDetail = computed(() => biddingStore.projectDetail);

// 直接获取响应式字典对象
const dicts = useDict('plan_recruit_method');

// 动态获取寻源方式名称
const sourcingTypeName = computed(() => {
  const dictArr = dicts['plan_recruit_method']?.value || [];
  const value = projectDetail.value?.sourcingType;
  return dictArr.find((i: any) => i.value === value)?.label || '-';
});

// 用户列表
const userList = ref<{ label: string; value: string; deptId?: string; deptName?: string }[]>([]);

// 获取用户列表
const getUserList = async () => {
  try {
    const { data } = await getUserListApi();
    userList.value = data.records.map(i => {
      return {
        label: i.username,
        value: i.userId,
        deptId: i.deptId,
        deptName: i.deptName,
      }
    })
    // console.log(userList, 'userList')
  } catch (error) {
    console.error('获取用户失败:', error);
  }
}

onMounted(() => {
  getUserList();
});

const projectLeaderName = computed(() => {
  const members = projectDetail.value?.projectMemberList || [];
  const leader = members.find((m: any) => m.role === 'PROJECT_LEADER');
  if (!leader?.userId) return '-';
  const user = userList.value.find(u => u.value === leader.userId);
  return user?.label || leader.userId || '-';
});
</script>

<style lang="scss" scoped>
.project-info {
  padding: 20px 12px 0;
  background: #f1f2f5;
  .project-content {
    background: #fff;
    border-radius: 4px;
    padding: 20px;
    .project-title {
      margin-bottom: 16px;
      padding: 12px;
      width: fit-content;
      background: linear-gradient(90deg, #FAF4FF 0%, #F4FAFF 100%);
      color: #170961;
      font-family: "PingFang SC";
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
    }
    .project-meta {
      display: flex;
      flex-wrap: wrap;
      font-size: 14px;
      color: #666;
      align-items: center;
      .meta-label {
        display: inline-block;
        background: #F0F2F5;
        color: #4E5969;
        border-radius: 2px;
        padding: 4px 6px;
        margin-right: 6px;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
      }
      .meta-value {
        margin-right: 16px;
        font-weight: 600;
      }
    }
  }
}
</style> 