<template>
	<el-select
		ref="selectRef"
		v-model="modelValue"
		:multiple="multiple"
		filterable
		remote
		:remote-method="handleRemoteSearch"
		:loading="isLoading"
		:clearable="true"
		:reserve-keyword="true"
		:loading-text="'加载中...'"
		:placeholder="placeholder"
		:popper-class="uniqueId"
		v-bind="$attrs"
		class="w-full"
		@visible-change="onDropdownVisibleChange"
	>
		<el-option
			v-for="item in options"
			:key="item.value"
			:label="item.value"
			:value="item.value"
			:disabled="disabledNames.includes(item.value)"
		>
			<div class="option-row">
				<span class="option-value">{{ item.value }}</span>
				<span class="option-label">{{ item.label }}</span>
			</div>
		</el-option>
	</el-select>
</template>

<script setup lang="ts">
import { ref, watch, computed, nextTick } from 'vue';
import { fetchList } from '/@/api/gen/table';

interface TableOption {
	value: string;
	label: string;
	raw: any;
}

const props = defineProps({
	modelValue: {
		type: [String, Array],
		default: '',
	},
	multiple: {
		type: Boolean,
		default: false,
	},
	tableName: {
		type: String,
		default: '',
	},
	placeholder: {
		type: String,
		default: '请选择表',
	},
	disabledNames: {
		type: Array,
		default: () => [],
	},
});

const emit = defineEmits(['update:modelValue', 'change']);
const options = ref<TableOption[]>([]);
const isLoading = ref(false);
const total = ref(0);
const current = ref(1);
const size = ref(20);
const searchTableName = ref(props.tableName);
const selectRef = ref();
const uniqueId = `table-select-dropdown-${Math.random().toString(36).slice(2, 10)}`;

const modelValue = computed({
	get: () => props.modelValue,
	set: (v) => {
		emit('update:modelValue', v);
		emit('change', v);
	},
});

watch(
	() => props.tableName,
	(val) => {
		searchTableName.value = val;
		current.value = 1;
		options.value = [];
		fetchOptions();
	},
	{ immediate: true }
);

async function fetchOptions(isAppend = false) {
	isLoading.value = true;
	try {
		const params = {
			dsName: 'master',
			tableName: searchTableName.value,
			current: current.value,
			size: size.value,
		};
		const res = await fetchList(params);
		const records = res?.data?.records || [];
		total.value = res?.data?.total || 0;
		const newOptions = records.map((item: any) => ({
			value: item.metadata?.TABLE_NAME,
			label: item.metadata?.TABLE_COMMENT,
			raw: item,
		}));
		if (isAppend) {
			const exist = new Set(options.value.map((o: TableOption) => o.value));
			options.value = [...options.value, ...newOptions.filter((o: TableOption) => !exist.has(o.value))];
		} else {
			options.value = newOptions;
		}
	} finally {
		isLoading.value = false;
	}
}

function handleRemoteSearch(query: string) {
	searchTableName.value = query;
	current.value = 1;
	fetchOptions();
}

function getDropdownWrap(): HTMLElement | null {
	return document.querySelector(`.${uniqueId} .el-select-dropdown__wrap`) || document.querySelector(`.${uniqueId} .el-scrollbar__wrap`);
}

function onDropdownVisibleChange(visible: boolean) {
	if (visible) {
		nextTick(() => {
			const dropdown = getDropdownWrap();
			if (dropdown) {
				dropdown.addEventListener('scroll', handleDropdownScroll);
				if (!modelValue.value || (Array.isArray(modelValue.value) && modelValue.value.length === 0)) {
					dropdown.scrollTop = 0;
				}
			}
		});
	} else {
		const dropdown = getDropdownWrap();
		if (dropdown) {
			dropdown.removeEventListener('scroll', handleDropdownScroll);
		}
	}
}

function handleDropdownScroll(e: Event) {
	const target = e.target as HTMLElement;
	// 增大触发阈值，且不做防抖
	if (target.scrollTop + target.clientHeight >= target.scrollHeight - 20 && options.value.length < total.value && !isLoading.value) {
		current.value += 1;
		fetchOptions(true);
	}
}
</script>

<style scoped>
.option-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}
.option-value {
	color: #64748b;
	font-size: 12px;
	flex: 1 1 0;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.option-label {
	color: #222;
	font-size: 15px;
	margin-left: 1rem;
	flex: 1 1 0;
	text-align: right;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
.dark .option-value {
	color: #a3a3a3;
}
.dark .option-label {
	color: #fff;
}
</style>
<style>
/* 选中项高亮为 primary，适配 .selected class */
.el-select-dropdown__item.selected .option-row,
.el-select-dropdown__item.selected .option-value,
.el-select-dropdown__item.selected .option-label {
	color: var(--el-color-primary) !important;
}
</style>
