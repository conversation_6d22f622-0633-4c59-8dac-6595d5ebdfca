<template>
  <div class="user-select">
    <splitpanes>
      <pane size="35">
        <div class="layout-padding-auto layout-padding-view">
          <el-scrollbar>
            <query-tree
              :placeholder="$t('common.queryDeptTip')"
              :query="deptData.queryList"
              :show-expand="true"
              ref="treeRef"
              @node-click="handleNodeClick"
            >
              <!-- 没有数据权限提示 -->
              <template #default="{ node, data }">
                <el-tooltip
                  v-if="data.isLock"
                  class="item"
                  effect="dark"
                  :content="$t('sysuser.noDataScopeTip')"
                  placement="right-start"
                >
                  <span
                    >{{ node.label }}
                    <SvgIcon name="ele-Lock" />
                  </span>
                </el-tooltip>
                <span v-if="!data.isLock">{{ node.label }}</span>
              </template>
            </query-tree>
          </el-scrollbar>
        </div>
      </pane>
      <pane class="ml8">
        <div class="layout-padding-auto layout-padding-view">
          <el-row v-show="showSearch">
            <el-form
              ref="queryRef"
              :inline="true"
              :model="state.queryForm"
              @keyup.enter="getDataList"
            >
              <el-form-item :label="$t('sysuser.username')" prop="username">
                <el-input
                  v-model="state.queryForm.username"
                  :placeholder="$t('sysuser.inputUsernameTip')"
                  clearable
                />
              </el-form-item>
              <el-form-item>
                <el-button icon="Search" type="primary" @click="getDataList">{{
                  $t('common.queryBtn')
                }}</el-button>
                <el-button icon="Refresh" @click="resetQuery">{{
                  $t('common.resetBtn')
                }}</el-button>
              </el-form-item>
            </el-form>
          </el-row>
          <el-table
            v-loading="state.loading"
            :data="state.dataList"
            height="440px"
            ref="tableRef"
            @select-all="handleSelectAllChange"
            @select="handleSelectChange"
            row-key="userId"
            border
            :cell-style="tableStyle.cellStyle"
            :header-cell-style="tableStyle.headerCellStyle"
          >
            <el-table-column :selectable="handleSelectable" type="selection" width="40" />
            <el-table-column
              :label="$t('sysuser.index')"
              type="index"
              width="60"
              fixed="left"
            />
            <el-table-column
              :label="$t('sysuser.username')"
              prop="username"
              fixed="left"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('sysuser.name')"
              prop="name"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              :label="$t('sysuser.phone')"
              prop="phone"
              show-overflow-tooltip
            ></el-table-column>
          </el-table>
          <pagination
            v-bind="state.pagination"
            small
            layout="prev, pager, next"
            @current-change="currentChangeHandle"
            @size-change="sizeChangeHandle"
          >
          </pagination>
        </div>
      </pane>
    </splitpanes>
  </div>
</template>

<script lang="ts" setup>
import { deptTree } from '/@/api/admin/dept';
import { pageList } from '/@/api/admin/user';
import { BasicTableProps, useTable } from '/@/hooks/table';
import { useUserInfo } from '/@/stores/userInfo';

// 动态引入组件
const QueryTree = defineAsyncComponent(() => import('/@/components/QueryTree/index.vue'));

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true,
  },
  selectSelf: {
    type: Boolean,
    default: true,
  },
  disabledUserIds: {
    type: Array,
    default: () => [],
  },
  enableIdentityFilter: {
    type: Boolean,
    default: false,
  },
});
const list = defineModel<any>();
const tableRef = ref();

const queryRef = ref();
const showSearch = ref(true);
const treeRef = ref();
const currentNode = ref(null);

// 定义表格查询、后台调用的API
const state: BasicTableProps = reactive<BasicTableProps>({
  queryForm: {
    deptId: '',
    username: '',
    phone: '',
  },
  pageList: pageList,
  enableIdentityFilter: props.enableIdentityFilter,
});

const { getDataList, currentChangeHandle, sizeChangeHandle, tableStyle } = useTable(
  state
);

// 部门树使用的数据
const deptData = reactive({
  queryList: (name: String) => {
    return deptTree({
      deptName: name,
    });
  },
});

// 清空搜索条件
const resetQuery = () => {
  queryRef.value?.resetFields();
  state.queryForm.deptId = '';
  currentNode.value = null;
  getDataList();
  treeRef.value?.resetTree();
};
// 点击树
const handleNodeClick = (e: any) => {
  state.queryForm.deptId = e.id;
  currentNode.value = e;
  getDataList();
};

// 是否可以多选
const handleSelectable = (row: any) => {
  if (props.selectSelf) return row.lockFlag === '0';
  return (
    row.lockFlag === '0' &&
    row.userId !== useUserInfo().userInfos.user.userId &&
    !props.disabledUserIds.includes(row.userId)
  );
};
function updateSelectedList() {
  nextTick(() => {
    state.dataList?.forEach((row) => {
      const isSelected = list.value.some((item) => item.id === row.userId);
      tableRef.value.toggleRowSelection(row, isSelected);
    });
  });
}
watch(
  [() => list.value, () => state.dataList],
  () => {
    updateSelectedList();
  },
  { immediate: true, deep: true }
);
const changeEvent = (row) => {
  list.value = list.value.filter((item) => item.id !== row.id);
};
const listMap = computed(() => {
  return list.value.reduce((acc, item) => {
    acc[item.id] = item;
    return acc;
  }, {});
});
// 全选事件
const handleSelectAllChange = (selection: any) => {
  const isChecked = selection.length > 0;
  if (!props.multiple) {
    list.value = isChecked
      ? [
          {
            id: selection[0].userId,
            name: selection[0].username,
            type: 'user',
            avatar: selection[0].avatar,
            realName: selection[0].name,
          },
        ]
      : [];
    return;
  }
  if (!list.value) {
    list.value = [];
  }
  if (isChecked) {
    // 把selection数据去重放入list
    selection.forEach((item: any) => {
      const isExist = listMap.value[item.userId];
      if (!isExist) {
        list.value.push({
          id: item.userId,
          name: item.username,
          type: 'user',
          avatar: item.avatar,
          realName: item.name,
        });
      }
    });
  } else {
    // 将表格内所有数据清空
    list.value = list.value.filter((item) => {
      return !state.dataList.some((row: any) => row.userId === item.id);
    });
  }
};
// 多选事件
const handleSelectChange = (objs: { userId: string }[], row: any) => {
  const isChecked = objs.some((item: { userId: string }) => item.userId === row.userId);
  if (!props.multiple) {
    list.value = isChecked
      ? [
          {
            id: row.userId,
            name: row.username,
            type: 'user',
            avatar: row.avatar,
            realName: row.name,
          },
        ]
      : [];
    return;
  }
  if (!list.value) {
    list.value = [];
  }
  if (isChecked) {
    list.value.push({
      id: row.userId,
      name: row.username,
      type: 'user',
      avatar: row.avatar,
      realName: row.name,
    });
  } else {
    list.value = list.value.filter((item) => item.id !== row.userId);
  }
};
defineExpose({
  changeEvent,
});
</script>
<style lang="scss">
.user-select {
  .el-input__validateIcon {
    display: none !important;
  }
}
</style>

<style scoped lang="scss">
.user-select {
  height: 100%;
  background-color: #fff;
  width: 730px;
  :deep(.splitpanes__pane) {
    // overflow: auto;
    height: 100%;
  }
}
</style>
