<template>
  <el-dialog
    :title="$t('orgSelecotr.select') + $t(`orgSelecotr.${props.type}`)"
    v-model="visibleDialog"
    :width="['user', 'org'].includes(type) ? 1000 : 600"
    append-to-body
    destroy-on-close
    class="promoter_person"
  >
    <div
      class="person_body clear"
      :class="{ person_body_user: ['user', 'org'].includes(type) }"
    >
      <div class="person_tree">
        <userSelect
          class="userSelectRef"
          v-if="type === 'user'"
          ref="userSelectRef"
          :selectSelf="selectSelf"
          :multiple="multiple"
          v-model="selectedList"
          :disabledUserIds="disabledUserIds"
          :enableIdentityFilter="enableIdentityFilter"
        />
        <deptSelect
          class="deptSelectRef"
          v-else-if="type === 'dept'"
          ref="deptSelectRef"
          :multiple="multiple"
          :selectSelf="selectSelf"
          v-model="selectedList"
        />
        <orgSelect
          class="orgSelectRef"
          v-else-if="type === 'org'"
          ref="orgSelectRef"
          :selectSelf="selectSelf"
          :multiple="multiple"
          v-model="selectedList"
        />
        <identitySelect
          class="identitySelectRef"
          v-else-if="type === 'identity'"
          ref="identitySelectRef"
          :multiple="!props.identitySingle"
          :disabled-ids="props.disabledIdentityIds"
          :filterable="!!props.identityFilter"
          v-model="selectedList"
        />
        <selectBox
          v-else
          ref="selectBoxRef"
          :selectSelf="selectSelf"
          :list="list"
          :multiple="multiple"
          v-model:selectedList="selectedList"
          :type="type"
        />
      </div>
      <selectResult :total="total" @del="delList" :list="resList" />
    </div>
    <template #footer>
      <el-button @click="$emit('update:visible', false)">{{
        $t('common.cancelButtonText')
      }}</el-button>
      <el-button type="primary" @click="saveDialog">{{
        $t('common.confirmButtonText')
      }}</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import selectBox from './selectBox.vue';
import selectResult from './selectResult.vue';
import deptSelect from './deptSelect.vue';
import userSelect from './userSelect.vue';
import orgSelect from './orgSelect.vue';
import identitySelect from './identitySelect.vue';
import { computed, watch, ref, onMounted } from 'vue';
import { departments, searchVal } from './common';
import other from '/@/utils/other';
import { useI18n } from 'vue-i18n';

const selectBoxRef = ref();
const userSelectRef = ref();
const deptSelectRef = ref();
const orgSelectRef = ref();
const identitySelectRef = ref();

let props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  data: {
    type: Array,
    default: () => [],
  },
  type: {
    type: String,
    default: 'user',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  selectSelf: {
    type: Boolean,
    default: true,
  },
  disabledUserIds: {
    type: Array,
    default: () => [],
  },
  disabledIdentityIds: {
    type: Array,
    default: () => [],
  },
  identityFilter: {
    type: String,
    default: '',
  },
  identitySingle: {
    type: Boolean,
    default: false,
  },
  enableIdentityFilter: {
    type: Boolean,
    default: false,
  },
});
//已选择的集合
let selectedList = ref([]);

let emits = defineEmits(['update:visible', 'change']);
let visibleDialog = computed({
  get() {
    return props.visible;
  },
  set() {
    closeDialog();
  },
});
const isChecked = (id, type) => {
  return (
    selectedList.value.filter((res) => res.id === id && res.type === type).length > 0
  );
};

let list = computed(() => {
  let value = departments.value;
  return [
    {
      type: 'dept',
      data: value === undefined ? [] : value.childDepartments,
    },
    {
      type: 'role',
      data: value === undefined ? [] : value.roleList,
    },
    {
      type: 'user',
      data: value === undefined ? [] : value.employees,
      change: (item) => {
        if (!isChecked(item.id, item.type)) {
          if (!props.multiple) {
            //单选
            selectedList.value = [];
          }

          selectedList.value.push(item);
        } else {
          selectedList.value = selectedList.value.filter(
            (res) => !(res.id === item.id && res.type === item.type)
          );
        }
      },
    },
  ];
});
const currentDom = computed(() => {
  if (props.type === 'user') {
    return userSelectRef.value;
  } else if (props.type === 'dept') {
    return deptSelectRef.value;
  } else if (props.type === 'org') {
    return orgSelectRef.value;
  } else if (props.type === 'identity') {
    return identitySelectRef.value;
  } else {
    return selectBoxRef.value;
  }
});
let resList = computed(() => {
  let userData = selectedList.value.filter((res) => res.type === 'user');
  let deptData = selectedList.value.filter((res) => res.type === 'dept');
  let roleData = selectedList.value.filter((res) => res.type === 'role');
  let identityData = selectedList.value.filter((res) => res.type === 'identity');
  console.log(selectedList.value,'234')
  let data = [
    {
      type: 'user',
      data: userData,
      cancel: (item) => {
        item.selected = false;
        currentDom.value.changeEvent(item);
      },
    },
  ];
  if (props.type === 'org' || props.type === 'dept') {
    data.unshift({
      type: 'dept',
      data: deptData,
      cancel: (item) => {
        item.selected = false;
        currentDom.value.changeEvent(item);
      },
    });
  }
  if (props.type === 'role') {
    data.unshift({
      type: 'role',
      data: roleData,
      cancel: (item) => {
        item.selected = false;
        selectBoxRef.value.changeEvent(item);
      },
    });
  }
  if (props.type === 'identity') {
    data.unshift({
      type: 'identity',
      data: identityData,
      cancel: (item) => {
        item.selected = false;
        currentDom.value.changeEvent(item);
      },
    });
  }
  return data;
});

watch(
  () => props.visible,
  (val) => {
    if (val) {
      selectedList.value = [...props.data];
      searchVal.value = '';
    }
  }
);

const closeDialog = () => {
  emits('update:visible', false);
};

let total = computed(() => {
  let v = departments.value;
  if (!v) {
    return 0;
  }
  return selectedList.value.length;
});

const { proxy } = getCurrentInstance();

let saveDialog = () => {
  const v = selectedList.value;

  let checkedList = other.deepClone(v).map((item) => ({
    type: item.type,
    id: item.id,
    name: item.name,
    avatar: item.avatar,
    realName: item.realName,
  }));
  emits('change', checkedList);
  //selectedList.value=[]
};
const delList = () => {
  for (const item of other.deepClone(selectedList.value)) {
    item.selected = false;
    currentDom.value.changeEvent(item);
  }
  selectedList.value = [];
};
</script>
<style scoped lang="scss">
@import './dialog.scss';
.userSelectRef {
  :deep(.el-tree) {
    height: 489px;
    overflow: auto;
  }
}
.deptSelectRef {
  :deep(.el-tree) {
    height: 489px;
    overflow: auto;
  }
}
.orgSelectRef {
  :deep(.el-tree) {
    height: 550px;
    overflow: auto;
  }
}
.identitySelectRef {
  :deep(.el-tree) {
    height: 489px;
    overflow: auto;
  }
}

:deep(.el-dialog__body) {
  height: 600px;
}
</style>
