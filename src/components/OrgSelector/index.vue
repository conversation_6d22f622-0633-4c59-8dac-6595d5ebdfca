<!--
 * @Author: chenting<PERSON> <EMAIL>
 * @Date: 2025-06-05 13:54:33
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-06-10 17:14:20
 * @FilePath: \fe-dcrg-admin\src\components\OrgSelector\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div>
    <employees-dialog
      v-model:visible="selectUserDialogVisible"
      :data="defaultValue"
      :type="type"
      :multiple="multiple"
      :selectSelf="selectSelf"
      :disabledUserIds="disabledUserIds"
      :enableIdentityFilter="enableIdentityFilter"
      @change="afterSelectUser"
    />
  </div>
  <el-button
    :disabled="disabled"
    size="large"
    plain
    @click="selectUserDialogVisible = true"
  >
    <div class="flex gap-[4px] justify-center items-center">
      <CirclePlus class="text-[#1677FF] w-4 h-4" />
      <div class="text-[14px] leading-[22px] font-normal text-[#1677FF]">
        {{ props.text }}
      </div>
    </div>
  </el-button>
  <div style="width: 100%; margin-top: 10px; text-align: left">
    <org-item v-model:data="defaultValue" :disabled="disabled" />
  </div>
</template>

<script setup lang="ts">
import { CirclePlus } from '@yun-design/icons-vue';
import employeesDialog from './employeesDialog.vue';
import orgItem from './orgItem.vue';

let selectUserDialogVisible = ref(false);
const afterSelectUser = (data) => {
  //选择人员变化
  selectUserDialogVisible.value = false;
  emits('update:orgList', data);
};

var defaultValue = computed({
  get: () => {
    return props.orgList || [];
  },
  set: (r) => {
    emits('update:orgList', r);
  },
});
let emits = defineEmits(['update:orgList']);

let props = defineProps({
  text: {
    type: String,
    default: '',
    required: false,
  },
  orgList: {
    type: Array,
    default: () => [],
  },

  type: {
    type: String,
    default: 'user',
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  selectSelf: {
    type: Boolean,
    default: true,
  },
  disabledUserIds: {
    type: Array,
    default: () => [],
  },
  enableIdentityFilter: {
    type: Boolean,
    default: false,
  },
});
</script>
