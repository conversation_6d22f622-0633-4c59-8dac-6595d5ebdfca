<template>
  <div class="identity-select">
    <div class="layout-padding-auto layout-padding-view">
      <el-scrollbar>
        <el-tree
          :data="data"
          show-checkbox
          node-key="id"
          default-expand-all
          check-strictly
          :default-checked-keys="defaultCheckedKeys"
          ref="treeRef"
          :props="defaultProps"
          @check-change="onChange"
        >
          <template #default="{ data }">
            <span>{{ data.identityName }}</span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { fetchListTree } from '/@/api/admin/identity';
import { ref, computed, watch, nextTick } from 'vue';

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true,
  },
});
const list = defineModel<any>();
const treeRef = ref();
const data = ref([]);
const defaultProps = {
  label: 'identityName',
  children: 'children',
  value: 'id',
};
const defaultCheckedKeys = computed(() => {
  return list.value.map((item: any) => item.id);
});

async function getIdentityList() {
  const res = await fetchListTree();
  data.value = res.data || [];
}

const onChange = (row: any, checked: boolean) => {
  if (!props.multiple) {
    if (checked) {
      list.value = [
        {
          id: row.id,
          identityName: row.identityName,
          type: 'identity',
        },
      ];
    } else {
      list.value = list.value.filter((item: any) => item.id !== row.id);
    }
    return;
  }
  if (!list.value) {
    list.value = [];
  }
  if (checked) {
    list.value.push({
      id: row.id,
      name: row.identityName,
      type: 'identity',
    });
  } else {
    list.value = list.value.filter((item: any) => item.id !== row.id);
  }
};

getIdentityList();

watch(
  [() => list.value, () => data.value],
  () => {
    treeRef.value?.setCheckedKeys(list.value.map((item: any) => item.id));
  },
  { immediate: true, deep: true }
);

let tempArr: any[] = [];
const changeEvent = (row: any) => {
  if (!tempArr.length) {
    tempArr = [...list.value];
  }
  tempArr = tempArr.filter((item: any) => item.id !== row.id);
  nextTick(() => {
    list.value = [...tempArr];
  });
};
defineExpose({
  changeEvent,
});
</script>

<style scoped lang="scss">
.identity-select {
  height: 100%;
  background-color: #fff;
  width: 262px;
  :deep(.splitpanes__pane) {
    height: 100%;
  }
}
</style> 