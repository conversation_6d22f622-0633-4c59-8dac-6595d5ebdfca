<template>
  <div class="dept-select">
    <div class="layout-padding-auto layout-padding-view">
      <el-scrollbar>
        <el-tree
          :data="data"
          show-checkbox
          node-key="id"
          default-expand-all
          check-strictly
          :default-checked-keys="defaultCheckedKeys"
          ref="treeRef"
          :props="defaultProps"
          @check-change="onChange"
        >
          <template #default="{ data }">
            <el-tooltip
              v-if="data.isLock"
              class="item"
              effect="dark"
              :content="$t('sysuser.noDataScopeTip')"
              placement="right-start"
            >
              <span
                >{{ data.name }}
                <SvgIcon name="ele-Lock" />
              </span>
            </el-tooltip>
            <span v-if="!data.isLock">{{ data.name }}</span>
          </template>
        </el-tree>
      </el-scrollbar>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { deptTree } from '/@/api/admin/dept';

const props = defineProps({
  multiple: {
    type: Boolean,
    default: true,
  },
});
const list = defineModel<any>();
const treeRef = ref();
const data = ref([]);
const defaultProps = {
  label: 'name',
  children: 'children',
  value: 'id',
  disabled: 'isLock',
};
const defaultCheckedKeys = computed(() => {
  return list.value.map((item) => item.id);
});
async function getDeptList(name: string = '') {
  const res = await deptTree({ deptName: name });
  data.value = res.data;
}
const onChange = (row, checked) => {
  if (!props.multiple) {
    if (checked) {
      list.value = [
        {
          id: row.id,
          name: row.name,
          type: 'dept',
          avatar: row.avatar,
        },
      ];
    } else {
      list.value = list.value.filter((item) => item.id !== row.id);
    }
    return;
  }
  if (!list.value) {
    list.value = [];
  }
  if (checked) {
    list.value.push({
      id: row.id,
      name: row.name,
      avatar: row.avatar,
      type: 'dept',
    });
  } else {
    list.value = list.value.filter((item) => item.id !== row.id);
  }
};
getDeptList();
watch(
  [() => list.value, () => data.value],
  () => {
    treeRef.value?.setCheckedKeys(list.value.map((item) => item.id));
  },
  { immediate: true, deep: true }
);
let tempArr: any[] = [];
const changeEvent = (row) => {
  if (!tempArr.length) {
    tempArr = [...list.value];
  }
  tempArr = tempArr.filter((item) => item.id !== row.id);
  nextTick(() => {
    // tempArr = [];
    list.value = [...tempArr];
  });
};
defineExpose({
  changeEvent,
});
</script>

<style scoped lang="scss">
.user-select {
  height: 100%;
  background-color: #fff;
  width: 400px;
  :deep(.splitpanes__pane) {
    // overflow: auto;
    height: 100%;
  }
}
</style>
