$d-type: (
	flex: flex,
	block: block,
	none: none,
);

$flex-jc: (
	start: flex-start,
	end: flex-end,
	center: center,
	between: space-between,
	around: space-around,
);

$flex-ai: (
	start: flex-start,
	end: flex-end,
	center: center,
	stretch: stretch,
);

//spacing
$spacing-types: (
	m: margin,
	p: padding,
);

$spacing-directions: (
	t: top,
	r: right,
	b: bottom,
	l: left,
);

$spacing-base-size: 5px;

$spacing-sizes: (
	0: 0,
	1: 1,
	2: 2,
	3: 3,
	4: 4,
	5: 5,
);

@each $key, $value in $d-type {
	.d-#{$key} {
		display: $value;
	}
}

.flex-column {
	flex-direction: column;
}

.text-ellipsis {
	display: inline-block;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.w-100 {
	width: 100%;
}

.h-100 {
	height: 100%;
}

.flex-wrap {
	flex-wrap: wrap;
}

.flex-grow-1 {
	flex: 1;
}

@each $dir in(top, bottom, right, left) {
	.border-#{$dir} {
		border-#{$dir}: 1px solid;
	}
}

@each $key, $value in $flex-jc {
	.jc-#{$key} {
		justify-content: $value;
	}
}

@each $key, $value in $flex-ai {
	.ai-#{$key} {
		align-items: $value;
	}
}
//text
@each $var in (left, center, right) {
	.text-#{$var} {
		text-align: $var !important;
	}
}

@each $typeKey, $type in $spacing-types {
	@each $sizeKey, $size in $spacing-sizes {
		.#{$typeKey}-#{$sizeKey} {
			#{$type}: $size * $spacing-base-size;
		}
	}

	@each $sizeKey, $size in $spacing-sizes {
		.#{$typeKey}x-#{$sizeKey} {
			#{$type}-left: $size * $spacing-base-size;
			#{$type}-right: $size * $spacing-base-size;
		}

		.#{$typeKey}y-#{$sizeKey} {
			#{$type}-top: $size * $spacing-base-size;
			#{$type}-bottom: $size * $spacing-base-size;
		}
	}

	@each $directionKey, $direction in $spacing-directions {
		@each $sizeKey, $size in $spacing-sizes {
			.#{$typeKey}#{$directionKey}-#{$sizeKey} {
				#{$type}-#{$direction}: $size * $spacing-base-size;
			}
		}
	}
}
