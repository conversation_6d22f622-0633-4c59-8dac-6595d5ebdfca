<script setup lang="ts">
import { ElUpload } from 'element-plus';
import { defineEmits, defineProps } from 'vue';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => [],
  },
  beforeUpload: Function,
  onExceed: Function,
  onChange: Function,
  onSuccess: Function,
  onError: Function,
  onRemove: Function,
  limit: {
    type: Number,
    default: 10,
  },
  multiple: {
    type: Boolean,
    default: true,
  },
  tip: {
    type: String,
    default: '最多上传10个文件，单个文件文件不超过10M',
  },
  keyValue: {
    type: String,
    default: '',
  },
  action: {
    type: String,
    default: `${import.meta.env.VITE_SAAS_PROXY_PATH}admin/sys-file/upload`,
  },
});

const emit = defineEmits(['update:modelValue']);

const fileList = ref([]);

const handleChange = ( list: any[]) => {
  const  fileListData= list.map((e) => {
    const fileData = {
      url: e.response?.data?.url ? `${import.meta.env.VITE_SAAS_PROXY_PATH.slice(0, -1)}${e.response?.data?.url}` : e.url,
      name: e.name || e.response?.data?.name,
      uuid: e.uid,
    }
    return fileData
  }).filter((e) => e.url);
  console.log('fileListData', fileListData);
  emit('update:modelValue', fileListData);
}

function onSuccess(response, file, list) {
  handleChange(list);
}

const handleRemove = (file, list) => {
  handleChange(list);
};
const handleDownload = (file) => {
  window.open(file.url);
};
</script>

<template>
  <ElUpload
    :file-list="fileList"
    :action="action" 
    :on-success="onSuccess" 
    :on-remove="handleRemove"
    :download-image="handleDownload" 
    :before-upload="props.beforeUpload"
    :on-exceed="props.onExceed"
    :on-change="(file: any, fileList: any[]) => { props.onChange && props.onChange(file, fileList); }"
    :limit="props.limit"
    :multiple="props.multiple"
    :tip="props.tip"
    :key="props.keyValue"
>
  <div style="text-align: left;">
    <el-button size="small" type="primary">点击上传</el-button>
  </div>
  <div class="el-upload__tip">{{props.tip}}</div>
</ElUpload>
</template> <style lang="scss">
.el-upload-list__item-name {
  display: flex;
  align-items: center;
}
.el-progress__text {
  top: -1.5em !important;
}
</style>