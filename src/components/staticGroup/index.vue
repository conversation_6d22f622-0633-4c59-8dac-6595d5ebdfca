<template>
  <div class="static-group_header">
    <div class="static-group_title prelint">
      <div class="static-group_title_style"></div>
      {{ textContent }}
    </div>
  </div>
</template>

<script>
export default {
  name: 'static-group',
  props: {
    textContent: {
      type: String,
      default: '',
    },
  },
};
</script>

<style lang="scss" scoped>
.static-group_header {
  height: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 15px;
  padding-bottom: 13px;
  box-sizing: content-box;
}

.static-group_title {
  color: var(--el-text-color-primary);
  font-weight: 600;
  font-size: 16px;
  white-space: nowrap;
  line-height: 24px;
}

.static-group_title.prelint {
  display: flex;
  align-items: center;
}

.static-group_title_style {
  height: 16px;
  width: 3px;
  margin-right: 8px;
  background-color: var(--el-color-primary);
}
</style>
