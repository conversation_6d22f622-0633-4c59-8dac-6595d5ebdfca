<template>
  <div class="process-design"  :class="{ expand: isExpand }">
    <my-process-designer
      v-model="xmlString"
      keyboard
      ref="processDesigner"
      :events="[
        'element.click',
        'connection.added',
        'connection.removed',
        'connection.changed'
      ]"
      @element-click="elementClick"
      @init-finished="initModeler"
      @element-contextmenu="elementContextmenu"
      @save="onSaveProcess"
      :process-id="designerForm.form.processKey"
      :process-name="designerForm.form.processName"
    />

    <!-- 展开收缩属性面板 -->
    <div class="process-container-header">
      <el-icon
        v-if="isExpand"
        @click="toggleExpand(false)"
        ><Fold
      /></el-icon>
      <el-icon
        v-else
        @click="toggleExpand(true)"
        ><Expand
      /></el-icon>
    </div>
    <div class="process-panel-wrapper">
      <my-process-penal :bpmn-modeler="modeler" :id-edit-disabled="idEditDisabled" :scene="scene" :prefix="controlForm.prefix" class="process-panel" />
      <div class="process-check-section">
        <h3>流程校验</h3>
        <div v-if="validationErrors.length > 0" class="validation-errors">
          <el-alert
            v-for="error in validationErrors"
            :key="error.elementId"
            :title="error.message"
            :type="error.type"
            :closable="false"
            show-icon
            class="mb-2"
            @click="focusElement(error.elementId)"
          >
            <template #title>
              <span class="cursor-pointer">
                {{ error.elementName }} (ID: {{ error.elementId }}): {{ error.message }}
              </span>
            </template>
          </el-alert>
        </div>
        <el-alert
          v-else
          title="流程校验通过"
          type="success"
          :closable="false"
          show-icon
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue';
import MyProcessDesigner from '@/package/designer/ProcessDesigner.vue';
import MyProcessPenal from '@/package/penal/PropertiesPanel.vue';
import CustomContentPadProvider from '@/package/designer/plugins/content-pad/index.js';
import CustomPaletteProvider from '@/package/designer/plugins/palette/CustomPalette.js';
import { ElMessage } from 'yun-design';
import { Expand, Fold } from '@yun-design/icons-vue';

import '@/package/theme/index.scss';
import 'bpmn-js/dist/assets/diagram-js.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';

// 校验相关状态
interface ValidationError {
  elementId: string;
  elementName: string;
  message: string;
  type: 'error' | 'warning';
}

interface Props {
  bpmnXml: string;
  designerForm: {
    form: {
      processKey: string;
      processName: string;
    };
  };
  idEditDisabled?: boolean;
  scene?: string | null;
}

const props = withDefaults(defineProps<Props>(), {
  idEditDisabled: false,
  scene: null,
});

const emits = defineEmits(['save']);

const isExpand = ref(false);
const processDesigner = ref<any>(null);

// const height = ref("calc(100vh - 110px)");
const xmlString = ref<string>(props.bpmnXml);
const modeler = ref<any>(null);
const validationErrors = ref<ValidationError[]>([]); // 添加校验错误列表

interface ControlForm {
  processId: string;
  processName: string;
  simulation: boolean;
  labelEditing: boolean;
  labelVisible: boolean;
  prefix: string;
  headerButtonSize: string;
  additionalModel: any[];
}

function toggleExpand(bool: boolean) {
	isExpand.value = bool;
	setTimeout(() => {
    if (processDesigner.value) {
      // processDesigner.value.processReZoom();
      processDesigner.value.fitViewport();
    }
	}, 500);
}

const controlForm: ControlForm = reactive({
  processId: props.designerForm.form.processKey || '',
  processName: props.designerForm.form.processName || '',
  simulation: true,
  labelEditing: false,
  labelVisible: false,
  prefix: 'flowable',
  headerButtonSize: 'default',
  additionalModel: [CustomContentPadProvider, CustomPaletteProvider],
});

watch(() => props.bpmnXml, (val) => {
  xmlString.value = val;
});

// 添加标记（高亮）
const addMarker = (canvas: any, elementId: string, type: string) => {
  if (canvas && elementId && type) {
    try {
      canvas.addMarker(elementId, type);
    } catch (error) {
      // console.warn('添加标记失败', error);
    }
  }
};

// 移除标记（高亮）
const removeMarker = (canvas: any, elementId: string, type: string) => {
  if (canvas && elementId && type) {
    try {
      canvas.removeMarker(elementId, type);
    } catch (error) {
      // console.warn('移除标记失败', error);
    }
  }
};

// 聚焦到指定元素
const focusElement = (elementId: string) => {
  if (!modeler.value) return;

  const elementRegistry = modeler.value.get('elementRegistry');
  const canvas = modeler.value.get('canvas');
  const element = elementRegistry.get(elementId);

  if (element) {
    // 高亮显示目标元素
    addMarker(canvas, elementId, 'error-tip');

    // 将视图中心移动到目标元素
    canvas.centerElement(element);

    // 3秒后移除高亮
    setTimeout(() => {
      removeMarker(canvas, elementId, 'error-tip');
    }, 3000);
  }
};

// 校验流程函数
const validateProcess = () => {
  validationErrors.value = []; // 清空之前的错误
  const elementRegistry = modeler.value?.get('elementRegistry');
  const canvas = modeler.value?.get('canvas');
  if (!elementRegistry || !canvas) return;

  // 清除所有错误标记
  elementRegistry.forEach((element: any) => {
    removeMarker(canvas, element.id, 'error-tip');
  });

  // 获取所有元素
  const elements = elementRegistry.getAll();

  // 检查开始事件
  const startEvents = elements.filter((el: any) => el.type === 'bpmn:StartEvent');
  if (startEvents.length === 0) {
    validationErrors.value.push({
      elementId: 'process',
      elementName: '流程',
      message: '流程缺少开始事件',
      type: 'error'
    });
  }

  // 检查结束事件
  const endEvents = elements.filter((el: any) => el.type === 'bpmn:EndEvent');
  if (endEvents.length === 0) {
    validationErrors.value.push({
      elementId: 'process',
      elementName: '流程',
      message: '流程缺少结束事件',
      type: 'error'
    });
  }

  // 检查每个元素
  elements.forEach((element: any) => {
    const businessObject = element.businessObject;

    // 检查用户任务
    // if (element.type === 'bpmn:UserTask') {
      // const hasAssignee = businessObject.assignee ||
      //                    businessObject.candidateUsers ||
      //                    businessObject.candidateGroups;
      // if (!hasAssignee) {
      //   validationErrors.value.push({
      //     elementId: element.id,
      //     elementName: businessObject.name || element.id,
      //     message: '请选择提交人或候选人/组',
      //     type: 'error'
      //   });
      //   addMarker(canvas, element.id, 'error-tip');
      // }
    // }

    // 检查网关
    if (element.type.includes('bpmn:Gateway')) {
      const outgoing = element.outgoing || [];
      if (outgoing.length === 0) {
        validationErrors.value.push({
          elementId: element.id,
          elementName: businessObject.name || element.id,
          message: '网关缺少出口连线',
          type: 'error'
        });
        addMarker(canvas, element.id, 'error-tip');
      } else if (element.type === 'bpmn:ExclusiveGateway' && outgoing.length > 1) {
        // 检查排他网关的连线条件
        const hasMissingCondition = outgoing.some((flow: any) => {
          const condition = flow.businessObject.conditionExpression;
          return !condition || !condition.body;
        });
        if (hasMissingCondition) {
          validationErrors.value.push({
            elementId: element.id,
            elementName: businessObject.name || element.id,
            message: '排他网关的连线缺少条件表达式',
            type: 'error'
          });
          addMarker(canvas, element.id, 'error-tip');
        }
      }
    }

    // 检查连线
    if (element.type === 'bpmn:SequenceFlow') {
      const condition = businessObject.conditionExpression;
      if (condition && !condition.body) {
        validationErrors.value.push({
          elementId: element.id,
          elementName: businessObject.name || element.id,
          message: '连线条件表达式为空',
          type: 'error'
        });
        addMarker(canvas, element.id, 'error-tip');
      }
    }
  });
};

const elementClick = () => {
  // element 参数未被使用，此处无需操作
};

const initModeler = (newModeler: any) => {
  modeler.value = newModeler;
  // 检查是否存在禁用元素创建的配置
  const modeling = modeler.value.get('modeling');
  const elementFactory = modeler.value.get('elementFactory');
  // 确保模型器状态正常
  if (modeling && elementFactory) {
    // console.log('模型器功能正常');
  }else{
    // console.log('模型器功能异常');
  }
  // 监听命令栈变化，实时校验
  modeler.value.on('commandStack.changed', validateProcess);

  // TODO 并没有完全加载完成 临时添加一个定时器
  // 防止没有加载完成就开始进行校验,导致报错校验异常
  setTimeout(() => {
    // 首次加载完成进行校验
    validateProcess();
  }, 1000);
};

const elementContextmenu = () => {
};

const onSaveProcess = (saveData: any) => {
  // 在保存前进行校验
  validateProcess();
  if (validationErrors.value.length > 0) {
    ElMessage.error('流程图存在校验问题，请修正后再保存！');
    return;
  }
  emits('save', saveData);
};

defineExpose({
  getXmlString: () => xmlString.value || null,
});
</script>

<style lang="scss">
body {
  overflow: hidden;
  margin: 0;
  box-sizing: border-box;
}

@keyframes blink-red {
  0% {
    stroke: red;
    stroke-width: 2px;
  }
  50% {
    stroke: transparent;
    stroke-width: 2px;
  }
  100% {
    stroke: red;
    stroke-width: 2px;
  }
}

.error-tip {
  .djs-visual > :first-child {
    animation: blink-red 1s infinite;
    fill: rgba(252, 127, 127, 0.2) !important;
  }
}

body,
body * {
  /* 滚动条 */
  &::-webkit-scrollbar-track-piece {
    background-color: #fff; /*滚动条的背景颜色*/
    -webkit-border-radius: 0; /*滚动条的圆角宽度*/
  }

  &::-webkit-scrollbar {
    width: 10px; /*滚动条的宽度*/
    height: 8px; /*滚动条的高度*/
  }

  &::-webkit-scrollbar-thumb:vertical {
    /*垂直滚动条的样式*/
    height: 50px;
    background-color: rgba(153, 153, 153, 0.5);
    -webkit-border-radius: 4px;
    outline: 2px solid #fff;
    outline-offset: -2px;
    border: 2px solid #fff;
  }

  &::-webkit-scrollbar-thumb {
    /*滚动条的hover样式*/
    background-color: rgba(159, 159, 159, 0.3);
    -webkit-border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb:hover {
    /*滚动条的hover样式*/
    background-color: rgba(159, 159, 159, 0.5);
    -webkit-border-radius: 4px;
  }
}
.demo-control-bar {
  position: fixed;
  right: 8px;
  bottom: 48px;
  z-index: 1;
}
.open-model-button {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  font-size: 32px;
  background: rgba(64, 158, 255, 1);
  color: #ffffff;
  cursor: pointer;
}

.process-check-section {
  padding: 16px;
  background-color: var(--el-bg-color);
  border-radius: 4px;
  margin-top: 16px;
  flex-shrink: 0;
  min-height: 100px; /* 确保有一个最小高度 */

  h3 {
    margin-bottom: 16px;
    font-size: 16px;
    font-weight: 500;
  }

  .validation-errors {
    .el-alert {
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: translateX(4px);
      }
    }
  }
}

.cursor-pointer {
  cursor: pointer;
}
.process-design {
  display: flex;
  height: 100%;
  width: 100%;
  position: relative;
  .process-panel-wrapper {
    display: flex;
    flex-direction: column;
    opacity: 1;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    flex-shrink: 0;
    .process-panel {
      flex-grow: 1;
    }
  }
  .process-container-header {
    position: absolute;
    top: 50%;
    right: 442px;
    z-index: 10;
    font-size: 20px;
    cursor: pointer;
    background-color: #fff;
    padding: 4px;
    box-shadow: 0 0 8px #cccccc;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  &.expand {
    .process-panel-wrapper {
      width: 0;
      padding: 0;
      opacity: 0;
      margin: 0;
      overflow: hidden;
      transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
    .process-container-header {
      right: 6px;
    }
  }
}
</style>