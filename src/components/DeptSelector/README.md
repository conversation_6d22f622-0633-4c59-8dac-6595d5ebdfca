# DeptSelector 部门选择器

一个带有搜索功能的树形部门选择器组件，支持 v-model 双向绑定。

## 功能特性

- 🌲 树形结构展示部门层级
- 🔍 支持部门名称搜索过滤
- 📱 支持 v-model 双向绑定
- 🎨 基于 Element Plus 的 Select 和 Tree 组件
- 💪 完整的 TypeScript 类型支持

## 基础用法

```vue
<template>
  <DeptSelector 
    v-model="selectedDeptId" 
    placeholder="请选择部门"
    @change="handleDeptChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue';
import DeptSelector, { type DeptItem } from '@/components/DeptSelector';

const selectedDeptId = ref('');

function handleDeptChange(value: string, item: DeptItem) {
  console.log('选中的部门ID:', value);
  console.log('选中的部门信息:', item);
}
</script>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| modelValue | 绑定值（部门ID） | `string` | - |
| placeholder | 输入框占位文本 | `string` | `'请选择部门'` |
| disabled | 是否禁用 | `boolean` | `false` |

## Events

| 事件名 | 说明 | 参数 |
|--------|------|------|
| update:modelValue | 绑定值变化时触发 | `(value: string)` |
| change | 选中值发生变化时触发 | `(value: string, item: DeptItem)` |

## 类型定义

```typescript
interface DeptItem {
  id: string;
  name: string;
  children?: DeptItem[];
}
```

## 在表单中使用

可以直接在表单配置中使用：

```javascript
{
  prop: 'deptId',
  label: '部门',
  render: (form) => {
    return (
      <DeptSelector 
        v-model={form.deptId} 
        placeholder="请选择部门"
        onUpdate:modelValue={(val) => form.deptId = val}
        onChange={(val, item) => {
          form.deptId = val;
          form.deptName = item.name;
        }}
      />
    );
  }
}
``` 