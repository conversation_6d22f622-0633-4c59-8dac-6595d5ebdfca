<template>
  <el-select
    :model-value="modelValue"
    style="width: 100%"
    :placeholder="placeholder"
    :disabled="disabled"
    @update:model-value="handleChange"
    :multiple="multiple"
  >
    <div>
      <el-input
        v-model="filterText"
        placeholder="搜索部门"
        style="margin-bottom: 10px; margin-left: 10px; margin-right: 10px; box-sizing: border-box; width: calc(100% - 20px);"
      />
      <el-tree
        ref="deptTreeRef"
        highlight-current
        :data="treeDeptData"
        :props="{ label: 'name', value: 'id' }"
        :filter-node-method="filterNode"
        @node-click="handleNodeClick"
        @check="handleCheck"
        node-key="id"
        :show-checkbox="multiple"
        :check-strictly="checkStrictly"
      />
    </div>
    <el-option
      v-for="item in flatDept"
      :key="item.id"
      :label="item.name"
      :value="item.id"
      class="hide"
      style="display: none"
    />
  </el-select>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { deptTree } from '@/api/admin/dept';

interface DeptItem {
  id: string;
  name: string;
  children?: DeptItem[];
}

interface Props {
  modelValue?: string | string[];
  placeholder?: string;
  disabled?: boolean;
  multiple?: boolean;
  checkStrictly?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '请选择部门',
  disabled: false,
  multiple: false,
  checkStrictly: false,
});

const emit = defineEmits<{
  'update:deptNames': [value: string];
  'update:modelValue': [value: string | string[]];
  'change': [value: string | string[], item: DeptItem | undefined];
}>();

// 部门相关状态
const deptTreeRef = ref();
const treeDeptData = ref<DeptItem[]>([]);
const filterText = ref('');

// 扁平化树形数据
const getFlatTree = (list: DeptItem[], subKey: keyof DeptItem = 'children'): DeptItem[] =>
  list?.reduce((result: DeptItem[], item: DeptItem) => {
    result.push(item);
    if (item[subKey] && Array.isArray(item[subKey])) {
      result.push(...getFlatTree(item[subKey] as DeptItem[]));
    }
    return result;
  }, []) || [];

const flatDept = computed(() => getFlatTree(treeDeptData.value));

// 过滤节点方法
const filterNode = (value: string, data: DeptItem) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

// 获取所有的名称
const getDeptNames = () => {
  if (props.multiple) {
    nextTick(() => {
      const names = deptTreeRef.value?.getCheckedNodes().map((node: Record<string, any>) => node.name)
      emit('update:deptNames', names?.join(','));
    })
  }
};

// 同步向上传递修改值
const syncUpChange = (data: DeptItem) => {
  if (props.multiple) {
     // 同步更新树选择节点  默认不会变化
    // 如果选中节点存在，则删除，否则添加
    const ids = deptTreeRef.value?.getCheckedNodes().map((node: Record<string, any>) => node.id);
    let newIds: string[] = [ ...ids ];
    if (newIds?.includes(data.id)) {
      newIds = newIds?.filter((id: string) => id !== data.id);
    } else {
      newIds = [...newIds, data.id];
    }
    emit('update:modelValue', newIds);
    emit('change', newIds, data);
    deptTreeRef.value?.setCheckedKeys(newIds);
    getDeptNames();
  }
};

// 设置选中节点
const setChecked = (mode: 'all' | 'none', nodeIds?: string[]) => {
  if (mode === 'all') {
    const ids = nodeIds || flatDept.value.map((dept) => dept.id);
    deptTreeRef.value?.setCheckedKeys(ids);
    emit('update:modelValue', ids);
  } else {
    deptTreeRef.value?.setCheckedKeys(nodeIds || []);
    emit('update:modelValue', nodeIds || []);
  }
  getDeptNames();
}

// 处理节点选中
const handleCheck = (data: DeptItem) => {
  if (props.multiple) {
      const newIds = deptTreeRef.value?.getCheckedNodes().map((node: Record<string, any>) => node.id);
      emit('update:modelValue', newIds);
      emit('change', newIds, data);
      deptTreeRef.value?.setCheckedKeys(newIds);
      getDeptNames();
  } else {
    emit('update:modelValue', data.id);
    emit('change', data.id, data);
  }
}
// 处理节点点击
const handleNodeClick = (data: DeptItem) => {
  if (props.multiple) {
    syncUpChange(data)
  } else {
    emit('update:modelValue', data.id);
    emit('change', data.id, data);
  }
};

// 处理值变化
const handleChange = (value: string) => {
  if (props.multiple) {
    emit('update:modelValue', value);
    emit('change', value, undefined);
    // 同步设置选中节点
    deptTreeRef.value?.setCheckedKeys(value);
    getDeptNames();
  } else {
    emit('update:modelValue', value);
    const item = flatDept.value.find(dept => dept.id === value);
    if (item) {
      emit('change', value, item);
    }
  }
};

// 获取部门树数据
const getTree = async () => {
  try {
    const response = await deptTree();
    treeDeptData.value = response.data;
  } catch (error) {
    // eslint-disable-next-line no-console
    console.error('获取部门树失败:', error);
  }
};

// 监听过滤文本变化
watch(
  () => filterText.value,
  (val) => {
    deptTreeRef.value?.filter(val);
  },
);

// 组件挂载时获取数据
onMounted(() => {
  getTree();
});


defineExpose({
  setChecked
})
</script>

<style lang="scss" scoped>
.hide {
  display: none !important;
}
</style>