<template>
  <div class="el-tree-select">
    <el-select
      style="width: 100%"
      v-model="valueId"
      ref="treeSelect"
      :disabled="disabled"
      :filterable="true"
      :clearable="true"
      :multiple="showCheckbox"
      collapse-tags
      @clear="clearHandle"
      @focus="selectFilterData('')"
      @remove-tag="handleRemoveTag"
      :filter-method="selectFilterData"
      :placeholder="placeholder"
      v-bind="$attrs"
    >
      <template v-if="Array.isArray(valueId)">
        <el-option
          v-for="item in flatOptions"
          :key="item[props.props.value]"
          :label="item[props.props.label]"
          :value="item[props.props.value]"
        >
          <div style="display: none">{{ item[props.props.label] }}</div>
        </el-option>
      </template>
      <el-option :value="valueId" disabled :label="valueTitle">
        <el-tree
          id="tree-option"
          ref="selectTree"
          :accordion="accordion"
          :data="data"
          node-key="id"
          :props="props.props"
          :showCheckbox="showCheckbox"
          :node-key="props.props.value"
          :expand-on-click-node="false"
          :default-expanded-keys="defaultExpandedKey"
          :filter-node-method="filterNode"
          @check-change="handleCheckChange"
          @node-click="handleNodeClick"
          v-bind="$attrs"
        >
          <template #default="{ node, data }">
            <div :class="{ isDisabled: data[props.props.disabled] }">
              {{ data[props.props.label] }}
            </div>
          </template>
        </el-tree>
      </el-option>
    </el-select>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue';
const { proxy } = getCurrentInstance();

const props = defineProps({
  /* 配置项 */
  props: {
    type: Object,
    default: () => {
      return {
        value: 'id', // ID字段名
        label: 'label', // 显示名称
        children: 'children', // 子级字段名
        disabled: false,
      };
    },
  },
  /* 自动收起 */
  accordion: {
    type: Boolean,
    default: () => {
      return false;
    },
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  showCheckbox: {
    type: Boolean,
    default: false,
  },
  /**当前双向数据绑定的值 */
  modelValue: {
    type: [String, Number],
    default: '',
  },
  /**当前的数据 */
  data: {
    type: Array,
    default: () => [],
  },
  /**输入框内部的文字 */
  placeholder: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue', 'node-click']);

const valueId = computed({
  get: () => {
    return props.modelValue;
  },
  set: (val) => {
    emit('update:modelValue', val);
  },
});

const valueTitle = ref('');
const defaultExpandedKey = ref([]);

watch(
  () => props.modelValue,
  (val) => {
    if (props.showCheckbox) {
      nextTick(() => {
        proxy.$refs.selectTree?.setCheckedKeys(val);
      });
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
const flatOptions = computed(() => {
  const arr = [];
  function loop(data) {
    data.forEach((item) => {
      arr.push(item);
      if (item.children) {
        loop(item.children);
      }
    });
  }
  if (Array.isArray(props.data)) {
    console.log('props.data', props.data)
    loop(props.data);
  }
  return arr;
});
/**
 * 初始化下拉菜单选择器的默认值，并设置默认选中和默认展开。
 */
function initHandle() {
  nextTick(() => {
    const selectedValue = valueId.value;
    if (selectedValue !== null && typeof selectedValue !== 'undefined') {
      const node = proxy.$refs.selectTree.getNode(selectedValue);
      if (node) {
        valueTitle.value = node.data[props.props.label];
        proxy.$refs.selectTree.setCurrentKey(selectedValue); // 设置默认选中
        defaultExpandedKey.value = [selectedValue]; // 设置默认展开
      }
    } else {
      clearHandle();
    }
  });
}

/**
 * 点击某一节点时触发的事件，更新当前的选中值和展开状态。
 * @param {Object} node - 被点击的节点对象
 */
function handleNodeClick(node) {
  if (props.showCheckbox || node[props.props.disabled]) {
    return;
  }
  valueTitle.value = node[props.props.label];
  valueId.value = node[props.props.value];
  defaultExpandedKey.value = [];
  proxy.$refs.treeSelect.blur();
  selectFilterData('');
}

function handleCheckChange(data, checked, indeterminate) {
  const value = proxy.$refs.selectTree?.getCheckedKeys() || [];
  emit('update:modelValue', value);
}

/**
 * 搜索过滤函数，根据输入的值来过滤显示的节点。
 * @param {String} val - 输入框内的搜索关键字
 */
function selectFilterData(val) {
  proxy.$refs.selectTree.filter(val);
}

/**
 * 根据输入的值来判断节点是否需要显示。
 * @param {String} value - 输入框内的搜索关键字
 * @param {Object} data - 当前处理的节点数据
 * @returns {Boolean} - 是否需要显示此节点
 */
function filterNode(value, data) {
  if (!value) return true;
  return data[props.props['label']].indexOf(value) !== -1;
}
/**
 * 清空当前的选中状态，并重置展开状态。
 */
function clearHandle() {
  valueTitle.value = '';
  valueId.value = '';
  defaultExpandedKey.value = [];
  clearSelected();
}
function handleRemoveTag(val) {
  const fn = props.props.disabled;
  let isDisabled = false;
  if (typeof fn === 'function') {
    isDisabled = fn({ id: val });
  }
  if (isDisabled) {
    const index = (valueId.value || []).indexOf(val);
    nextTick(() => {
      if (Array.isArray(valueId.value)) {
        if (!valueId.value.includes(val)) {
          valueId.value.splice(index, 0, val);
        }
      } else {
        if (valueId.value !== val) {
          valueId.value = val;
        }
      }
    });
  }
}

/**
 * 删除所有选中状态的节点。
 */
function clearSelected() {
  const allNode = document.querySelectorAll('#tree-option .el-tree-node');
  allNode.forEach((element) => element.classList.remove('is-current'));
}

onMounted(() => {
  initHandle();
});

watch(valueId, () => {
  initHandle();
});
watch(
  () => props.data,
  () => {
    initHandle();
  }
);
</script>

<style lang="scss" scoped>
@import '/@/assets/styles/variables.module.scss';
.el-scrollbar .el-scrollbar__view .el-select-dropdown__item {
  padding: 0;
  background-color: #fff;
  height: auto;
}
.isDisabled {
  color: #999;
}
.el-select-dropdown__item.selected {
  font-weight: normal;
}

ul li .el-tree .el-tree-node__content {
  height: auto;
  padding: 0 20px;
  box-sizing: border-box;
}

:deep(.el-tree-node__content:hover),
:deep(.el-tree-node__content:active),
:deep(.is-current > div:first-child),
:deep(.el-tree-node__content:focus) {
  background-color: mix(#fff, $--color-primary, 90%);
  color: $--color-primary;
}
</style>
