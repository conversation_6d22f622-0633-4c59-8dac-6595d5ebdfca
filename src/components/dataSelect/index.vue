<template>
  <div class="data-select-with-icon flex items-center gap-2 w-full">
    <el-select
      v-model="selectedValue"
      class="full-width-input"
      v-bind="$attrs"
      :multiple="multiple"
      :placeholder="placeholder"
      @change="handleChange"
    >
      <el-option
        v-for="item in optionItems"
        :key="item[valueField]"
        :label="item[labelField]"
        :value="item[valueField]"
        :disabled="item.disabled"
      />
    </el-select>
    <el-button
      icon="Plus"
      class="ml-2"
      @click="handleOpenConfigDialog"
      circle
      size="small"
      title="配置数据源筛选"
    />
    <DataSourceConfigDialog
      v-model="showConfigDialog"
      :data-source="dataSourceConfig"
      :selected-values="selectedValue"
      @confirm="handleConfigConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import DataSourceConfigDialog from '@/components/dataSourceConfigDialog/index.vue';
import { useTableQueryApi } from '/@/api/gen/table';

interface Props {
  modelValue?: string | string[] | number | number[];
  dataSource?: {
    tableName: string;
    labelField: string;
    valueField: string;
    fields?: any[];
  };
  multiple?: boolean;
  placeholder?: string;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '',
  dataSource: () => ({
    tableName: '',
    labelField: '',
    valueField: '',
    fields: [],
  }),
  multiple: false,
  placeholder: '请选择',
});

const emit = defineEmits(['update:modelValue', 'change']);

// 内部状态
const showConfigDialog = ref(false);
const dataSourceConfig = ref<any>({});
const optionItems = ref<any[]>([]);
const selectedValue = ref(props.modelValue);

// 计算属性
const labelField = computed(() => props.dataSource?.labelField || 'label');
const valueField = computed(() => props.dataSource?.valueField || 'value');

// 监听值变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedValue.value = newVal;
  }
);

watch(selectedValue, (newVal) => {
  emit('update:modelValue', newVal);
});

// 加载初始数据
async function loadInitialData() {
  if (!props.dataSource?.tableName || !props.modelValue) return;

  try {
    const values = Array.isArray(props.modelValue) ? props.modelValue : [props.modelValue];
    const res = await useTableQueryApi({
      dsName: props.dataSource.dsName || 'master',
      tableName: props.dataSource.tableName,
      page: 1,
      pageSize: 1000,
      conditions: [
        {
          field: props.dataSource.valueField,
          operator: 'IN',
          value: values,
        },
      ],
    });

    if (res.data?.records) {
      optionItems.value = res.data.records.map((row: any) => ({
        [labelField.value]: row[labelField.value],
        [valueField.value]: row[valueField.value],
        ...row,
      }));
    }
  } catch (error) {
    console.error('加载初始数据失败:', error);
  }
}

// 打开配置弹窗
async function handleOpenConfigDialog() {
  dataSourceConfig.value = { ...props.dataSource };
  showConfigDialog.value = true;
  
  // 如果有选中值，加载对应的数据
  if (props.modelValue) {
    await loadInitialData();
  }
}

// 处理配置确认
function handleConfigConfirm(data: any) {
  const { selectedRows } = data;
  
  // 更新选项列表
  optionItems.value = selectedRows.map((row: any) => ({
    [labelField.value]: row[labelField.value],
    [valueField.value]: row[valueField.value],
    ...row,
  }));

  // 更新选中值
  if (props.multiple) {
    selectedValue.value = selectedRows.map(row => row[valueField.value]);
  } else if (selectedRows.length > 0) {
    selectedValue.value = selectedRows[0][valueField.value];
  } else {
    selectedValue.value = '';
  }

  showConfigDialog.value = false;
}

// 处理选择变化
function handleChange(value: any) {
  emit('change', value);
}

// 初始化
onMounted(() => {
  // 移除这里的 loadInitialData 调用
});
</script>

<style lang="scss" scoped>
.data-select-with-icon {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.full-width-input {
  width: 100% !important;
}
</style>
