<template>
  <el-select ref="selectRef" v-model="model" v-bind="$attrs">
    <el-option 
      v-for="item in options" 
      :style="{
        height: 'auto',
        'white-space': 'normal'
      }" 
      :class="{
        stripe: stripe
      }"
      :key="item.value" 
      :label="item.label" 
      :value="item.value" 
      :disabled="item.disabled"
    >
      <div :style="{
        maxWidth: width,
        lineHeight: '30px'
      }">{{ item.label }}</div>
    </el-option>
  </el-select>
</template>

<script setup>
import { computed, onMounted } from 'vue'

const props = defineProps({
  stripe: {
    type: Boolean,
    default: true
  },
  options: {
    type: Array,
    default: () => []
  },
  optionWidth: {
    type: [Number, String],
    default: ''
  }
})

const model = defineModel();
const selectRef = ref();
const selectWidth = ref(0);
const width = computed(() => {
  if (!props.optionWidth && selectWidth.value) return `${selectWidth.value - 40}px`;
  return typeof props.optionWidth === 'number' ? `${props.optionWidth}px` : props.optionWidth
})
onMounted(() => {
  nextTick(() => {
    selectWidth.value = selectRef.value.$el?.clientWidth || 0;
  })
})
</script>

<style lang='scss'>
.el-select-dropdown {
  .stripe:nth-child(odd) {
    background-color: #f5f7fa;
  }
  .hover {
    background-color: #e7f0fb !important;
  }
}
</style>