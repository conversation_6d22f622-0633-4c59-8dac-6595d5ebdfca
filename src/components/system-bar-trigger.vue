<template>
  <div
    class="system-bar-trigger-wrapper"
    @mouseenter="onEnter"
    :style="{ transform: triggerTransform, transition: 'transform 0.5s cubic-bezier(.4,0,.2,1)' }"
  >
    <div class="system-bar-trigger"></div>
  </div>
</template>

<script setup lang="ts">

const visible = defineModel();
const triggerTransform = computed(() =>
  !visible.value ? 'translate(-50%, 100%)' : 'translate(-50%, 0)'
);

function onEnter() {
  visible.value = false;
}
</script>

<script lang="ts">
export default {};
</script>

<style lang="scss" scoped>
.system-bar-trigger-wrapper {
  position: fixed;
  left: 50%;
  bottom: 0px;
  z-index: 9998;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 488px;
  height: 8px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 12px 12px 0px 0px;
  border-top: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
  border-right: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
  border-left: 1px solid var(--Color-Border-border-color-light, #E4E7ED);
  background: #FFF;
  box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.12);
}
.system-bar-trigger {
  width: 120px;
  height: 4px;
  flex-shrink: 0;
  border-radius: 4px;
  background: var(--Color-Fill-fill-color-darker, #E6E8EB);
}
</style> 