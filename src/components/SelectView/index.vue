<template>
  <div class="select-view-container">
    <el-card>
      <div class="flex h-full">
        <!-- 左侧数据列表 -->
        <div class="flex flex-col flex-1 border-r border-gray-200">
          <!-- 左侧头部 -->
          <div class="flex items-center justify-between px-4 py-2">
            <div class="flex items-center gap-2">
              <el-checkbox
                :model-value="isAllSelected"
                :indeterminate="isIndeterminate"
                @change="handleSelectAll"
              />
              <span
                class="all-data-label"
                :class="{ selected: selectedIds.size > 0 }"
                >全部数据</span
              >
            </div>
            <span class="all-data-count-label">{{ totalCount }}</span>
          </div>

          <!-- 搜索框 -->
          <div class="px-4 py-2">
            <el-input
              v-model="searchKeyword"
              placeholder="请输入关键词搜索"
              size="default"
              clearable
              @input="handleSearch"
              @clear="handleClearSearch"
            />
          </div>

          <!-- 数据列表 -->
          <div
            ref="leftScrollRef"
            class="flex-1 overflow-y-auto"
            @scroll="handleScroll"
          >
            <div>
              <div
                v-for="item in dataList"
                :key="item.id"
                class="data-list-item"
                @click="toggleItem(item)"
              >
                <el-checkbox
                  :model-value="selectedIds.has(item.id)"
                  @change="toggleItem(item)"
                  @click.stop
                />
                <el-tooltip
                  :content="item.name"
                  placement="top"
                  :disabled="!isTextOverflow(item.name)"
                >
                  <span
                    :class="['item-name-label', { 'item-name-selected': selectedIds.has(item.id) }]"
                    ref="itemNameRef"
                  >
                    {{ item.name }}
                  </span>
                </el-tooltip>
              </div>
            </div>

            <!-- 加载更多指示器 -->
            <div
              v-if="isLoading"
              class="flex justify-center py-4"
            >
              <el-icon class="text-blue-500 animate-spin"><Loading /></el-icon>
              <span class="ml-2 text-sm text-gray-500">加载中...</span>
            </div>

            <!-- 无更多数据提示 -->
            <div
              v-if="!hasMore && dataList.length > 0"
              class="py-2 text-center"
            >
              <span class="text-sm text-gray-500">没有更多数据了</span>
            </div>
          </div>
        </div>

        <!-- 右侧已选列表 -->
        <div class="flex flex-col flex-1">
          <!-- 右侧头部 -->
          <div class="flex items-center justify-between px-4 py-2">
            <div class="flex items-center gap-2">
              <span class="selected-data-label">已选择数据</span>
              <span class="selected-count-label">{{ selectedItems.length }}</span>
            </div>
            <button
              v-if="selectedItems.length > 0"
              @click="clearAll"
              class="clear-all-button"
            >
              清空全部
            </button>
          </div>

          <!-- 右侧搜索框 -->
          <div class="px-4 py-2" v-if="selectedItems.length > 0">
            <el-input
              v-model="rightSearchKeyword"
              placeholder="搜索已选择的数据"
              size="default"
              clearable
              @input="handleRightSearch"
              @clear="handleClearRightSearch"
            />
          </div>

          <!-- 已选项列表 -->
          <div class="flex-1 overflow-y-auto">
            <div
              v-if="selectedItems.length === 0"
              class="flex items-center justify-center h-full"
            >
              <span class="text-sm text-gray-400">暂无选择项</span>
            </div>

            <div v-else>
              <!-- 没有搜索结果时的提示 -->
              <div
                v-if="rightSearchKeyword && filteredSelectedItems.length === 0"
                class="flex items-center justify-center py-8"
              >
                <span class="text-sm text-gray-400">未找到匹配的已选择项</span>
              </div>
              
              <!-- 已选项列表 -->
              <div
                v-for="item in filteredSelectedItems"
                :key="item.id"
                class="data-list-item"
              >
                <el-tooltip
                  :content="item.name"
                  placement="top"
                  :disabled="!isTextOverflow(item.name)"
                >
                  <span class="selected-item-name">
                    {{ item.name }}
                  </span>
                </el-tooltip>
                <button
                  @click="removeItem(item)"
                  class="remove-item-button"
                >
                  <el-icon size="16"><Close /></el-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Close, Loading } from '@element-plus/icons-vue';
import { debounce } from 'lodash';
import { computed, onMounted, ref, watch } from 'vue';

interface DataItem {
  id: string | number;
  name: string;
}

interface Props {
  // 数据获取函数（动态生成，可能初始为空）
  fetchData?: (params: {
    keyword?: string;
    current: number;
    size: number;
  }) => Promise<{ data: { records: DataItem[]; total: number; } }>;
  // 每页大小
  size?: number;
  // 已选中的项目ID
  modelValue?: (string | number)[];
}

interface Emits {
  (e: 'update:modelValue', value: (string | number)[]): void;
  (e: 'change', selected: DataItem[]): void;
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  modelValue: () => [],
});

const emit = defineEmits<Emits>();

// 响应式数据
const dataList = ref<DataItem[]>([]);
const searchKeyword = ref('');
const rightSearchKeyword = ref(''); // 右侧搜索关键词
const currentPage = ref(1);
const totalCount = ref(0);
const isLoading = ref(false);
const hasMore = ref(true);
const leftScrollRef = ref<HTMLElement>();
const isSearching = ref(false);
const isInitialized = ref(false); // 防重标识

// 选中状态管理
const selectedIds = ref<Set<string | number>>(new Set(props.modelValue));
const selectedItemsData = ref<DataItem[]>([]); // 独立维护已选项的完整数据

// 计算已选项（从独立数据源获取，而不是从当前页数据过滤）
const selectedItems = computed(() => {
  return selectedItemsData.value;
});

// 过滤已选择的数据（根据右侧搜索关键词）
const filteredSelectedItems = computed(() => {
  if (!rightSearchKeyword.value.trim()) {
    return selectedItemsData.value;
  }
  
  const keyword = rightSearchKeyword.value.toLowerCase();
  return selectedItemsData.value.filter(item => 
    item.name.toLowerCase().includes(keyword)
  );
});

// 全选状态计算
const isAllSelected = computed(() => {
  return dataList.value.length > 0 && dataList.value.every((item) => selectedIds.value.has(item.id));
});

// 半选状态计算
const isIndeterminate = computed(() => {
  const selectedCount = dataList.value.filter((item) => selectedIds.value.has(item.id)).length;
  return selectedCount > 0 && selectedCount < dataList.value.length;
});

// 搜索处理（防抖）
const handleSearch = debounce(() => {
  // 重置分页和状态
  currentPage.value = 1;
  hasMore.value = true;
  isSearching.value = !!searchKeyword.value.trim();
  
  // 重新加载数据（重置模式）
  loadData(true);
}, 300);

// 清空搜索
const handleClearSearch = () => {
  searchKeyword.value = '';
  isSearching.value = false;
  currentPage.value = 1;
  hasMore.value = true;
  loadData(true);
};

// 右侧搜索处理
const handleRightSearch = () => {
  // 右侧搜索是即时的，不需要防抖，因为只是过滤内存中的数据
};

// 清空右侧搜索
const handleClearRightSearch = () => {
  rightSearchKeyword.value = '';
};

// 加载数据
const loadData = async (reset = false) => {
  if (isLoading.value) return;
  
  // 检查fetchData是否存在
  if (!props.fetchData || typeof props.fetchData !== 'function') {
    return;
  }

  isLoading.value = true;

  try {
    const response = await props.fetchData({
      keyword: searchKeyword.value.trim(),
      current: currentPage.value,
      size: props.size,
    });
    
    const result = {
      list: response.data.records,
      total: response.data.total,
    };

    if (reset) {
      // 重置模式：清空现有数据，加载新数据
      dataList.value = result.list;
    } else {
      // 追加模式：在现有数据后追加新数据
      dataList.value.push(...result.list);
    }

    totalCount.value = result.total;
    hasMore.value = dataList.value.length < result.total;
  } catch (error) {
    // Handle error silently or emit error event if needed
  } finally {
    isLoading.value = false;
  }
};

// 滚动处理
const handleScroll = () => {
  if (!leftScrollRef.value || !hasMore.value || isLoading.value) return;

  const { scrollTop, scrollHeight, clientHeight } = leftScrollRef.value;

  if (scrollTop + clientHeight >= scrollHeight - 10) {
    currentPage.value++;
    loadData();
  }
};

// 全选/取消全选处理
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    // 全选当前页数据
    dataList.value.forEach((item) => {
      selectedIds.value.add(item.id);
      // 避免重复添加
      if (!selectedItemsData.value.find(selectedItem => selectedItem.id === item.id)) {
        selectedItemsData.value.push(item);
      }
    });
  } else {
    // 取消选择当前页数据
    dataList.value.forEach((item) => {
      selectedIds.value.delete(item.id);
      selectedItemsData.value = selectedItemsData.value.filter(selectedItem => selectedItem.id !== item.id);
    });
  }

  emit('update:modelValue', Array.from(selectedIds.value));
  emit('change', selectedItems.value);
};

// 初始化已选数据（用于回显）
const initSelectedItems = (items: DataItem[]) => {
  selectedItemsData.value = [...items];
  selectedIds.value = new Set(items.map(item => item.id));
  
  emit('update:modelValue', Array.from(selectedIds.value));
  emit('change', selectedItems.value);
};

// 统一的初始化方法
const initializeComponent = () => {
  // 检查fetchData是否准备好
  if (!props.fetchData || typeof props.fetchData !== 'function') {
    return; // fetchData还未准备好，不进行初始化
  }
  
  if (isInitialized.value) return; // 避免重复初始化
  
  isInitialized.value = true;
  loadData(true);
};

// 监听fetchData变化，当它准备好时自动初始化
watch(
  () => props.fetchData,
  (newFetchData) => {
    if (newFetchData && typeof newFetchData === 'function') {
      // fetchData准备好了，如果还未初始化则进行初始化
      if (!isInitialized.value) {
        initializeComponent();
      }
    }
  },
  { immediate: true }
);

// 切换选中状态
const toggleItem = (item: DataItem) => {
  if (selectedIds.value.has(item.id)) {
    // 取消选中：从ID集合和数据数组中移除
    selectedIds.value.delete(item.id);
    selectedItemsData.value = selectedItemsData.value.filter(selectedItem => selectedItem.id !== item.id);
  } else {
    // 选中：添加到ID集合和数据数组中
    selectedIds.value.add(item.id);
    // 避免重复添加
    if (!selectedItemsData.value.find(selectedItem => selectedItem.id === item.id)) {
      selectedItemsData.value.push(item);
    }
  }

  emit('update:modelValue', Array.from(selectedIds.value));
  emit('change', selectedItems.value);
};

// 移除选中项
const removeItem = (item: DataItem) => {
  selectedIds.value.delete(item.id);
  selectedItemsData.value = selectedItemsData.value.filter(selectedItem => selectedItem.id !== item.id);
  
  emit('update:modelValue', Array.from(selectedIds.value));
  emit('change', selectedItems.value);
};

// 清空全部
const clearAll = () => {
  selectedIds.value.clear();
  selectedItemsData.value = [];
  
  emit('update:modelValue', []);
  emit('change', []);
};

// 检测文本是否溢出
const isTextOverflow = (text: string) => {
  // 简单的长度检测，可以根据实际需要调整
  return (text || '').length > 20;
};

// 监听外部传入的选中项变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedIds.value = new Set(newValue);
    // 如果外部传入的ID在当前已选数据中不存在，则需要清理已选数据
    selectedItemsData.value = selectedItemsData.value.filter(item => newValue.includes(item.id));
  },
  { deep: true }
);

// 初始化
onMounted(() => {
  // 只有在fetchData准备好时才初始化
  if (props.fetchData && typeof props.fetchData === 'function') {
    initializeComponent();
  }
  // 如果fetchData还没准备好，watch会在准备好时自动初始化
});

// 暴露方法供外部调用
defineExpose({
  initSelectedItems,
});
</script>

<style scoped lang="scss">
.select-view-container {
  width: 100%;
  box-sizing: border-box;
  // 强制 el-card 继承父容器高度
  :deep(.el-card) {
    height: 100% !important;
    display: flex;
    flex-direction: column;
    min-height: 0;
    margin: 0 !important;
    box-sizing: border-box;
  }

  // 强制 el-card__body 占满剩余空间
  :deep(.el-card__body) {
    flex: 1 !important;
    padding: 0 !important;
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 0;
  }
}

:deep(.el-input__wrapper) {
  @apply bg-white border-gray-300;
  border-radius: 8px;
}

:deep(.el-input__inner) {
  @apply text-gray-900 bg-white;

  &::placeholder {
    @apply text-gray-400;
  }
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  @apply bg-blue-500 border-blue-500;
}

:deep(.el-checkbox__input.is-checked + .el-checkbox__label) {
  @apply text-blue-500;
}

:deep(.el-checkbox) {
  height: 22px;
  line-height: 22px;
}

:deep(.el-checkbox__inner) {
  border-radius: 4px;
  height: 16px;
  width: 16px;
}

:deep(.el-checkbox__label) {
  line-height: 22px;
}

.selected-data-label {
  color: var(--el-text-color-secondary, #7e8694);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
}

.selected-count-label {
  color: var(--el-text-color-regular, #505762);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
}

.all-data-label {
  color: var(--el-text-color-primary, #1c2026);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
  &.selected {
    color: var(--el-color-action-light-5, #1677ff);
  }
}

.all-data-count-label {
  color: var(--el-text-color-secondary, #7e8694);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
}

.data-list-item {
  display: flex;
  padding: 5px 16px;
  align-items: center;
  gap: 8px;
  align-self: stretch;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: var(--el-fill-color-light, #f5f7fa);
  }
}

.item-name-label {
  color: var(--el-text-color-primary, #1c2026);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
  flex: 1;

  /* 文本截断样式 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.item-name-selected {
  color: var(--el-color-action-light-5, #1677ff) !important;
}

.selected-item-name {
  color: var(--el-text-color-primary, #1c2026);
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
  flex: 1;

  /* 文本截断样式 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.remove-item-button {
  color: var(--el-text-color-primary, #1c2026);
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    opacity: 0.8;
  }
}

.clear-all-button {
  color: var(--el-color-action-light-5, #1677ff);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px; /* 157.143% */
  letter-spacing: -0.01px;
  border: none;
  background: none;
  cursor: pointer;
  padding: 0;

  &:hover {
    opacity: 0.8;
  }
}

/* 自定义滚动条 */
.select-view-container :deep(*::-webkit-scrollbar) {
  width: 6px;
}

.select-view-container :deep(*::-webkit-scrollbar-track) {
  @apply bg-gray-100;
}

.select-view-container :deep(*::-webkit-scrollbar-thumb) {
  @apply bg-gray-300 rounded-full;
}

.select-view-container :deep(*::-webkit-scrollbar-thumb:hover) {
  @apply bg-gray-400;
}
</style>
