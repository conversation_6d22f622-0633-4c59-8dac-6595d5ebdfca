# SelectView 组件

一个功能丰富的双列选择器组件，支持搜索、多选、滚动加载等功能。

## 功能特性

### 左侧数据列表
- ✅ 双输入框模糊搜索（姓名和内容）
- ✅ 多选 checkbox 支持
- ✅ 滚动到底部自动加载更多数据
- ✅ 全选功能
- ✅ 支持静态数据和异步数据获取

### 右侧已选列表  
- ✅ 展示已选择的数据项
- ✅ 超过高度显示滚动条
- ✅ 点击 X 图标取消选择
- ✅ 一键清空全部选择

### 其他特性
- ✅ 支持亮色/暗色主题
- ✅ 响应式设计
- ✅ TypeScript 类型支持
- ✅ 防抖搜索优化

## Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `modelValue` | `(string \| number)[]` | `[]` | 已选中的项目ID数组，支持 v-model |
| `data` | `DataItem[]` | `[]` | 静态数据数组 |
| `fetchData` | `Function` | - | 异步数据获取函数 |
| `pageSize` | `number` | `20` | 每页数据大小 |

## Events

| 事件 | 参数 | 说明 |
|------|------|------|
| `update:modelValue` | `(string \| number)[]` | 选中项ID发生变化时触发 |
| `change` | `DataItem[]` | 选中项发生变化时触发，返回完整的数据项 |

## 类型定义

```typescript
interface DataItem {
  id: string | number
  name: string
}

interface FetchParams {
  keyword?: string
  contentKeyword?: string
  page: number
  pageSize: number
}

interface FetchResult {
  list: DataItem[]
  total: number
}

type FetchFunction = (params: FetchParams) => Promise<FetchResult>
```

## 基础用法

### 静态数据

```vue
<template>
  <SelectView 
    v-model="selectedIds" 
    :data="staticData"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SelectView from '@/components/SelectView/index.vue'

const selectedIds = ref([])
const staticData = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  // ...
]

function handleChange(selected) {
  console.log('选中项:', selected)
}
</script>
```

### 异步数据

```vue
<template>
  <SelectView 
    v-model="selectedIds"
    :fetch-data="fetchData"
    :page-size="10"
    @change="handleChange"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import SelectView from '@/components/SelectView/index.vue'

const selectedIds = ref([])

async function fetchData(params) {
  const response = await api.getData(params)
  return {
    list: response.data,
    total: response.total
  }
}

function handleChange(selected) {
  console.log('选中项:', selected)
}
</script>
```

## 样式定制

组件使用 Tailwind CSS 和 DaisyUI 构建，支持主题切换：

```scss
// 自定义样式
.select-view {
  // 自定义滚动条样式
  :deep(*::-webkit-scrollbar) {
    width: 6px;
  }
  
  // 自定义主色调
  :deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
    @apply bg-primary border-primary;
  }
}
```

## 注意事项

1. 组件需要明确的高度容器才能正常显示滚动
2. 搜索功能使用 300ms 防抖优化
3. 异步数据加载失败时会静默处理错误
4. 组件内部使用 Set 数据结构管理选中状态，确保高性能 