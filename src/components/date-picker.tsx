import { ElDatePicker } from 'yun-design';
import dayjs from 'dayjs';

type DateType =
  | 'year'
  | 'years'
  | 'month'
  | 'months'
  | 'date'
  | 'dates'
  | 'datetime'
  | 'week'
  | 'datetimerange'
  | 'daterange'
  | 'monthrange'
  | 'yearrange';
function getConfig(type: DateType) {
  let config: any[] = [];
  switch (type) {
    case 'date':
      config = [
        {
          text: '今天',
          value: dayjs().startOf('day'),
        },
        {
          text: '昨天',
          value: () => {
            return dayjs().subtract(1, 'day').startOf('day');
          },
        },
        {
          text: '一周前',
          value: () => {
            return dayjs().subtract(1, 'week').startOf('day');
          },
        },
      ];
      break;
    case 'daterange':
    case 'datetimerange':
      config = [
        {
          text: '最近一周',
          value: () => {
            const end = dayjs().endOf('day');
            const start = dayjs().subtract(7, 'day').startOf('day');
            return [start, end];
          },
        },
        {
          text: '最近一个月',
          value: () => {
            const end = dayjs().endOf('day');
            const start = dayjs().subtract(1, 'month').startOf('day');
            return [start, end];
          },
        },
        {
          text: '最近三个月',
          value: () => {
            const end = dayjs().endOf('day');
            const start = dayjs().subtract(3, 'month').startOf('day');
            return [start, end];
          },
        },
      ];
      break;
    case 'monthrange':
      config = [
        {
          text: '本月',
          value: () => {
            const end = dayjs().endOf('month').endOf('day');
            const start = dayjs().startOf('month').startOf('day');
            return [start, end];
          },
        },
        {
          text: '今年至今',
          value: () => {
            const end = dayjs().endOf('month').endOf('day');
            const start = dayjs().startOf('year').startOf('month').startOf('day');
            return [start, end];
          },
        },
        {
          text: '最近六个月',
          value: () => {
            const end = dayjs().endOf('month').endOf('day');
            const start = dayjs().subtract(6, 'month').startOf('day');
            return [start, end];
          },
        },
      ];
      break;
    case 'year':
      config = [
        {
          text: '本年',
          value: () => {
            const end = dayjs().endOf('year').endOf('month').endOf('day');
            const start = dayjs().startOf('year').startOf('month').startOf('day');
            return [start, end];
          },
        },
        {
          text: '过去十年',
          value: () => {
            const end = dayjs().endOf('year').endOf('month').endOf('day');
            const start = dayjs().subtract(10, 'year').startOf('year').startOf('month').startOf('day');
            return [start, end];
          },
        },
        {
          text: '今后五十年',
          value: () => {
            const end = dayjs().add(50, 'year').endOf('year').endOf('month').endOf('day');
            const start = dayjs().startOf('year').startOf('month').startOf('day');
            return [start, end];
          },
        },
      ];
      break;
    default:
      config = [];
  }
  return config;
}

export default (props: any) => {
  const shortcuts = props?.shortcuts || getConfig(props.type || 'date');
  return (
    <ElDatePicker
      {...props}
      shortcuts={shortcuts}
    />
  );
};
