<template>
  <div ref="editorContainer" class="code-editor" :id="id"></div>
</template>

<script setup>
import { ref, onMounted, toRaw, onUnmounted, nextTick } from 'vue';
import 'monaco-editor/esm/vs/basic-languages/sql/sql.contribution';
import 'monaco-editor/esm/vs/basic-languages/xml/xml.contribution';
import 'monaco-editor/esm/vs/basic-languages/java/java.contribution';
import 'monaco-editor/esm/vs/basic-languages/python/python.contribution';
import * as monaco from 'monaco-editor';
import editorWorker from 'monaco-editor/esm/vs/editor/editor.worker?worker'
import jsonWorker from 'monaco-editor/esm/vs/language/json/json.worker?worker'
import cssWorker from 'monaco-editor/esm/vs/language/css/css.worker?worker'
import htmlWorker from 'monaco-editor/esm/vs/language/html/html.worker?worker'
import tsWorker from 'monaco-editor/esm/vs/language/typescript/ts.worker?worker'

self.MonacoEnvironment = {
    getWorker(_, label) {
        if (label === 'json') {
        return new jsonWorker()
        }
        if (label === 'css' || label === 'scss' || label === 'less') {
        return new cssWorker()
        }
        if (label === 'html' || label === 'handlebars' || label === 'razor') {
        return new htmlWorker()
        }
        if (label === 'typescript' || label === 'javascript') {
        return new tsWorker()
        }
        return new editorWorker()
    }
}

const codes = defineModel()
const props = defineProps({
  language: {
    type: String,
    default: 'json' // typescript javascript css html json python
  },
  disabled: {
    type: Boolean,
    default: false
  },
  options: {
    type: Object,
    default: () => ({})
  },
  scrollDom: {
    type: Object,
    default: () => ({})
  },
  theme: {
    type: String,
    default: 'vs'
  },
})

const emits = defineEmits(['change', 'scroll'])

let editor;
const editorContainer = ref(null)
const isScrollEnd = ref(true)
const id = ref(`editor${Math.ceil(Math.random() * 1000) + Date.now()}`)

onMounted(() => {
  nextTick(() => {
    monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({
      noSemanticValidation: true,
      noSyntaxValidation: false
    });
    monaco.languages.typescript.javascriptDefaults.setCompilerOptions({
      target: monaco.languages.typescript.ScriptTarget.ES2016,
      allowNonTsExtensions: true
    });

    editor = monaco.editor.create(editorContainer.value, {
      value: codes.value || '',
      language: props.language,
      minimap: {
        enabled: false,
      },
      tanSize: 2,
      fontSize: 16,
      theme: props.language,	// 主题 vs-dark
      folding: true, // 是否折叠
      foldingHighlight: true, // 折叠等高线
      foldingStrategy: "auto", // 折叠方式
      showFoldingControls: "always", // 是否一直显示折叠
      disableLayerHinting: true, // 等宽优化
      emptySelectionClipboard: false, // 空选择剪切板
      selectionClipboard: false, // 选择剪切板
      automaticLayout: true, // 自动布局
      codeLens: false, // 代码镜头
      readOnly: props.disabled,
      scrollBeyondLastLine: false, // 滚动完最后一行后再滚动一屏幕
      colorDecorators: true, // 颜色装饰器
      accessibilitySupport: "on", // 辅助功能支持"auto" | "off" | "on"
      lineNumbers: "on", // 行号 取值： "on" | "off" | "relative" | "interval" | function
      lineNumbersMinChars: 4, // 行号最小字符   number
      enableSplitViewResizing: false,
      ...props.options
    });
    // 监听编辑器内容变化
    editor.onDidChangeModelContent(() => {
      const value = toRaw(editor).getValue();
      // 触发父组件的 change 事件，通知编辑器内容变化
      emits('change', value);
      codes.value = value;
    });
    const scrollContainer = editor.getDomNode().querySelector('.monaco-scrollable-element');
    scrollContainer?.addEventListener('wheel', function(event) {
      if (!isScrollEnd.value) return;
      const dom = props.scrollDom;
      if (!dom) return;
      dom.scrollTo({ top: dom.scrollTop - event.wheelDeltaY })
    }, true);
    editor.onDidScrollChange((e) => {
      // 获取编辑器的滚动容器
      // 获取滚动容器的高度和滚动位置
      const scrollTop = e.scrollTop;
      const scrollHeight = e.scrollHeight;
      const clientHeight = scrollContainer.clientHeight;
      // 判断是否滚动到底部
      if (scrollTop + clientHeight >= scrollHeight) {
        // 获取父级容器
        isScrollEnd.value = true;
      } else {
        isScrollEnd.value = false;
      }
      if (!scrollTop) {
        isScrollEnd.value = true;
      }
    });
  })
});
onUnmounted(() => {
  editor?.dispose();
})
defineExpose({
  getEditor: () => editor,
  isScrollEnd
})
</script>

<style scoped lang='scss'>
.code-editor {
  width: 100%;
  height: 100%;
  min-height: 300px;
  padding: 10px 0;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #ffffff;
}
:deep(.monaco-editor) {
  // padding: 10px 0;
}
</style>