<template>
  <div class="system-bar-wrapper" :style="{ bottom: tempVisible ? '0' : '-56px' }" @mouseleave="onLeave">
    <div class="system-bar">
      <el-tooltip
        v-for="item in systems"
        :key="item.name"
        :content="item.name"
        placement="top"
        effect="dark"
      >
        <button
          class="system-bar__item"
          :title="item.name"
          @click="openLink(item.url)"
        >
          <img :src="item.icon" :alt="item.name" class="system-bar__icon" />
        </button>
      </el-tooltip>
    </div>
  </div>
</template>

<script setup lang="ts">
import larkIcon from '/src/assets/system/lark.png?url';
import publicServiceIcon from '/src/assets/system/public-service.png?url';
import xxlJobIcon from '/src/assets/system/xxl-job.png?url';
import logIcon from '/src/assets/system/log.png?url';
import traceIcon from '/src/assets/system/trace.png?url';
import frontendIcon from '/src/assets/system/frontend.png?url';
import opsIcon from '/src/assets/system/ops.png?url';
import sqlIcon from '/src/assets/system/sql.png?url';
import rancherIcon from '/src/assets/system/rancher.png?url';
import gitlabIcon from '/src/assets/system/gitlab.png?url';
import aiIcon from '/src/assets/system/ai.png?url';

const emits = defineEmits(["update:modelValue"]);
const tempVisible = ref(false);
const visible = defineModel();
interface SystemItem {
  name: string;
  icon: string;
  url: string;
}
watch(visible, (val) => {
  tempVisible.value = val;
}, { immediate: true });
const systems: SystemItem[] = [
  { name: '云雀发布平台', icon: larkIcon, url: 'https://lark.ops.yunlizhi.cn/' },
  { name: '公共服务平台', icon: publicServiceIcon, url: 'https://open-dc.canpan.net/' },
  { name: 'XXL定时任务平台', icon: xxlJobIcon, url: 'https://saas-xxl-job-admin-v3.canpanscp.com/xxl-job-admin' },
  { name: '日志平台', icon: logIcon, url: 'http://kibana.dc-qa.yunlizhi.net/' },
  { name: '链路监控平台', icon: traceIcon, url: 'http://skywalking.dc-qa.yunlizhi.net/' },
  { name: '前端组件平台', icon: frontendIcon, url: 'http://ms.yunlizhi.net/' },
  { name: '运维平台', icon: opsIcon, url: 'http://grafana.ops.yunlizhi.net/dashboards' },
  { name: 'SQL工单平台', icon: sqlIcon, url: 'https://dmp-v3.ops.yunlizhi.cn/' },
  { name: 'Rancher', icon: rancherIcon, url: 'https://paas.ops.yunlizhi.cn/' },
  { name: 'Gitlab', icon: gitlabIcon, url: 'https://git.ops.yunlizhi.cn/' },
  { name: 'AI服务平台', icon: aiIcon, url: 'https://saas-dify.canpanscp.com/signin' }, // 补充真实地址
];

function openLink(url: string) {
  if (url && url !== '#') {
    window.open(url, '_blank');
  }
}
function onLeave(){
  // tempVisible.value = false;
  // visible.value = false;
}
</script>

<script lang="ts">
export default {};
</script>

<style lang="scss" scoped>
.system-bar-wrapper {
  position: fixed;
  left: 50%;
  right: 0;
  transform: translateX(-50%);
  bottom: -56px;
  z-index: 9999;
  transition: all 0.5s ease-in-out;
  padding-bottom: 8px;
  width: fit-content;
}
.system-bar {
  display: flex;
  gap: 12px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 16px;
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.08);
  padding: 8px;
  pointer-events: auto;
  align-items: center;
}
.system-bar__item {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  transition: transform 0.12s;
  border-radius: 6px;
  &:hover {
    background: rgba(0, 105, 255, 0.08);
    // transform: translateY(-2px) scale(1.08);
  }
  &:focus {
    background: rgba(0, 105, 255, 0.12);
  }
}
.system-bar__icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
  transition: filter 0.2s;
}
html.dark .system-bar {
  background: rgba(30, 41, 59, 0.92);
  box-shadow: 0 4px 24px 0 rgba(0,0,0,0.32);
}
</style> 