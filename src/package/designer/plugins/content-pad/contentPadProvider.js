import { assign, forEach, isArray } from 'min-dash';

import { is } from 'bpmn-js/lib/util/ModelUtil';

import { isExpanded, isEventSubProcess } from 'bpmn-js/lib/util/DiUtil';

import { isAny } from 'bpmn-js/lib/features/modeling/util/ModelingUtil';

import { getChildLanes } from 'bpmn-js/lib/features/modeling/util/LaneUtil';

import { hasPrimaryModifier } from 'diagram-js/lib/util/Mouse';


/**
 * A provider for BPMN 2.0 elements context pad
 */
// 上下文菜单提供者构造函数
export default function ContextPadProvider(
  config,        // 配置对象
  injector,     // 依赖注入器
  eventBus,     // 事件总线
  contextPad,   // 上下文菜单实例
  modeling,     // 建模工具
  elementFactory, // 元素工厂
  connect,      // 连接工具
  create,       // 创建工具
  popupMenu,    // 弹出菜单
  canvas,       // 画布
  rules,        // 规则检查
  translate,    // 翻译工具
  elementRegistry // 元素注册表
) {
  config = config || {};

  contextPad.registerProvider(this);

  this._contextPad = contextPad;

  this._modeling = modeling;

  this._elementFactory = elementFactory;
  this._connect = connect;
  this._create = create;
  this._popupMenu = popupMenu;
  this._canvas = canvas;
  this._rules = rules;
  this._translate = translate;
  

  if (config.autoPlace !== false) {
    this._autoPlace = injector.get('autoPlace', false);
  }

  // 监听创建结束事件
  eventBus.on('create.end', 250, function(event) {
    var context = event.context;
    var shape = context.shape;

    // 如果没有主修饰键或上下文菜单未打开，则返回
    if (!hasPrimaryModifier(event) || !contextPad.isOpen(shape)) {
      return;
    }

    // 获取菜单项并触发替换操作
    var entries = contextPad.getEntries(shape);
    if (entries.replace) {
      entries.replace.action.click(event, shape);
    }
  });
}

ContextPadProvider.$inject = [
  'config.contextPad',
  'injector',
  'eventBus',
  'contextPad',
  'modeling',
  'elementFactory',
  'connect',
  'create',
  'popupMenu',
  'canvas',
  'rules',
  'translate',
  'elementRegistry'
];

/**
 * 获取BPMN元素的上下文菜单项
 * 这是BPMN设计器中最重要的上下文菜单生成方法，根据元素类型动态生成可用的操作菜单
 * 
 * @param {Object} element - 当前选中的BPMN元素对象，包含业务对象(businessObject)等信息
 * @return {Object} 返回一个包含所有可用菜单项的对象，格式为 { actionKey: { 配置对象 } }
 */
ContextPadProvider.prototype.getContextPadEntries = function(element) {
  // 获取依赖的服务实例
  var contextPad = this._contextPad;
  var modeling = this._modeling;
  var elementFactory = this._elementFactory;
  var connect = this._connect;
  var create = this._create;
  var popupMenu = this._popupMenu;
  var canvas = this._canvas;
  var rules = this._rules;
  var autoPlace = this._autoPlace;
  var translate = this._translate;

  // 初始化菜单项容器
  var actions = {};

  // 如果是标签元素，不显示任何菜单项
  if (element.type === 'label') {
    return actions;
  }

  // 获取元素的业务对象(包含BPMN元数据)
  var businessObject = element.businessObject;

  // 定义连接操作函数
  function startConnect(event, element) {
    connect.start(event, element);
  }

  // 定义删除元素函数
  function removeElement() {
    modeling.removeElements([element]);
  }

  // 定义获取替换菜单位置的函数
  function getReplaceMenuPosition(element) {
    var Y_OFFSET = 5;

    var diagramContainer = canvas.getContainer();
    var pad = contextPad.getPad(element).html;

    var diagramRect = diagramContainer.getBoundingClientRect();
    var padRect = pad.getBoundingClientRect();

    var top = padRect.top - diagramRect.top;
    var left = padRect.left - diagramRect.left;

    var pos = {
      x: left,
      y: top + padRect.height + Y_OFFSET
    };

    console.log('Replace menu position:', pos);
    return pos;
  }

  // 定义创建追加动作的工厂函数
  function appendAction(type, className, title, options) {
    if (typeof title !== 'string') {
      options = title;
      title = translate('Append {type}', { type: type.replace(/^bpmn:/, '') });
    }

    function appendStart(event, element) {
      var shape = elementFactory.createShape(assign({ type: type }, options));
      create.start(event, shape, {
        source: element
      });
    }

    var append = autoPlace
      ? function(event, element) {
        var shape = elementFactory.createShape(assign({ type: type }, options));

        autoPlace.append(element, shape);
      }
      : appendStart;

    return {
      group: 'model',
      className: className,
      title: title,
      action: {
        dragstart: appendStart,
        click: append
      }
    };
  }

  // 定义泳道分割处理器
  function splitLaneHandler(count) {
    return function(event, element) {
      modeling.splitLane(element, count);
      contextPad.open(element, true); // 分割后刷新菜单
    };
  }

  // ========== 根据元素类型添加特定菜单项 ==========

  // 1. 处理泳道/参与者类型的元素
  if (isAny(businessObject, ['bpmn:Lane', 'bpmn:Participant']) && isExpanded(businessObject)) {
    var childLanes = getChildLanes(element);

    assign(actions, {
      'lane-insert-above': {
        group: 'lane-insert-above',
        className: 'bpmn-icon-lane-insert-above',
        title: translate('Add Lane above'),
        action: {
          click: function(event, element) {
            modeling.addLane(element, 'top');
          }
        }
      }
    });

    if (childLanes.length < 2) {
      if (element.height >= 120) {
        assign(actions, {
          'lane-divide-two': {
            group: 'lane-divide',
            className: 'bpmn-icon-lane-divide-two',
            title: translate('Divide into two Lanes'),
            action: {
              click: splitLaneHandler(2)
            }
          }
        });
      }

      if (element.height >= 180) {
        assign(actions, {
          'lane-divide-three': {
            group: 'lane-divide',
            className: 'bpmn-icon-lane-divide-three',
            title: translate('Divide into three Lanes'),
            action: {
              click: splitLaneHandler(3)
            }
          }
        });
      }
    }

    assign(actions, {
      'lane-insert-below': {
        group: 'lane-insert-below',
        className: 'bpmn-icon-lane-insert-below',
        title: translate('Add Lane below'),
        action: {
          click: function(event, element) {
            modeling.addLane(element, 'bottom');
          }
        }
      }
    });
  }

  // 2. 处理流程节点类型的元素
  if (is(businessObject, 'bpmn:FlowNode')) {
    console.log('Processing FlowNode:', businessObject);
    
    // 根据不同节点子类型添加特定菜单
    if (is(businessObject, 'bpmn:EventBasedGateway')) {
      console.log('Processing EventBasedGateway');
      assign(actions, {
        'append.receive-task': appendAction('bpmn:ReceiveTask', 'bpmn-icon-receive-task', translate('Append ReceiveTask')),
        'append.message-intermediate-event': appendAction(
          'bpmn:IntermediateCatchEvent',
          'bpmn-icon-intermediate-event-catch-message',
          translate('Append MessageIntermediateCatchEvent'),
          { eventDefinitionType: 'bpmn:MessageEventDefinition' }
        ),
        'append.timer-intermediate-event': appendAction(
          'bpmn:IntermediateCatchEvent',
          'bpmn-icon-intermediate-event-catch-timer',
          translate('Append TimerIntermediateCatchEvent'),
          { eventDefinitionType: 'bpmn:TimerEventDefinition' }
        ),
        'append.condition-intermediate-event': appendAction(
          'bpmn:IntermediateCatchEvent',
          'bpmn-icon-intermediate-event-catch-condition',
          translate('Append ConditionIntermediateCatchEvent'),
          { eventDefinitionType: 'bpmn:ConditionalEventDefinition' }
        ),
        'append.signal-intermediate-event': appendAction(
          'bpmn:IntermediateCatchEvent',
          'bpmn-icon-intermediate-event-catch-signal',
          translate('Append SignalIntermediateCatchEvent'),
          { eventDefinitionType: 'bpmn:SignalEventDefinition' }
        )
      });
    } else if (isEventType(businessObject, 'bpmn:BoundaryEvent', 'bpmn:CompensateEventDefinition')) {
      assign(actions, {
        'append.compensation-activity': appendAction('bpmn:Task', 'bpmn-icon-task', translate('Append compensation activity'), {
          isForCompensation: true
        })
      });
    } else if (
      !is(businessObject, 'bpmn:EndEvent') &&
      !businessObject.isForCompensation &&
      !isEventType(businessObject, 'bpmn:IntermediateThrowEvent', 'bpmn:LinkEventDefinition') &&
      !isEventSubProcess(businessObject)
    ) {
      assign(actions, {
        'append.end-event': appendAction('bpmn:EndEvent', 'bpmn-icon-end-event-none', translate('Append EndEvent')),
        'append.gateway': appendAction('bpmn:ExclusiveGateway', 'bpmn-icon-gateway-none', translate('Append Gateway')),
        'append.append-task': appendAction('bpmn:UserTask', 'bpmn-icon-user-task', translate('Append Task')),
        'append.intermediate-event': appendAction(
          'bpmn:IntermediateThrowEvent',
          'bpmn-icon-intermediate-event-none',
          translate('Append Intermediate/Boundary Event')
        )
      });
    }

    // 添加替换菜单
    console.log('Adding replace menu for FlowNode');
    actions['replace'] = {
      group: 'edit',
      className: 'bpmn-icon-wrench',
      title: translate('替换元素'),
      action: {
        click: function(event, element) {
          var position = getReplaceMenuPosition(element);
          popupMenu.open(element, 'bpmn-replace', position);
        }
      }
    };

    // 添加删除菜单
    assign(actions, {
      delete: {
        group: 'edit',
        className: 'bpmn-icon-trash',
        title: translate('Remove'),
        action: {
          click: removeElement
        }
      }
    });

    // 添加连接菜单
    assign(actions, {
      connect: {
        group: 'connect',
        className: 'bpmn-icon-connection-multi',
        title: translate('Connect using SequenceFlow'),
        action: {
          click: startConnect,
          dragstart: startConnect
        }
      }
    });
  }

  // 4. 添加连接和文本批注等通用菜单项
  if (isAny(businessObject, ['bpmn:FlowNode', 'bpmn:InteractionNode', 'bpmn:DataObjectReference', 'bpmn:DataStoreReference'])) {
    assign(actions, {
      'append.text-annotation': appendAction('bpmn:TextAnnotation', 'bpmn-icon-text-annotation'),

      connect: {
        group: 'connect',
        className: 'bpmn-icon-connection-multi',
        title: translate('Connect using ' + (businessObject.isForCompensation ? '' : 'Sequence/MessageFlow or ') + 'Association'),
        action: {
          click: startConnect,
          dragstart: startConnect
        }
      }
    });
  }

  if (isAny(businessObject, ['bpmn:DataObjectReference', 'bpmn:DataStoreReference'])) {
    assign(actions, {
      connect: {
        group: 'connect',
        className: 'bpmn-icon-connection-multi',
        title: translate('Connect using DataInputAssociation'),
        action: {
          click: startConnect,
          dragstart: startConnect
        }
      }
    });
  }

  if (is(businessObject, 'bpmn:Group')) {
    assign(actions, {
      'append.text-annotation': appendAction('bpmn:TextAnnotation', 'bpmn-icon-text-annotation')
    });
  }

  return actions;
};

// helpers /////////

function isEventType(eventBo, type, definition) {
  var isType = eventBo.$instanceOf(type);
  var isDefinition = false;

  var definitions = eventBo.eventDefinitions || [];
  forEach(definitions, function(def) {
    if (def.$type === definition) {
      isDefinition = true;
    }
  });

  return isType && isDefinition;
}
