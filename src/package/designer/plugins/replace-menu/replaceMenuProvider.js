import { is } from 'bpmn-js/lib/util/ModelUtil';
import ReplaceMenuProvider from 'bpmn-js/lib/features/popup-menu/ReplaceMenuProvider';
// console.log(ReplaceMenuProvider,'ReplaceMenuProvider')

export default function CustomReplaceMenuProvider(
  popupMenu, modeling, bpmnFactory, moddle, translate, rules, bpmnReplace, elementFactory
) {
  // 创建原始的 ReplaceMenuProvider 实例
  const originalProvider = new ReplaceMenuProvider(bpmnFactory, popupMenu, modeling, moddle, bpmnReplace, rules, translate);
    
  popupMenu.registerProvider('bpmn-replace', {
    getEntries(element) {
      // 获取原始菜单项
      const entries = originalProvider.getEntries(element);
      
      // 只对任务节点添加
      if (is(element, 'bpmn:Task') || is(element, 'bpmn:ServiceTask')) {
        entries['replace-microservice-task'] = {
          id: 'replace-microservice-task',
          label: translate('微服务任务'),
          className: 'bpmn-icon-service-task',
          action: () => {
            // 创建新的服务任务业务对象
            const businessObject = moddle.create('bpmn:ServiceTask');
            
            // 创建新的服务任务元素
            const newElement = elementFactory.createShape({
              type: 'bpmn:ServiceTask',
              businessObject: businessObject,
              x: element.x,
              y: element.y,
              width: element.width,
              height: element.height
            });
            
            // 替换元素
            modeling.replaceShape(element, newElement);
            
            // 设置属性
            modeling.updateProperties(newElement, {
              'flowable:type': 'sc'
            });
          }
        };

        // Add replace to Cc Task
        entries['replace-cc-task'] = {
          id: 'replace-cc-task',
          label: translate('抄送任务'),
          className: 'bpmn-icon-mail', // Using mail icon for CC
          action: () => {
            // Create a new Service Task business object
            const businessObject = moddle.create('bpmn:ServiceTask');

            // Create a new shape element
            const newElement = elementFactory.createShape({
              type: 'bpmn:ServiceTask',
              businessObject: businessObject,
              x: element.x,
              y: element.y,
              width: element.width,
              height: element.height
            });

   
            // 替换元素
            modeling.replaceShape(element, newElement);

            // Set properties for Cc Task
            modeling.updateProperties(newElement, {
            'flowable:type': 'copy',
            'name': '抄送任务'
          });
    
          }
        };
      }
      return entries;
    }
  });
}

CustomReplaceMenuProvider.$inject = [
  'popupMenu', 'modeling', 'bpmnFactory', 'moddle', 'translate', 'rules', 'bpmnReplace', 'elementFactory'
]; 