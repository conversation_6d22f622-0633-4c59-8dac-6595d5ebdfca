export default (key, name, type) => {
  if (!type) type = 'flowable';
  const TYPE_TARGET = {
    activiti: 'http://activiti.org/bpmn',
    camunda: 'http://bpmn.io/schema/bpmn',
    flowable: 'http://www.flowable.org/processdef'
  };
  return `<?xml version="1.0" encoding="UTF-8"?><definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL"
             xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
             xmlns:flowable="http://flowable.org/bpmn"
             typeLanguage="http://www.w3.org/2001/XMLSchema"
             expressionLanguage="http://www.w3.org/1999/XPath"
             targetNamespace="${TYPE_TARGET[type]}">
              <process id="${key}" name="${name}" isExecutable="true">
              </process>
              <bpmndi:BPMNDiagram id="BPMNDiagram_1">
                <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="${key}">
                </bpmndi:BPMNPlane>
              </bpmndi:BPMNDiagram>
             </definitions>
             `
};
