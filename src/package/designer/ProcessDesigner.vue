<template>
  <div class="my-process-designer">
    <div class="my-process-designer__header">
      <slot name="control-header"></slot>
      <template v-if="!$slots['control-header']">
        <el-button-group key="align-control">
          <!-- <el-tooltip effect="light" class="align align-left"  content="保存流程">
            <el-button :size="headerButtonSize" icon="Folder" @click="onSave"></el-button>
          </el-tooltip> -->
          <el-tooltip effect="light" class="align align-left"  content="打开文件">
            <el-button :size="headerButtonSize" :icon="FolderOpened" @click="$refs.refFile.click()"></el-button>
          </el-tooltip>
          <el-tooltip effect="light">
            <template #content>
              <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsXml()">下载为XML文件</el-button>
              <br />
              <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsSvg()">下载为SVG文件</el-button>
              <br />
              <el-button :size="headerButtonSize" type="text" @click="downloadProcessAsBpmn()">下载为BPMN文件</el-button>
            </template>
            <el-button :size="headerButtonSize" :icon="Download"></el-button>
          </el-tooltip>
           <el-tooltip effect="light">
            <template #content>
              <el-button :size="headerButtonSize" type="text" @click="previewProcessXML">预览XML</el-button>
              <br />
               <el-button :size="headerButtonSize" type="text" @click="previewProcessJson">预览JSON</el-button>
            </template>
            <el-button :size="headerButtonSize" :icon="View"></el-button>
          </el-tooltip>
          <el-tooltip effect="light" class="align align-left"  content="编辑xml">
            <el-button
            :size="headerButtonSize"
            icon="Edit"
            @click="openXmlEditor">
          </el-button>
          </el-tooltip>
          <el-tooltip effect="light" :content="this.simulationStatus ? '退出模拟' : '开启模拟'">
           <el-button class="align align-left"  :size="headerButtonSize"  :icon="Cpu" @click="processSimulation">
            </el-button>
          </el-tooltip>
          <el-tooltip effect="light" content="向左对齐">
            <el-button :size="headerButtonSize" class="align align-left" :icon="Histogram" @click="elementsAlign('left')" />
          </el-tooltip>
          <el-tooltip effect="light" content="向右对齐">
            <el-button :size="headerButtonSize" class="align align-right" :icon="Histogram" @click="elementsAlign('right')" />
          </el-tooltip>
          <el-tooltip effect="light" content="向上对齐">
            <el-button :size="headerButtonSize" class="align align-top" :icon="Histogram" @click="elementsAlign('top')" />
          </el-tooltip>
          <el-tooltip effect="light" content="向下对齐">
            <el-button :size="headerButtonSize" class="align align-bottom" :icon="Histogram" @click="elementsAlign('bottom')" />
          </el-tooltip>
          <el-tooltip effect="light" content="水平居中">
            <el-button :size="headerButtonSize" class="align align-center" :icon="Histogram" @click="elementsAlign('center')" />
          </el-tooltip>
          <el-tooltip effect="light" content="垂直居中">
            <el-button :size="headerButtonSize" class="align align-middle" :icon="Histogram" @click="elementsAlign('middle')" />
          </el-tooltip>
        </el-button-group>
        <el-button-group key="scale-control">
          <el-tooltip effect="light" content="缩小视图">
            <el-button :size="headerButtonSize" :disabled="defaultZoom < 0.2" :icon="ZoomOut" @click="processZoomOut()" />
          </el-tooltip>
          <el-button :size="headerButtonSize">{{ Math.floor(this.defaultZoom * 10 * 10) + "%" }}</el-button>
          <el-tooltip effect="light" content="放大视图">
            <el-button :size="headerButtonSize" :disabled="defaultZoom > 4" :icon="ZoomIn" @click="processZoomIn()" />
          </el-tooltip>
          <el-tooltip effect="light" content="重置视图并居中">
            <el-button :size="headerButtonSize" :icon="ScaleToOriginal" @click="processReZoom()" />
          </el-tooltip>
        </el-button-group>
        <el-button-group key="stack-control">
          <el-tooltip effect="light" content="撤销">
            <el-button :size="headerButtonSize" :disabled="!revocable" :icon="RefreshLeft" @click="processUndo()" />
          </el-tooltip>
          <el-tooltip effect="light" content="恢复">
            <el-button :size="headerButtonSize" :disabled="!recoverable" :icon="RefreshRight" @click="processRedo()" />
          </el-tooltip>
          <el-tooltip effect="light" content="重新绘制">
            <el-button :size="headerButtonSize" :icon="Refresh" @click="processRestart" />
          </el-tooltip>
        </el-button-group>
        <!-- <el-button-group key="stack-control">
          <el-tooltip effect="light" content="切换主题">
            <el-button :size="headerButtonSize" class="align align-middle" :icon="Sunny" @click="toggleTheme" />
          </el-tooltip>
        </el-button-group> -->
      </template>
      <!-- 用于打开本地文件-->
      <input type="file" id="files" ref="refFile" style="display: none" accept=".xml, .bpmn" @change="importLocalFile" />
    </div>
    <div class="my-process-designer__container">
      <div class="my-process-designer__canvas" ref="bpmn-canvas"></div>
    </div>
    <DrawerEditor ref="drawerEditor" v-model="previewResult" @change="validateAndUpdateXml" />
  </div>
</template>

<script>
import { Histogram, Cpu, Refresh, RefreshLeft, RefreshRight, ZoomOut, ZoomIn, View, Download, FolderOpened, ScaleToOriginal ,Edit, Sunny } from '@element-plus/icons-vue'
import BpmnModeler from "bpmn-js/lib/Modeler";
// import {BpmnModeler} from "bpmn-js";
import DefaultEmptyXML from "./plugins/defaultEmpty";
// 翻译方法
import customTranslate from "./plugins/translate/customTranslate";
import translationsCN from "./plugins/translate/zh";
// 模拟流转流程
import tokenSimulation from "ylz-bpmn-js-token-simulation";
// 标签解析构建器
// import bpmnPropertiesProvider from "bpmn-js-properties-panel/lib/provider/bpmn";
// 标签解析 Moddle
import camundaModdleDescriptor from './plugins/descriptor/camundaDescriptor.json';
import activitiModdleDescriptor from './plugins/descriptor/activitiDescriptor.json';
import flowableModdleDescriptor from './plugins/descriptor/flowableDescriptor.json';
// 标签解析 Extension
import camundaModdleExtension from './plugins/extension-moddle/camunda';
import activitiModdleExtension from './plugins/extension-moddle/activiti';
import flowableModdleExtension from './plugins/extension-moddle/flowable';
// 引入json转换与高亮
// import convert from "xml-js";
import X2JS from "x2js";
import CustomReplaceMenuProvider from './plugins/replace-menu/replaceMenuProvider';

import DrawerEditor from '/@/components/DrawerEditor/index.vue';

export default {
  name: "MyProcessDesigner",
  componentName: "MyProcessDesigner",
  components: {
    DrawerEditor,
  },
  setup() {
    return {
      Histogram, Cpu, Refresh, RefreshLeft, RefreshRight, ZoomOut, ZoomIn, View, Download, FolderOpened, ScaleToOriginal,Edit,Sunny
    }
  },
  emits: ['destroy', 'init-finished', 'commandStack-changed', 'update:modelValue', 'change', 'canvas-viewbox-changed', 'element-click', 'toggle-theme'],
  props: {
    modelValue: String, // xml 字符串
    processId: String,
    processName: String,
    translations: Object, // 自定义的翻译文件
    options: {
      type: Object,
      default: () => ({})
    }, // 自定义的翻译文件
    additionalModel: [Object, Array], // 自定义model
    moddleExtension: Object, // 自定义moddle
    onlyCustomizeAddi: {
      type: Boolean,
      default: false
    },
    onlyCustomizeModdle: {
      type: Boolean,
      default: false
    },
    simulation: {
      type: Boolean,
      default: true
    },
    keyboard: {
      type: Boolean,
      default: true
    },
    prefix: {
      type: String,
      default: "flowable"
    },
    events: {
      type: Array,
      default: () => ["element.click"]
    },
    headerButtonSize: {
      type: String,
      default: "medium",
      validator: value => ["default", "medium", "small", "mini"].indexOf(value) !== -1
    },
    headerButtonType: {
      type: String,
      default: "primary",
      validator: value => ["default", "primary", "success", "warning", "danger", "info"].indexOf(value) !== -1
    }
  },
  data() {
    return {
      defaultZoom: 1,
      simulationStatus: false,
      previewResult: "",
      recoverable: false,
      revocable: false,
    };
  },
  computed: {
    additionalModules() {
      const Modules = [];
      // 仅保留用户自定义扩展模块
      if (this.onlyCustomizeAddi) {
        if (Object.prototype.toString.call(this.additionalModel) === "[object Array]") {
          return this.additionalModel || [];
        }
        return [this.additionalModel];
      }

      // 插入用户自定义扩展模块
      if (Object.prototype.toString.call(this.additionalModel) === "[object Array]") {
        Modules.push(...this.additionalModel);
      } else {
        this.additionalModel && Modules.push(this.additionalModel);
      }

      // 翻译模块
      const TranslateModule = {
        translate: ["value", customTranslate(this.translations || translationsCN)]
      };
      Modules.push(TranslateModule);

      // 模拟流转模块
      if (this.simulation) {
        Modules.push(tokenSimulation);
      }

      // 根据需要的流程类型设置扩展元素构建模块
      // if (this.prefix === "bpmn") {
      //   Modules.push(bpmnModdleExtension);
      // }
      if (this.prefix === "camunda") {
        Modules.push(camundaModdleExtension);
      }
      if (this.prefix === "flowable") {
        Modules.push(flowableModdleExtension);
      }
      if (this.prefix === "activiti") {
        Modules.push(activitiModdleExtension);
      }

      // 注册自定义替换菜单提供者
      Modules.push({
        __init__: ['replaceMenuProvider'],
        replaceMenuProvider: ['type', CustomReplaceMenuProvider]
      });

      return Modules;
    },
    moddleExtensions() {
      const Extensions = {};
      // 仅使用用户自定义模块
      if (this.onlyCustomizeModdle) {
        return this.moddleExtension || null;
      }

      // 插入用户自定义模块
      if (this.moddleExtension) {
        for (let key in this.moddleExtension) {
          Extensions[key] = this.moddleExtension[key];
        }
      }

      // 根据需要的 "流程类型" 设置 对应的解析文件
      if (this.prefix === "activiti") {
        Extensions.activiti = activitiModdleDescriptor;
      }
      if (this.prefix === "flowable") {
        Extensions.flowable = flowableModdleDescriptor;
      }
      if (this.prefix === "camunda") {
        Extensions.camunda = camundaModdleDescriptor;
      }

      return Extensions;
    }
  },
  mounted() {
    this.initBpmnModeler();
    this.createNewDiagram(this.modelValue);
    // this.$once("hook:beforeUnmount", () => {
    //   if (this.bpmnModeler) this.bpmnModeler.destroy();
    //   this.$emit("destroy", this.bpmnModeler);
    //   this.bpmnModeler = null;
    // });
  },
  beforeUnmount() {
    if (this.bpmnModeler) this.bpmnModeler.destroy();
    this.$emit("destroy", this.bpmnModeler);
    this.bpmnModeler = null;
  },
  methods: {
    // 切换主题
    toggleTheme() {
      this.$emit("toggle-theme");
    },
    // 新增：自适应画布大小的完整方法
    fitViewport() {
      const bpmnViewer = this.bpmnModeler;
      if (!bpmnViewer) return;
      try {
        const canvas = bpmnViewer.get('canvas');
        const containerElement = this.$refs["bpmn-canvas"];
        if (!containerElement) return;

        // 获取当前容器的实际尺寸
        const containerWidth = containerElement.clientWidth;
        const containerHeight = containerElement.clientHeight;

        // 获取所有有效元素来计算内容边界
        const elementRegistry = bpmnViewer.get('elementRegistry');
        const elements = elementRegistry
          .getAll()
          .filter((element) => element.id && element.type !== 'bpmn:Process' && element.type !== 'bpmn:Collaboration' && element.type !== 'label');

        if (elements.length === 0) {
          canvas.zoom('fit-viewport');
          return;
        }

        // 计算所有元素的边界框
        let minX = Infinity,
          minY = Infinity,
          maxX = -Infinity,
          maxY = -Infinity;

        elements.forEach((element) => {
          if (element.waypoints) {
            element.waypoints.forEach((point) => {
              minX = Math.min(minX, point.x);
              minY = Math.min(minY, point.y);
              maxX = Math.max(maxX, point.x);
              maxY = Math.max(maxY, point.y);
            });
          } else if (element.x !== undefined && element.y !== undefined) {
            minX = Math.min(minX, element.x);
            minY = Math.min(minY, element.y);
            maxX = Math.max(maxX, element.x + (element.width || 0));
            maxY = Math.max(maxY, element.y + (element.height || 0));
          }
        });

        if (minX === Infinity) {
          canvas.zoom('fit-viewport');
          return;
        }

        // 计算内容的实际尺寸
        const contentWidth = maxX - minX;
        const contentHeight = maxY - minY;

        // 计算内容中心
        const contentCenterX = (minX + maxX) / 2;
        const contentCenterY = (minY + maxY) / 2;

        // 预留边距
        const margin = 60;
        const availableWidth = containerWidth - 2 * margin;
        const availableHeight = containerHeight - 2 * margin;

        // 根据当前容器尺寸计算最佳缩放比例
        const scaleX = availableWidth / contentWidth;
        const scaleY = availableHeight / contentHeight;
        const optimalScale = Math.min(scaleX, scaleY, 1.5); // 最大150%缩放

        // 应用缩放
        canvas.zoom(optimalScale);

        // 等待缩放完成后居中
        setTimeout(() => {
          try {
            // 计算容器中心在画布坐标系中的位置
            const containerCenterX = containerWidth / 2 / optimalScale;
            const containerCenterY = containerHeight / 2 / optimalScale;

            // 设置视口位置实现居中
            canvas.viewbox({
              x: contentCenterX - containerCenterX,
              y: contentCenterY - containerCenterY,
              width: containerWidth / optimalScale,
              height: containerHeight / optimalScale,
            });
          } catch (centerError) {
            // 如果精确居中失败，使用简单的滚动方式
            try {
              const scrollX = containerWidth / 2 - contentCenterX * optimalScale;
              const scrollY = containerHeight / 2 - contentCenterY * optimalScale;

              canvas.scroll({
                dx: scrollX,
                dy: scrollY,
              });
            } catch (scrollError) {
              // 最后的降级方案
              canvas.zoom('fit-viewport');
            }
          }
        }, 100);
      } catch (error) {
        // 如果整个过程失败，至少确保有基本的适配
        try {
          const canvas = bpmnViewer.get('canvas');
          canvas.zoom('fit-viewport');
        } catch (fallbackError) {
          // 静默处理
        }
      }
    },
    onSave () {
      return new Promise((resolve, reject) => {
        if (this.bpmnModeler == null) {
          reject();
        }
        this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
          this.$emit('save', xml);
          resolve(xml);
        });
      })
    },
    initBpmnModeler() {
      if (this.bpmnModeler) return;
      this.bpmnModeler = new BpmnModeler({
        container: this.$refs["bpmn-canvas"],
        keyboard: this.keyboard ? { bindTo: document } : null,
        additionalModules: this.additionalModules,
        moddleExtensions: this.moddleExtensions,
        ...this.options
      });
      this.$emit("init-finished", this.bpmnModeler);
      this.initModelListeners();
    },
    initModelListeners() {
      const EventBus = this.bpmnModeler.get("eventBus");
      const that = this;
      // 注册需要的监听事件, 将. 替换为 - , 避免解析异常
      this.events.forEach(event => {
        EventBus.on(event, function(eventObj) {
          let eventName = event.replace(/\./g, "-");
          let element = eventObj ? eventObj.element : null;
          that.$emit(eventName, element, eventObj);
        });
      });
      // 监听图形改变返回xml
      EventBus.on("commandStack.changed", async event => {
        try {
          this.recoverable = this.bpmnModeler.get("commandStack").canRedo();
          this.revocable = this.bpmnModeler.get("commandStack").canUndo();
          let { xml } = await this.bpmnModeler.saveXML({ format: true });
          this.$emit("commandStack-changed", event);
          this.$emit('update:modelValue', xml);
          this.$emit("change", xml);
        } catch (e) {
          console.error(`[Process Designer Warn]: ${e.message || e}`);
        }
      });
      // 监听视图缩放变化
      this.bpmnModeler.on("canvas.viewbox.changed", ({ viewbox }) => {
        this.$emit("canvas-viewbox-changed", { viewbox });
        const { scale } = viewbox;
        this.defaultZoom = Math.floor(scale * 100) / 100;
      });
    },
    /* 创建新的流程图 */
    async createNewDiagram(xml) {
      // 将字符串转换成图显示出来
      let newId = this.processId || `Process_${new Date().getTime()}`;
      let newName = this.processName || `业务流程_${new Date().getTime()}`;
      let xmlString = xml || DefaultEmptyXML(newId, newName, this.prefix);
      try {
        let { warnings } = await this.bpmnModeler.importXML(xmlString);
        if (warnings && warnings.length) {
          warnings.forEach(warn => console.warn(warn));
        }
      } catch (e) {
        console.error(`[Process Designer Warn]: ${e?.message || e}`);
      }
    },

    // 下载流程图到本地
    /**
     * @param {string} type
     * @param {*} name
     */
    async downloadProcess(type, name) {
      try {
        const _this = this;
        // 按需要类型创建文件并下载
        if (type === "xml" || type === "bpmn") {
          const { err, xml } = await this.bpmnModeler.saveXML();
          // 读取异常时抛出异常
          if (err) {
            console.error(`[Process Designer Warn ]: ${err.message || err}`);
          }
          let { href, filename } = _this.setEncoded(type.toUpperCase(), name, xml);
          downloadFunc(href, filename);
        } else {
          const { err, svg } = await this.bpmnModeler.saveSVG();
          // 读取异常时抛出异常
          if (err) {
            return console.error(err);
          }
          let { href, filename } = _this.setEncoded("SVG", name, svg);
          downloadFunc(href, filename);
        }
      } catch (e) {
        console.error(`[Process Designer Warn ]: ${e.message || e}`);
      }
      // 文件下载方法
      function downloadFunc(href, filename) {
        if (href && filename) {
          let a = document.createElement("a");
          a.download = filename; //指定下载的文件名
          a.href = href; //  URL对象
          a.click(); // 模拟点击
          URL.revokeObjectURL(a.href); // 释放URL 对象
        }
      }
    },

    // 根据所需类型进行转码并返回下载地址
    setEncoded(type, filename = "diagram", data) {
      const encodedData = encodeURIComponent(data);
      return {
        filename: `${filename}.${type}`,
        href: `data:application/${type === "svg" ? "text/xml" : "bpmn20-xml"};charset=UTF-8,${encodedData}`,
        data: data
      };
    },

    // 加载本地文件
    importLocalFile() {
      const that = this;
      const file = this.$refs.refFile.files[0];
      const reader = new FileReader();
      reader.readAsText(file);
      reader.onload = function() {
        let xmlStr = this.result;
        that.createNewDiagram(xmlStr);
      };
    },
    /* ------------------------------------------------ refs methods ------------------------------------------------------ */
    async downloadProcessAsXml() {
      try {
        // 获取格式化后的XML
        const { err, xml } = await this.bpmnModeler.saveXML({ format: true });

        // 读取异常时抛出异常
        if (err) {
          console.error(`[Process Designer Warn]: ${err.message || err}`);
          return;
        }

        // 创建下载链接
        const encodedData = encodeURIComponent(xml);
        const filename = `流程图_${new Date().getTime()}.xml`;
        const href = `data:application/bpmn20-xml;charset=UTF-8,${encodedData}`;

        // 执行下载
        const a = document.createElement("a");
        a.download = filename;
        a.href = href;
        a.click();
        URL.revokeObjectURL(a.href);

      } catch (e) {
        console.error(`[Process Designer Warn]: ${e.message || e}`);
      }
    },
    downloadProcessAsBpmn() {
      this.downloadProcess("bpmn");
    },
    downloadProcessAsSvg() {
      this.downloadProcess("svg");
    },
    processSimulation() {
      this.simulationStatus = !this.simulationStatus;
      this.simulation && this.bpmnModeler.get("toggleMode").toggleMode();
    },
    processRedo() {
      this.bpmnModeler.get("commandStack").redo();
    },
    processUndo() {
      this.bpmnModeler.get("commandStack").undo();
    },
    processZoomIn(zoomStep = 0.1) {
      let newZoom = Math.floor(this.defaultZoom * 100 + zoomStep * 100) / 100;
      if (newZoom > 4) {
        throw new Error("[Process Designer Warn ]: The zoom ratio cannot be greater than 4");
      }
      this.defaultZoom = newZoom;
      this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
    },
    processZoomOut(zoomStep = 0.1) {
      let newZoom = Math.floor(this.defaultZoom * 100 - zoomStep * 100) / 100;
      if (newZoom < 0.2) {
        throw new Error("[Process Designer Warn ]: The zoom ratio cannot be less than 0.2");
      }
      this.defaultZoom = newZoom;
      this.bpmnModeler.get("canvas").zoom(this.defaultZoom);
    },
    processZoomTo(newZoom = 1) {
      if (newZoom < 0.2) {
        throw new Error("[Process Designer Warn ]: The zoom ratio cannot be less than 0.2");
      }
      if (newZoom > 4) {
        throw new Error("[Process Designer Warn ]: The zoom ratio cannot be greater than 4");
      }
      this.defaultZoom = newZoom;
      this.bpmnModeler.get("canvas").zoom(newZoom);
    },
    processReZoom() {
      this.defaultZoom = 1;
      this.bpmnModeler.get("canvas").zoom("fit-viewport", "auto");
    },
    processRestart() {
      this.recoverable = false;
      this.revocable = false;
      this.createNewDiagram(null);
    },
    elementsAlign(align) {
      const Align = this.bpmnModeler.get("alignElements");
      const Selection = this.bpmnModeler.get("selection");
      const SelectedElements = Selection.get();
      if (!SelectedElements || SelectedElements.length <= 1) {
        this.$message.warning("请按住 Ctrl 键选择多个元素对齐");
        return;
      }
      this.$confirm("自动对齐可能造成图形变形，是否继续？", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => Align.trigger(SelectedElements, align));
    },
    /*-----------------------------    方法结束     ---------------------------------*/
    previewProcessXML() {
      this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
        // 确保使用最新XML内容
        this.previewResult = xml;
        this.$refs.drawerEditor.show({
          language: 'xml',
          disabled: true,
        })
      });
    },
    previewProcessJson() {
      // this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
      //   this.previewResult = convert.xml2json(xml, { spaces: 2 });
      //   this.previewType = "json";
      //   this.previewModelVisible = true;
      // });
      const newConvert = new X2JS();
      this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
        const { definitions } = newConvert.xml2js(xml);
        if (definitions) {
          this.previewResult = JSON.stringify(definitions, null, 4);
        } else {
          this.previewResult = "";
        }
        this.$refs.drawerEditor.show({
          language: 'json',
          disabled: true,
        })

      });
    },
    openXmlEditor() {

    this.bpmnModeler.saveXML({ format: true }).then(({ xml }) => {
      this.previewResult = xml;
      this.$refs.drawerEditor.show({
        language: 'xml',
        disabled: false,
      })

    });
  },
  validateAndUpdateXml() {
    try {
      // 校验XML格式
      // const parser = new DOMParser();
      // const xmlDoc = parser.parseFromString(this.previewResult, "text/xml");

      // if (xmlDoc.getElementsByTagName("parsererror").length > 0) {
      //   throw new Error('XML格式错误，请检查修正');
      // }

      // 重新导入验证通过的XML
      this.createNewDiagram(this.previewResult);

      // 触发更新事件，确保状态同步
      this.$emit('update:modelValue', this.previewResult);
      this.$emit('change', this.previewResult);

      this.$message.success('XML更新成功');
    } catch (error) {
      this.$message.error(error.message || 'XML格式校验失败');
    }
  }
  }
};
</script>
<style scoped lang="scss">
</style>
<style>
.code-editor-dialog .code-editor-wrapper{
  height:600px;
}
</style>