@import "diagram-js-minimap/assets/diagram-js-minimap.css";

// 边框被 token-simulation 样式覆盖了
.djs-palette {
  background: var(--palette-background-color);
  border: solid 1px var(--palette-border-color) !important;
  border-radius: 2px;
}

.my-process-designer {
  display: flex;
  flex-direction: column;
  // width: 100%;
  // height: 100%;
  flex: 1;
  box-sizing: border-box;
  .my-process-designer__header {
    width: 100%;
    // min-height: 36px;
    .el-button {
      text-align: center;
    }
    .el-button-group {
      // margin: 4px;
      margin-right: 4px;
    }
    .el-tooltip__popper {
      .el-button {
        width: 100%;
        text-align: left;
        padding-left: 8px;
        padding-right: 8px;
      }
      .el-button:hover {
        background: rgba(64, 158, 255, 0.8);
        color: #ffffff;
      }
    }
    .align {
      position: relative;
      i {
        &:after {
          content: "|";
          position: absolute;
          transform: rotate(90deg) translate(200%, -10%);
        }
      }
    }
    .align.align-left i {
      transform: rotate(90deg);
    }
    .align.align-right i {
      transform: rotate(-90deg);
    }
    .align.align-top i {
      transform: rotate(180deg);
    }
    .align.align-bottom i {
      transform: rotate(0deg);
    }
    .align.align-center i {
      transform: rotate(90deg);
      &:after {
        transform: rotate(90deg) translate(0, -10%);
      }
    }
    .align.align-middle i {
      transform: rotate(0deg);
      &:after {
        transform: rotate(90deg) translate(0, -10%);
      }
    }
  }
  .my-process-designer__container {
    display: inline-flex;
    width: 100%;
    flex: 1;
    .my-process-designer__canvas {
      flex: 1;
      height: 100%;
      position: relative;
      background: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGRlZnM+PHBhdHRlcm4gaWQ9ImEiIHdpZHRoPSI0MCIgaGVpZ2h0PSI0MCIgcGF0dGVyblVuaXRzPSJ1c2VyU3BhY2VPblVzZSI+PHBhdGggZD0iTTAgMTBoNDBNMTAgMHY0ME0wIDIwaDQwTTIwIDB2NDBNMCAzMGg0ME0zMCAwdjQwIiBmaWxsPSJub25lIiBzdHJva2U9IiNlMGUwZTAiIG9wYWNpdHk9Ii4yIi8+PHBhdGggZD0iTTQwIDBIMHY0MCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZTBlMGUwIi8+PC9wYXR0ZXJuPjwvZGVmcz48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSJ1cmwoI2EpIi8+PC9zdmc+")
      repeat !important;
      div.toggle-mode {
        display: none;
      }
      .bjs-container {
        border: none;
      }
      .djs-palette {
        top:0;
        left:0;
        width:50px !important;
        background: #fff;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        box-sizing: border-box;
        .group{
          display: flex;
          flex-direction: column;
          align-items: center;
          .entry {
            height: 40px;
            line-height: 40px;
          }
        }
      }
    }
    .my-process-designer__property-panel {
      height: 100%;
      overflow: scroll;
      overflow-y: auto;
      z-index: 10;
      * {
        box-sizing: border-box;
      }
    }
    svg {
      width: 100%;
      height: 100%;
      min-height: 100%;
      overflow: hidden;
    }
  }
}

//侧边栏配置
.djs-palette.open {
  .djs-palette-entries {
    div[class^="bpmn-icon-"]:before,
    div[class*="bpmn-icon-"]:before {
      line-height: unset;
    }
    div.entry {
      position: relative;
    }
    div.entry:hover {
      &::after {
        width: max-content;
        content: attr(title);
        vertical-align: text-bottom;
        position: absolute;
        right: -10px;
        top: 0;
        bottom: 0;
        overflow: hidden;
        transform: translateX(100%);
        font-size: 0.5em;
        display: inline-block;
        text-decoration: inherit;
        font-variant: normal;
        text-transform: none;
        background: #fafafa;
        box-shadow: 0 0 6px #eeeeee;
        border: 1px solid #cccccc;
        box-sizing: border-box;
        padding: 0 16px;
        border-radius: 4px;
        z-index: 100;
      }
    }
  }
}
pre {
  margin: 0;
  height: 100%;
  overflow: hidden;
  max-height: calc(80vh - 32px);
  overflow-y: auto;
}
.hljs {
  word-break: break-word;
  white-space: pre-wrap;
}
.hljs * {
  font-family: Consolas, Monaco, monospace;
}

.custom-icon-flow-task {
  background-image: url('data:image/svg+xml;utf8,<svg t="1750150312506" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7270" width="40" height="40"><path d="M212.992 526.336 212.992 526.336 212.992 526.336 215.04 526.336 212.992 526.336Z" p-id="7271" fill="%23333"></path><path d="M813.056 768l96.256 0c20.48 0 38.912 16.384 38.912 38.912l0 96.256c0 20.48-16.384 38.912-38.912 38.912l-96.256 0c-20.48 0-38.912-16.384-38.912-38.912l0-38.912-18.432 0c-53.248 0-96.256-43.008-96.256-96.256L659.456 614.4l-96.256 0 0 192.512c0 20.48-16.384 38.912-38.912 38.912L120.832 845.824c-20.48 0-38.912-16.384-38.912-38.912L81.92 401.408c0-20.48 16.384-38.912 38.912-38.912l192.512 0L313.344 190.464 120.832 190.464C98.304 190.464 81.92 174.08 81.92 151.552L81.92 112.64c0-20.48 16.384-38.912 38.912-38.912l788.48 0c20.48 0 38.912 16.384 38.912 38.912l0 38.912c0 20.48-16.384 38.912-38.912 38.912L331.776 190.464l0 174.08 192.512 0c20.48 0 38.912 16.384 38.912 38.912L563.2 593.92l96.256 0 0-153.6c0-53.248 43.008-96.256 96.256-96.256l18.432 0 0-38.912c0-20.48 16.384-38.912 38.912-38.912l96.256 0c20.48 0 38.912 16.384 38.912 38.912l0 96.256c0 20.48-16.384 38.912-38.912 38.912l-96.256 0c-20.48 0-38.912-16.384-38.912-38.912l0-38.912-18.432 0c-43.008 0-77.824 34.816-77.824 77.824L677.888 593.92l96.256 0 0-38.912c0-20.48 16.384-38.912 38.912-38.912l96.256 0c20.48 0 38.912 16.384 38.912 38.912l0 96.256c0 20.48-16.384 38.912-38.912 38.912l-96.256 0c-20.48 0-38.912-16.384-38.912-38.912L774.144 614.4l-96.256 0 0 153.6c0 43.008 34.816 77.824 77.824 77.824l18.432 0 0-38.912C774.144 784.384 792.576 768 813.056 768z" p-id="7272" fill="%23333"></path></svg>');
  background-size: 30px 30px;
  background-repeat: no-repeat;
  display: inline-block;
  background-position: center;
}

.custom-icon-cc-task {
  background-image: url('data:image/svg+xml;utf8,<svg t="1750151143806" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="21756" width="64" height="64"><path d="M665.312539 1022.083593a32.578915 32.578915 0 0 1-28.746101-18.525265l-191.640674-411.388646-419.693075-191.640674a31.30131 31.30131 0 0 1-19.164068-30.662508 31.940112 31.940112 0 0 1 20.441672-24.913287L976.409233 9.582034a31.940112 31.940112 0 0 1 33.217716 7.026824 33.856519 33.856519 0 0 1 7.665627 33.217717l-319.401122 953.092951a31.940112 31.940112 0 0 1-28.746102 21.080474zM122.33063 378.809732l358.36806 162.894572a28.746101 28.746101 0 0 1 15.970056 15.331254l164.172177 350.063631 274.684966-815.750468z" fill="%23707070" p-id="21757"></path><path d="M467.283843 602.390518a34.495321 34.495321 0 0 1-19.164068-8.943232 31.940112 31.940112 0 0 1 0-45.354959L964.27199 17.247661a31.940112 31.940112 0 0 1 45.354959 0 31.940112 31.940112 0 0 1 0-45.354959l-519.346225 530.205864a32.578915 32.578915 0 0 1-22.996881 9.582034z" fill="%23707070" p-id="21758"></path></svg>');
  background-size: 30px 30px;
  background-repeat: no-repeat;
  display: inline-block;
  background-position: center;
}