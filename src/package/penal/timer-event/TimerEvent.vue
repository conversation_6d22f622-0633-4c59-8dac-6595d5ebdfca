<template>
  <div class="timer-event-config">
    <el-form size="small" label-width="80px" @submit.prevent>
      <!-- 时间类型选择 -->
      <el-form-item label="类型">
        <el-radio-group v-model="timerConfig.timerType" @change="onTimerTypeChange">
          <el-radio label="timeDate">时间</el-radio>
          <el-radio label="timeDuration">持续</el-radio>
          <el-radio label="timeCycle">循环</el-radio>
        </el-radio-group>
      </el-form-item>

      <!-- 条件输入 -->
      <el-form-item label="条件">
        <el-input 
          v-model="timerConfig.timerValue" 
          :placeholder="getPlaceholderText()"
          clearable 
          @input="updateTimerEvent"
          @change="updateTimerEvent"
        />
        <div class="timer-help-text">
          {{ getHelpText() }}
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "TimerEvent",
  props: {
    id: String,
    type: String,
    scene: String
  },
  data() {
    return {
      timerConfig: {
        timerType: 'timeDate',
        timerValue: ''
      }
    };
  },
  computed: {
    isFormDisabled() {
      return !!(this.scene === 'DETAIL');
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetTimerConfig());
      }
    }
  },
  methods: {
    resetTimerConfig() {
      if (!this.bpmnElement) return;
      
      const businessObject = this.bpmnElement.businessObject;
      if (businessObject && businessObject.eventDefinitions && businessObject.eventDefinitions.length > 0) {
        const timerEventDef = businessObject.eventDefinitions.find(def => 
          def.$type === 'bpmn:TimerEventDefinition'
        );
        
        if (timerEventDef) {
          // 检查定时器类型和值
          if (timerEventDef.timeDate) {
            this.timerConfig.timerType = 'timeDate';
            this.timerConfig.timerValue = timerEventDef.timeDate.body || '';
          } else if (timerEventDef.timeDuration) {
            this.timerConfig.timerType = 'timeDuration';
            this.timerConfig.timerValue = timerEventDef.timeDuration.body || '';
          } else if (timerEventDef.timeCycle) {
            this.timerConfig.timerType = 'timeCycle';
            this.timerConfig.timerValue = timerEventDef.timeCycle.body || '';
          }
        }
      } else {
        // 默认配置
        this.timerConfig = {
          timerType: 'timeDate',
          timerValue: ''
        };
      }
    },
    
    onTimerTypeChange() {
      // 切换类型时清空条件值
      this.timerConfig.timerValue = '';
      this.updateTimerEvent();
    },
    
    updateTimerEvent() {
      if (!this.bpmnElement) return;
      
      const modeling = window.bpmnInstances.modeling;
      const moddle = window.bpmnInstances.moddle;
      const businessObject = this.bpmnElement.businessObject;
      
      // 查找或创建 TimerEventDefinition
      let timerEventDef = null;
      let eventDefinitions = businessObject.eventDefinitions || [];
      
      // 查找现有的 TimerEventDefinition
      timerEventDef = eventDefinitions.find(def => def.$type === 'bpmn:TimerEventDefinition');
      
      if (!timerEventDef) {
        // 创建新的 TimerEventDefinition
        timerEventDef = moddle.create('bpmn:TimerEventDefinition');
        eventDefinitions = [...eventDefinitions, timerEventDef];
      }
      
      // 清除所有时间定义
      delete timerEventDef.timeDate;
      delete timerEventDef.timeDuration;
      delete timerEventDef.timeCycle;
      
      // 设置对应的时间定义
      if (this.timerConfig.timerValue && this.timerConfig.timerValue.trim()) {
        const formalExpression = moddle.create('bpmn:FormalExpression', {
          body: this.timerConfig.timerValue.trim()
        });
        
        timerEventDef[this.timerConfig.timerType] = formalExpression;
      }
      
      // 更新元素的事件定义
      modeling.updateProperties(this.bpmnElement, {
        eventDefinitions: eventDefinitions
      });
    },
    
    getPlaceholderText() {
      switch (this.timerConfig.timerType) {
        case 'timeDate':
          return '如：2023-12-25T10:00:00';
        case 'timeDuration':
          return '如：PT5M (5分钟)';
        case 'timeCycle':
          return '如：R3/PT10M (重复3次，每10分钟)';
        default:
          return '请输入条件';
      }
    },
    
    getHelpText() {
      switch (this.timerConfig.timerType) {
        case 'timeDate':
          return '指定具体的日期时间，格式：ISO 8601';
        case 'timeDuration':
          return '指定持续时间，格式：ISO 8601 持续时间 (如 PT1H30M)';
        case 'timeCycle':
          return '指定循环时间，格式：ISO 8601 重复间隔 (如 R/PT1H)';
        default:
          return '';
      }
    }
  },
  beforeUnmount() {
    this.bpmnElement = null;
  }
};
</script>

<style lang="scss" scoped>
.timer-event-config {
  padding: 16px 0;
  
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .el-radio-group {
    .el-radio {
      margin-right: 15px;
      
      &:last-child {
        margin-right: 0;
      }
    }
  }
  
  .timer-help-text {
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-color-info);
    line-height: 1.4;
  }
}
</style> 