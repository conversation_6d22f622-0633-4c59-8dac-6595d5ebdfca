<template>
  <div class="panel-tab__content">
    <el-form
      label-width="60px"
      size="small"
      label-position="left"
      :disabled="isFormDisabled"
    >
      <el-form-item label="审批流">
        <el-select
          v-model="calledElement"
          filterable
          placeholder="请选择审批流"
          style="width: 100%"
          @change="setCurrentFormList"
          remote
          reserve-keyword
          :remote-method="remoteMethod"
        >
          <el-option
            v-for="item in flowList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-divider>输入参数映射</el-divider>
      <el-table
        :data="inputMappings"
        size="small"
        style="margin-bottom: 10px"
      >
        <el-table-column
          prop="source"
          label="源变量"
        >
          <template #default="{ row }">
              <el-input
                style="width: 100%; box-sizing: border-box"
                placeholder="请输入源变量"
                v-model="row.source"
                size="mini"
              ></el-input>
          </template>
        </el-table-column>
        <el-table-column
          prop="target"
          label="目标变量"
        />
        <el-table-column
          prop="targetName"
          label="目标名称"
        />
      </el-table>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, inject, onMounted } from 'vue';
// import { ElMessage } from 'yun-design';
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';
import { getAllProcess } from '/@/api/workflow/process-management';

interface Props {
  id?: string;
  type?: string;
  scene?: string;
}

interface MappingForm {
  source: string;
  target: string;
}

interface InputMapping extends MappingForm {
  targetName?: string;
}

interface OutputMapping extends MappingForm {}

// Props
const props = withDefaults(defineProps<Props>(), {
  id: '',
  type: '',
  scene: '',
});

// Inject
const prefix = inject<string>('prefix', 'flowable');

// Reactive state
const bpmnElement = ref<any>(null);
const calledElement = ref<string>('');
const inputMappings = ref<InputMapping[]>([]);
const outputMappings = ref<OutputMapping[]>([]);
const currentFormList = ref<any[]>([]);
const flowList = ref<any[]>([]);
function setCurrentFormList() {
  const flowKey = calledElement?.value;
  if (flowKey) {
    const data = flowList?.value?.find((item: any) => item.flowKey === flowKey)?.formList || [];
    currentFormList.value = data?.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.id,
    }));
    // 设置输入映射
    inputMappings.value = currentFormList.value.map((item: any) => ({
      source: item.value,
      target: item.value,
      targetName: item.name,
    }));
    // 设置输出映射-固定成当前流程的flowKey
    outputMappings.value = [{ source: flowKey, target: flowKey }];
  }
}
async function loadProcessList(ext = {}) {
  try {
    const res = await getAllProcess({ ...ext });
    flowList.value = res?.data?.map((item: any) => ({
      ...item,
      label: item.name,
      value: item.flowKey,
    }));
  } catch (err) {
    // eslint-disable-next-line no-console
    console.error(err);
    // flowList.value = [];
  }
}
const remoteMethod = (query: string) => {
  loadProcessList({ keyword: query || null });
}

// Computed
const isFormDisabled = computed(() => props.scene === 'DETAIL');

// Methods
function initCalledElementAndMappings(): void {
  const currentBpmnInstances = (window as any).bpmnInstances;
  const selectedElements = currentBpmnInstances?.selection?.get();
  bpmnElement.value = selectedElements?.length > 0 ? selectedElements[0] : null;

  if (!bpmnElement.value) {
    resetData();
    return;
  }

  const businessObject = getBusinessObject(bpmnElement.value);
  calledElement.value = businessObject.calledElement || '';

  // Reset mappings
  inputMappings.value = [];
  outputMappings.value = [];

  // Parse extensions
  const extensions = businessObject.extensionElements?.values || [];

  extensions.forEach((ext: any) => {
    // 其他属性都在$attrs内
    const attrs = ext?.['$attrs'] || {};
    if (ext.$type === `${prefix}:In`) {
      inputMappings.value.push({ source: ext.source, target: ext.target, ...attrs });
    } else if (ext.$type === `${prefix}:Out`) {
      outputMappings.value.push({ source: ext.source, target: ext.target, ...attrs });
    }
  });
}

function resetData(): void {
  calledElement.value = '';
  inputMappings.value = [];
  outputMappings.value = [];
}

function saveConfig(): void {
  const currentBpmnInstances = (window as any).bpmnInstances;
  const selectedElements = currentBpmnInstances?.selection?.get();
  const currentBpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;

  if (!currentBpmnElement) return;

  const modeling = currentBpmnInstances.modeling;
  const bpmnFactory = currentBpmnInstances.bpmnFactory;

  // Update called element
  modeling.updateProperties(currentBpmnElement, {
    calledElement: calledElement.value,
  });

  const businessObject = getBusinessObject(currentBpmnElement);
  const newExtensionsValues: any[] = [];

  // Preserve existing extensions (except In/Out mappings)
  if (businessObject.extensionElements?.values) {
    businessObject.extensionElements.values.forEach((ext: any) => {
      if (ext.$type !== `${prefix}:In` && ext.$type !== `${prefix}:Out`) {
        const recreatedExt = bpmnFactory.create(ext.$type, { ...ext });
        newExtensionsValues.push(recreatedExt);
      }
    });
  }

  // Add input mappings
  inputMappings.value.forEach((mapping) => {
    if (mapping.source && mapping.target) {
      const inMapping = bpmnFactory.create(`${prefix}:In`, {
        source: mapping.source,
        target: mapping.target,
        targetName: mapping.targetName,
      });
      newExtensionsValues.push(inMapping);
    }
  });

  // Add output mappings
  outputMappings.value.forEach((mapping) => {
    if (mapping.source && mapping.target) {
      const outMapping = bpmnFactory.create(`${prefix}:Out`, {
        source: mapping.source,
        target: mapping.target,
      });
      newExtensionsValues.push(outMapping);
    }
  });

  // Update extension elements
  if (newExtensionsValues.length > 0) {
    if (businessObject.extensionElements) {
      modeling.updateModdleProperties(currentBpmnElement, businessObject.extensionElements, { values: newExtensionsValues });
    } else {
      const newExtElements = bpmnFactory.create('bpmn:ExtensionElements', {
        values: newExtensionsValues,
      });
      modeling.updateProperties(currentBpmnElement, {
        extensionElements: newExtElements,
      });
    }
  } else if (businessObject.extensionElements) {
    modeling.updateProperties(currentBpmnElement, {
      extensionElements: undefined,
    });
  }
}

onMounted(() => {
  loadProcessList();
});

// Watchers
watch(
  () => props.id,
  () => {
    nextTick(() => {
      initCalledElementAndMappings();
    });
  },
  { immediate: true }
);

watch(calledElement, saveConfig);
watch(inputMappings, saveConfig, { deep: true });
watch(outputMappings, saveConfig, { deep: true });
</script>

<style scoped>
.mapping-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px dashed theme('colors.gray.200');
  border-radius: 4px;
  @apply dark:border-gray-600;
}

/* 抽屉标题样式 */
.custom-mapping-drawer :deep(.el-drawer__header) {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  padding-bottom: 8px;
}

/* 按钮区样式 */
.element-drawer__button {
  @apply flex justify-end gap-2 p-3 border-t border-gray-100 bg-white;
  @apply dark:border-gray-700 dark:bg-gray-800;
}
</style>
