<template>
    <el-form label-position="top" class="p-2">
    <el-form-item label="信号选择：">
      <el-select
    v-model="selected"
    placeholder="请选择信号"
    class="w-full"
    :class="[$attrs.class]"
    @change="onChange"
    clearable
  >
    <el-option
      v-for="item in options"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    />
  </el-select>
    </el-form-item>
  </el-form>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue'

interface SignalOption {
  label: string
  value: string | number
}

const props = defineProps<{
  modelValue: string | number | null
  options: SignalOption[]
}>()

const emit = defineEmits(['update:modelValue', 'change'])
const selected = ref(props.modelValue)

watch(() => props.modelValue, val => {
  selected.value = val
})

function onChange(val: string | number) {
  emit('update:modelValue', val)
  emit('change', val)
}
</script>

<style scoped lang="scss">
.el-select {
  @apply dark:bg-gray-800 dark:text-gray-100;
}
</style> 