<template>
  <div class="condition-event-config">
    <el-form size="small" label-width="90px" @submit.prevent>
      <!-- 完成条件 -->
      <el-form-item label="完成条件">
        <el-input 
          v-model="conditionConfig.condition" 
          type="textarea"
          :rows="3"
          placeholder="请输入条件表达式，如：${amount > 1000}"
          clearable 
          @input="updateConditionEvent"
          @change="updateConditionEvent"
        />
        <div class="condition-help-text">
          支持表达式语言（EL），如：${变量名 > 值}、${status == 'approved'}
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "ConditionEvent",
  props: {
    id: String,
    type: String,
    scene: String
  },
  data() {
    return {
      conditionConfig: {
        condition: ''
      }
    };
  },
  computed: {
    isFormDisabled() {
      return !!(this.scene === 'DETAIL');
    }
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        this.$nextTick(() => this.resetConditionConfig());
      }
    }
  },
  methods: {
    resetConditionConfig() {
      if (!this.bpmnElement) return;
      
      const businessObject = this.bpmnElement.businessObject;
      if (businessObject && businessObject.eventDefinitions && businessObject.eventDefinitions.length > 0) {
        const conditionEventDef = businessObject.eventDefinitions.find(def => 
          def.$type === 'bpmn:ConditionalEventDefinition'
        );
        
        if (conditionEventDef && conditionEventDef.condition) {
          this.conditionConfig.condition = conditionEventDef.condition.body || '';
        }
      } else {
        // 默认配置
        this.conditionConfig = {
          condition: ''
        };
      }
    },
    
    updateConditionEvent() {
      if (!this.bpmnElement) return;
      
      const modeling = window.bpmnInstances.modeling;
      const moddle = window.bpmnInstances.moddle;
      const businessObject = this.bpmnElement.businessObject;
      
      // 查找或创建 ConditionalEventDefinition
      let conditionEventDef = null;
      let eventDefinitions = businessObject.eventDefinitions || [];
      
      // 查找现有的 ConditionalEventDefinition
      conditionEventDef = eventDefinitions.find(def => def.$type === 'bpmn:ConditionalEventDefinition');
      
      if (!conditionEventDef) {
        // 创建新的 ConditionalEventDefinition
        conditionEventDef = moddle.create('bpmn:ConditionalEventDefinition');
        eventDefinitions = [...eventDefinitions, conditionEventDef];
      }
      
      // 设置条件
      if (this.conditionConfig.condition && this.conditionConfig.condition.trim()) {
        const formalExpression = moddle.create('bpmn:FormalExpression', {
          body: this.conditionConfig.condition.trim()
        });
        
        conditionEventDef.condition = formalExpression;
      } else {
        // 清空条件
        delete conditionEventDef.condition;
      }
      
      // 更新元素的事件定义
      modeling.updateProperties(this.bpmnElement, {
        eventDefinitions: eventDefinitions
      });
    }
  },
  beforeUnmount() {
    this.bpmnElement = null;
  }
};
</script>

<style lang="scss" scoped>
.condition-event-config {
  padding: 16px 0;
  
  .el-form-item {
    margin-bottom: 20px;
  }
  
  .condition-help-text {
    margin-top: 4px;
    font-size: 12px;
    color: var(--el-color-info);
    line-height: 1.4;
  }
}
</style> 