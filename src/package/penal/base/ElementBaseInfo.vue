<template>
  <div class="panel-tab__content">
    <el-form size="small" label-width="90px" @submit.prevent :disabled="isFormDisabled">
      <el-form-item label="ID">
        <el-input v-model="elementBaseInfo.id" :disabled="idEditDisabled || isDisabled" clearable @change="updateBaseInfo('id')" />
      </el-form-item>
      <el-form-item label="名称">
        <el-input v-model="elementBaseInfo.name" :disabled="isDisabled" clearable @change="updateBaseInfo('name')" />
      </el-form-item>
      <el-form-item v-if="type==='StartEvent'" label="发起人">
        <el-input v-model="elementBaseInfo['initiator']" clearable @change="updateBaseInfo('initiator')" />
      </el-form-item>
      <template v-if="elementBaseInfo.$type&&elementBaseInfo.$type.includes('Task')">
        <el-form-item label="是否异步">
          <el-switch v-model="elementBaseInfo.async" active-text="是" inactive-text="否" @change="updateBaseInfo('async')" />
        </el-form-item>
        <el-form-item label="跳过表达式">
          <el-input v-model="elementBaseInfo['assignee']" clearable @change="updateBaseInfo('assignee')" />
        </el-form-item>
      </template>

      <!--流程的基础属性-->
      <template v-if="elementBaseInfo.$type === 'bpmn:Process'">
        <el-form-item label="版本标签">
          <el-input v-model="elementBaseInfo.versionTag" clearable @change="updateBaseInfo('versionTag')" />
        </el-form-item>
        <el-form-item label="可执行">
          <el-switch v-model="elementBaseInfo.isExecutable" active-text="是" inactive-text="否" @change="updateBaseInfo('isExecutable')" />
        </el-form-item>

      </template>
      <el-form-item v-if="elementBaseInfo.$type === 'bpmn:SubProcess'" label="状态">
        <el-switch v-model="elementBaseInfo.isExpanded" active-text="展开" inactive-text="折叠" @change="updateBaseInfo('isExpanded')" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: "ElementBaseInfo",
  props: {
    businessObject: Object,
    type: String,
    idEditDisabled: {
      type: Boolean,
      default: true
    },
    scene: {
      type: String,
      default: null
    }
  },
  computed: {
    isDisabled() {
      // 流程编辑场景下，不可编辑
      return !!(this.scene === 'PROCESS_EDIT' && this.type === 'Process')
    },
    isFormDisabled() {
      return !!(this.scene === 'DETAIL')
    }
  },
  data() {
    return {
      elementBaseInfo: {}
    };
  },
  watch: {
    businessObject: {
      immediate: false,
      handler: function(val) {
        if (val) {
          this.$nextTick(() => this.resetBaseInfo());
        }
      }
    }
  },
  methods: {
    resetBaseInfo() {
      this.bpmnElement = window?.bpmnInstances?.bpmnElement || {};
      this.elementBaseInfo = JSON.parse(JSON.stringify(this.bpmnElement.businessObject));
      if (this.elementBaseInfo && this.elementBaseInfo.$type === "bpmn:SubProcess") {
        this.elementBaseInfo["isExpanded"] = this.elementBaseInfo.di?.isExpanded
      }
    },
    updateBaseInfo(key) {
      if (key === "id") {
        window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
          id: this.elementBaseInfo[key],
          di: { id: `${this.elementBaseInfo[key]}_di` }
        });
        return;
      }
      if (key === "isExpanded") {
        window?.bpmnInstances?.modeling.toggleCollapse(this.bpmnElement);
        return;
      }
      const attrObj = Object.create(null);
      attrObj[key] = this.elementBaseInfo[key];
      console.log(this.elementBaseInfo,'this.elementBaseInfo')
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, attrObj);
    }
  },
  beforeUnmount() {
    this.bpmnElement = null;
  }
};
</script>
