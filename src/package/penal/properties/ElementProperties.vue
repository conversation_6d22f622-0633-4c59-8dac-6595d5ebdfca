<template>
  <div class="panel-tab__content">
    <el-table :data="elementPropertyList" size="small" max-height="240" border fit>
      <el-table-column label="序号" width="50px" type="index" />
      <el-table-column label="属性名" prop="name" min-width="100px" show-overflow-tooltip />
      <el-table-column label="属性值" prop="value" min-width="100px" show-overflow-tooltip />
      <el-table-column label="操作" width="90px">
        <template v-slot="{ row, $index }">
          <el-button link type="text" @click="openAttributesForm(row, $index)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button link type="text" style="color: #ff4d4f" @click="remove(row, $index)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="element-drawer__button">
      <el-button size="small" type="primary" :icon="Plus" @click="openAttributesForm(null, -1)">添加属性</el-button>
    </div>

    <el-dialog v-model="propertyFormModelVisible" title="属性配置" width="600px" append-to-body destroy-on-close>
      <el-form :model="propertyForm" label-width="80px" size="small" ref="attributeFormRef" @submit.prevent>
        <el-form-item label="属性名：" prop="name">
          <el-input v-model="propertyForm.name" clearable />
        </el-form-item>
        <el-form-item label="属性值：" prop="value">
          <el-input v-model="propertyForm.value" clearable />
        </el-form-item>
      </el-form>
      <template v-slot:footer>
        <el-button size="small" @click="propertyFormModelVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="submit">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue'
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

export default {
  name: "ElementProperties",
  setup() {
    return {
      Plus
    }
  },
  props: {
    id: String,
    type: String
  },
  inject: {
    prefix: "prefix",
    width: "width"
  },
  data() {
    return {
      elementPropertyList: [],
      propertyForm: {},
      editingPropertyIndex: -1,
      propertyFormModelVisible: false
    };
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        val && val.length && this.resetAttributesList();
      }
    }
  },
  methods: {
    resetAttributesList() {
      this.bpmnElement = window.bpmnInstances.bpmnElement;
      this.otherExtensionList = []; // 其他扩展配置
      const extensionElements = this.bpmnElement?.businessObject?.extensionElements || this.bpmnElement?.businessObject?.processRef?.extensionElements || {};
      this.bpmnElementProperties =
      extensionElements?.values?.filter(ex => {
          if (ex.$type !== `${this.prefix}:Properties`) {
            this.otherExtensionList.push(ex);
          }
          return ex.$type === `${this.prefix}:Properties`;
        }) ?? [];
      // 保存所有的 扩展属性字段
      this.bpmnElementPropertyList = this.bpmnElementProperties.reduce((pre, current) => pre.concat(current.values), []);
      // 复制 显示
      this.elementPropertyList = JSON.parse(JSON.stringify(this.bpmnElementPropertyList ?? []));
    },
    openAttributesForm(attr, index) {
      this.editingPropertyIndex = index;
      this.propertyForm = index === -1 ? {} : JSON.parse(JSON.stringify(attr));
      this.propertyFormModelVisible = true;
      this.$nextTick(() => {
        if (this.$refs["attributeFormRef"]) this.$refs["attributeFormRef"].clearValidate();
      });
    },
    removeAttributes(attr, index) {
      this.$confirm("确认移除该属性吗？", "提示", {
        confirmButtonText: "确 认",
        cancelButtonText: "取 消"
      })
        .then(() => {
          this.elementPropertyList.splice(index, 1);
          this.bpmnElementPropertyList.splice(index, 1);
          // 新建一个属性字段的保存列表
          const propertiesObject = window.bpmnInstances.moddle.create(`${this.prefix}:Properties`, {
            values: this.bpmnElementPropertyList
          });
          this.updateElementExtensions(propertiesObject);
          this.resetAttributesList();
        })
        .catch(() => { /* 操作取消 */ });
    },
    saveAttribute() {
      const { name, value } = this.propertyForm;
      if (this.editingPropertyIndex !== -1) {
        window.bpmnInstances.modeling.updateModdleProperties(this.bpmnElement, this.bpmnElementPropertyList[this.editingPropertyIndex], {
          name,
          value
        });
      } else {
        // 新建属性字段
        const newPropertyObject = window.bpmnInstances.moddle.create(`${this.prefix}:Property`, { name, value });
        // 新建一个属性字段的保存列表
        const propertiesObject = window.bpmnInstances.moddle.create(`${this.prefix}:Properties`, {
          values: this.bpmnElementPropertyList.concat([newPropertyObject])
        });
        this.updateElementExtensions(propertiesObject);
      }
      this.propertyFormModelVisible = false;
      this.resetAttributesList();
    },
    updateElementExtensions(properties) {
      const extensions = window.bpmnInstances.moddle.create("bpmn:ExtensionElements", {
        values: this.otherExtensionList.concat([properties])
      });
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, {
        extensionElements: extensions
      });
    },
    remove(row, index) {
      const __that = this;
      // 获取当前节点的类型和ID
      const rawElement = bpmnInstances.elementRegistry.get(__that.id);
      const nodeType = rawElement?.type;
      // 只判断 Participant 节点
      if (nodeType === 'bpmn:Participant') {
        __that.removeCurrentPoolNode(row, index);
      } else {
        // 默认逻辑
        __that.removeAttributes(row, index);
      }
    },

    submit() {
      const __that = this;
      const bpmnInstances = window.bpmnInstances;
      if (!bpmnInstances || !bpmnInstances.elementRegistry || !__that.id) {
        return;
      }
      // 获取当前节点的类型和ID
      const rawElement = bpmnInstances.elementRegistry.get(__that.id);
      const nodeType = rawElement?.type;
      // 只判断 Participant 节点
      if (nodeType === 'bpmn:Participant') {
        __that.getCurrentPoolNode();
      } else {
        // 默认逻辑
        __that.saveAttribute();
      }
    },
    removeCurrentPoolNode(attr, index) {
      this.$confirm("确认移除该属性吗？", "提示", {
        confirmButtonText: "确 认",
        cancelButtonText: "取 消"
      })
        .then(() => {
          const __that = this;
          __that.elementPropertyList.splice(index, 1);
          __that.bpmnElementPropertyList.splice(index, 1);

          const bpmnInstances = window.bpmnInstances;
          const modeling = bpmnInstances.modeling;
          const moddle = bpmnInstances.moddle;
          const rawElement = bpmnInstances.elementRegistry.get(__that.id);
          const actualProcessElement = __that.resolveProcessElement(rawElement);
          if (__that.bpmnElementPropertyList?.length !== 0) {
            const businessObject = getBusinessObject(actualProcessElement);
            const extensionElements = businessObject?.extensionElements;
            const propertiesElements = moddle.create(`${__that.prefix}:Properties`, {
              values: __that.bpmnElementPropertyList
            });
            extensionElements.values = [propertiesElements]
            modeling.updateModdleProperties(actualProcessElement, actualProcessElement, {
              extensionElements: extensionElements
            });
          } else {
            modeling.updateModdleProperties(actualProcessElement, actualProcessElement, {
              extensionElements: null
            });
          }
          // 默认逻辑 关闭弹框|重置数据
          __that.propertyFormModelVisible = false;
          __that.resetAttributesList();
        })
    },
    /**创建池节点的一些操作 */
    resolveProcessElement(currentElement) {
      const bpmnInstances = window.bpmnInstances;
      if (!bpmnInstances) return null;

      if (currentElement.businessObject && currentElement.businessObject.$type === 'bpmn:Participant') {
        const processRef = currentElement.businessObject.processRef;
        if (!processRef) {
          return null;
        }
        const processId = processRef.id;
        // 先尝试 elementRegistry
        let processElement = bpmnInstances.elementRegistry.get(processId);
        // 如果 elementRegistry 没有，再从 rootElements 查找
        if (!processElement && bpmnInstances.modeler) {
          const rootElements = bpmnInstances.modeler.getDefinitions().rootElements || [];
          processElement = rootElements.find(el => el.id === processId && el.$type === 'bpmn:Process');
        }
        if (!processElement) {
          return null;
        }
        return processElement;
      }
      return currentElement;
    },
    getCurrentPoolNode() {
      const __that = this;
      const modeling = bpmnInstances.modeling;
      const moddle = bpmnInstances.moddle;
      const bpmnFactory = bpmnInstances.bpmnFactory;

      const rawElement = bpmnInstances.elementRegistry.get(__that.id);
      const actualProcessElement = __that.resolveProcessElement(rawElement);
      const businessObject = getBusinessObject(actualProcessElement);
      let extensionElements = businessObject?.extensionElements;

      // 编辑
      if (__that.editingPropertyIndex !== -1) {
        modeling.updateModdleProperties(actualProcessElement, __that.bpmnElementPropertyList[__that.editingPropertyIndex], { ...(__that.propertyForm || {}) });
        __that.propertyFormModelVisible = false;
        __that.resetAttributesList();
        return;
      }

      // 新建
      const newPropertyObject = moddle.create(`${__that.prefix}:Property`, { ...(__that.propertyForm || {}) });
      const propertiesElements = moddle.create(`${__that.prefix}:Properties`, {
        // values: [...values.flat(Infinity),  newPropertyObject]
        values: __that.bpmnElementPropertyList.concat([newPropertyObject])
      });

      if (!extensionElements) {
        extensionElements = bpmnFactory.create('bpmn:ExtensionElements');
      }
      extensionElements.values = [propertiesElements]
      modeling.updateModdleProperties(actualProcessElement, actualProcessElement, {
        // values 最作为属性
        // values: __that.otherExtensionList.concat([propertiesElements]),
        // 最作为扩展元素
        extensionElements: extensionElements
      });
      // setTimeout(() => {
        // 默认逻辑 关闭弹框|重置数据
        __that.propertyFormModelVisible = false;
        __that.resetAttributesList();
      // }, 200);
    },
    /**创建池节点的一些操作 */
  }
};
</script>
