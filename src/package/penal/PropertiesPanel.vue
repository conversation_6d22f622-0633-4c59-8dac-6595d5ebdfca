<template>
	<div
		class="process-panel__container"
		:style="{ width: `${this.width}px` }"
	>
		<!-- Add header section -->
		<div class="panel-header">
			<h3 class="panel-header__title">{{ elementTypeName }}</h3>
		</div>

		<el-collapse v-model="activeTab">
			<el-collapse-item name="base">
				<template #title>
					<div class="panel-tab__title">
						<el-icon><info-filled /></el-icon>常规
					</div>
				</template>
				<element-base-info
					:id-edit-disabled="idEditDisabled"
					:scene="scene"
					:business-object="elementBusinessObject"
					:type="elementType"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="condition"
				v-if="elementType === 'Process' || elementType === 'Collaboration'"
				key="message"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><comment /></el-icon>全局事件
					</div>
				</template>
				<signal-and-massage :scene="scene" @message-list-updated="updateMessageOptions" @signal-list-updated="updateSignalOptions" @escalation-list-updated="updateEscalationOptions" @error-list-updated="updateErrorOptions" />
			</el-collapse-item>
			<el-collapse-item
				name="condition"
				v-if="conditionFormVisible"
				key="condition"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>流转条件
					</div>
				</template>
				<flow-condition
					:business-object="elementBusinessObject"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="message"
				key="message"
				v-if="isMessageEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>消息事件
					</div>
				</template>
				<MessageSelect v-model="selectedMessageId" :options="messageOptions" />
			</el-collapse-item>

			<el-collapse-item
				name="signal"
				key="signal"
				v-if="isSignalEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>信号事件
					</div>
				</template>
				<SignalSelect v-model="selectedSignalId" :options="signalOptions" />
			</el-collapse-item>

			<el-collapse-item
				name="escalation"
				key="escalation"
				v-if="isEscalationEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>升级事件
					</div>
				</template>
				<EscalationSelect v-model="selectedEscalationId" :options="escalationOptions" />
			</el-collapse-item>

			<el-collapse-item
				name="error"
				key="error"
				v-if="isErrorEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>错误事件
					</div>
				</template>
				<ErrorSelect v-model="selectedErrorId" :options="errorOptions" />
			</el-collapse-item>

			<el-collapse-item
				name="timer"
				key="timer"
				v-if="isTimerEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><Clock /></el-icon>时间事件
					</div>
				</template>
				<timer-event
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>

			<el-collapse-item
				name="conditional"
				key="conditional"
				v-if="isConditionalEvent"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><SetUp /></el-icon>条件事件
					</div>
				</template>
				<condition-event
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>

			<el-collapse-item
				name="task"
				v-if="elementType.indexOf('Task') !== -1"
				key="task"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><checked /></el-icon>任务
					</div>
				</template>
				<element-task
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>


			<el-collapse-item
				name="multiInstance"
				v-if="elementType.indexOf('Task') !== -1 || elementType === 'CallActivity' || elementType === 'Transaction' || elementType === 'SubProcess'"
				key="multiInstance"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><help-filled /></el-icon>多实例
					</div>
				</template>
				<element-multi-instance
					:business-object="elementBusinessObject"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>

			<el-collapse-item
				name="task"
				v-if="elementType.indexOf('CallActivity') !== -1 && !isFlow"
				key="CallActivity"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><checked /></el-icon>调用任务
					</div>
				</template>
				<call-activity-panel
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
				<!-- <element-task :id="elementId" :type="elementType" /> -->
			</el-collapse-item>

			<!-- 接入审批流 -->
			<el-collapse-item
				name="task"
				v-if="elementType.indexOf('CallActivity') !== -1 && isFlow"
				key="CallActivity"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><checked /></el-icon>调用任务
					</div>
				</template>
				<call-activity-flow-panel
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>

			<el-collapse-item
				name="condition"
				v-if="formVisible"
				key="form"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><list /></el-icon>表单
					</div>
				</template>
				<element-form
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>

			<el-collapse-item
				name="taskListeners"
				v-if="elementType === 'UserTask'"
				key="taskListeners"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><bell-filled /></el-icon>任务监听器
					</div>
				</template>
				<user-task-listeners
					:id="elementId"
					:type="elementType"
					:scene="scene"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="listeners"
				v-if="elementType === 'Process' || elementType.indexOf('Task') !== -1 || elementType === 'SequenceFlow' || elementType === 'Participant' || elementType === 'StartEvent' || elementType === 'EndEvent' || elementType === 'IntermediateThrowEvent' || elementType === 'IntermediateCatchEvent'"
				key="listeners"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><bell-filled /></el-icon>执行监听器
					</div>
				</template>
				<element-listeners
					:id="elementId"
					:type="elementType"
					:scene="scene"
					:resolved-element="resolvedBpmnElement"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="processListeners"
				v-if="elementType === 'Process' || elementType === 'Participant'"
				key="processListeners"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><bell-filled /></el-icon>事件监听器
					</div>
				</template>
				<process-event-listeners
					:id="elementId"
					:type="elementType"
					:scene="scene"
					:resolved-element="resolvedBpmnElement"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="dataObjects"
				v-if="elementType === 'Process' || elementType === 'Transaction' || elementType === 'SubProcess' || elementType === 'Participant'"
				key="dataObjects"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><document /></el-icon>数据对象
					</div>
				</template>
				<element-data-objects :id="elementId" :scene="scene" />
			</el-collapse-item>
			<el-collapse-item
				name="extensions"
				key="extensions"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><circle-plus /></el-icon>扩展属性
					</div>
				</template>
				<element-properties
					:id="elementId"
					:type="elementType"
					:scene="scene"
					:resolved-element="resolvedBpmnElement"
				/>
			</el-collapse-item>
			<el-collapse-item
				name="other"
				key="other"
			>
				<template #title>
					<div class="panel-tab__title">
						<el-icon><promotion /></el-icon>文档描述
					</div>
				</template>
				<element-other-config :id="elementId" :scene="scene" />
			</el-collapse-item>
		</el-collapse>
	</div>
</template>
<script>
import ElementBaseInfo from './base/ElementBaseInfo.vue';
import ElementOtherConfig from './other/ElementOtherConfig.vue';
import ElementTask from './task/ElementTask.vue';
import ElementMultiInstance from './multi-instance/ElementMultiInstance.vue';
import FlowCondition from './flow-condition/FlowCondition.vue';
import SignalAndMassage from './signal-message/SignalAndMessage.vue';
import ElementListeners from './listeners/ElementListeners.vue';
import ElementProperties from './properties/ElementProperties.vue';
import ElementForm from './form/ElementForm.vue';
import UserTaskListeners from './listeners/UserTaskListeners.vue';
import MessageSelect from './message-select/MessageSelect.vue';
import Log from '../Log';
import CallActivityPanel from './call-activity/CallActivityPanel.vue';
import CallActivityFlowPanel from './call-activity-flow/CallActivityFlowPanel.vue';
import ElementDataObjects from './data-objects/ElementDataObjects.vue';
import ProcessEventListeners from './listeners/ProcessEventListeners.vue';
import SignalSelect from './signal-select/SignalSelect.vue';
import EscalationSelect from './escalation-select/EscalationSelect.vue';
import ErrorSelect from './error-select/ErrorSelect.vue';
import TimerEvent from './timer-event/TimerEvent.vue';
import ConditionEvent from './condition-event/ConditionEvent.vue';
import { InfoFilled, Comment, Promotion, Checked, HelpFilled, List, BellFilled, CirclePlus, Document, Clock, SetUp } from '@element-plus/icons-vue';
/**
 * 侧边栏
 * <AUTHOR>
 * @Home https://github.com/miyuesc
 * @Date 2021年3月31日18:57:51
 */
export default {
	name: 'MyPropertiesPanel',
	components: {
		UserTaskListeners,
		ElementForm,
		ElementProperties,
		ElementListeners,
		SignalAndMassage,
		FlowCondition,
		ElementMultiInstance,
		ElementTask,
		ElementOtherConfig,
		ElementBaseInfo,
		CallActivityPanel,
		ElementDataObjects,
		ProcessEventListeners,
		InfoFilled, Comment, Promotion, Checked, HelpFilled, List, BellFilled, CirclePlus, Document, Clock, SetUp,
		MessageSelect,
		SignalSelect,
		EscalationSelect,
		ErrorSelect,
		TimerEvent,
		ConditionEvent,
		CallActivityFlowPanel,
	},
	componentName: 'MyPropertiesPanel',
	props: {
		bpmnModeler: Object,
		prefix: {
			type: String,
			default: 'camunda',
		},
		width: {
			type: Number,
			default: 456,
		},
		idEditDisabled: {
			type: Boolean,
			default: false,
		},
		scene: {
			type: String,
			default: null,
		},
	},
	provide() {
		return {
			prefix: this.prefix,
			width: this.width,
		};
	},
	data() {
		return {
			activeTab: 'base',
			elementId: '',
			elementType: '',
			elementTypeName: '', // 存储元素类型的中文名称
			elementBusinessObject: {}, // 元素 businessObject 镜像，提供给需要做判断的组件使用
			conditionFormVisible: false, // 流转条件设置
			formVisible: false, // 表单配置
			isMessageEvent: false, // 是否为消息事件节点
			messageOptions: [], // 全局消息事件选项
			selectedMessageId: '', // 当前选中的消息ID
			resolvedBpmnElement: null, // New: Stores the resolved BPMN element (e.g., Process for Participant)
			isSignalEvent: false, // New: 是否为信号事件节点
			signalOptions: [], // New: 全局信号事件选项
			selectedSignalId: '', // New: 当前选中的信号ID
			isEscalationEvent: false, // New: 是否为升级事件节点
			escalationOptions: [], // New: 全局升级事件选项
			selectedEscalationId: '', // New: 当前选中的升级ID
			isErrorEvent: false, // New: 是否为错误事件节点
			errorOptions: [], // New: 全局错误事件选项
			selectedErrorId: '', // New: 当前选中的错误ID
			isTimerEvent: false, // New: 是否为时间事件节点
			isConditionalEvent: false, // New: 是否为条件事件节点
			isFlow: false, // New: 是否为审批流
		};
	},
	watch: {
		selectedMessageId(newVal) {
			this.updateNodeMessageRef(newVal);
		},
		selectedSignalId(newVal) { // New: Watcher for signal selection
			this.updateNodeSignalRef(newVal);
		},
		selectedEscalationId(newVal) { // New: Watcher for escalation selection
			this.updateNodeEscalationRef(newVal);
		},
		selectedErrorId(newVal) { // New: Watcher for error selection
			this.updateNodeErrorRef(newVal);
		},
		elementId: {
			handler() {
				this.activeTab = 'base';
			},
		},
		elementType: {
			handler() {
				this.checkIsMessageEvent();
				this.checkIsSignalEvent(); // New: Check for signal event type
				this.checkIsEscalationEvent(); // New: Check for escalation event type
				this.checkIsErrorEvent(); // New: Check for error event type
				this.checkIsTimerEvent(); // New: Check for timer event type
				this.checkIsConditionalEvent(); // New: Check for conditional event type
			},
		},
	},
	created() {
		this.initModels();
		this.checkIsMessageEvent();
		this.checkIsSignalEvent(); // New: Initial check for signal event type
		this.checkIsEscalationEvent(); // New: Initial check for escalation event type
		this.checkIsErrorEvent(); // New: Initial check for error event type
		this.checkIsTimerEvent(); // New: Initial check for timer event type
		this.checkIsConditionalEvent(); // New: Initial check for conditional event type
		this.updateMessageOptions();
		this.updateSignalOptions(); // New: Initial update for signal options
		this.updateEscalationOptions(); // New: Initial update for escalation options
		this.updateErrorOptions(); // New: Initial update for error options
	},
	methods: {
		judgeFlowableType() {
			// 自定义的一个审批流程节点
			const _attrs_ = this.bpmnElement?.businessObject?.['$attrs'] || {};
			this.isFlow = _attrs_?.['flowable:type'] === 'flow' ? true : false;
		},
		initModels() {
			if (!this.bpmnModeler) {
				this.timer = setTimeout(() => this.initModels(), 10);
				return;
			}
			if (this.timer) clearTimeout(this.timer);

			// 8.x 版本正确的扩展包注册方式
			const moddle = this.bpmnModeler.get('moddle');

			window.bpmnInstances = {
				modeler: this.bpmnModeler,
				modeling: this.bpmnModeler.get('modeling'),
				moddle: moddle,
				eventBus: this.bpmnModeler.get('eventBus'),
				bpmnFactory: this.bpmnModeler.get('bpmnFactory'),
				elementFactory: this.bpmnModeler.get('elementFactory'),
				elementRegistry: this.bpmnModeler.get('elementRegistry'),
				replace: this.bpmnModeler.get('replace'),
				selection: this.bpmnModeler.get('selection'),
			};

			this.getActiveElement();
			this.updateMessageOptions();
			this.updateSignalOptions(); // New: Update signal options on model init
			this.updateEscalationOptions(); // New: Update escalation options on model init
			this.updateErrorOptions(); // New: Update error options on model init
		},
		getActiveElement() {
			// 初始第一个选中元素 bpmn:Process
			this.initFormOnChanged(null);
			this.bpmnModeler.on('import.done', () => {
				this.initFormOnChanged(null);
			});
			// 监听选择事件，修改当前激活的元素以及表单
			this.bpmnModeler.on('selection.changed', ({ newSelection }) => {
				this.initFormOnChanged(newSelection[0] || null);
			});
			this.bpmnModeler.on('element.changed', ({ element }) => {
				// 保证 修改 "默认流转路径" 类似需要修改多个元素的事件发生的时候，更新表单的元素与原选中元素不一致。
				if (element && element.id === this.elementId) {
					this.initFormOnChanged(element);
				}
			});
		},
		// 获取元素类型的中文名称
		getElementTypeName(type) {
			const typeMap = {
				'Process': '流程',
				'StartEvent': '开始事件',
				'EndEvent': '结束事件',
        'SendTask': '发送任务',
        'ManualTask': '人工任务',
        'ReceiveTask': '接收任务',
				'UserTask': '用户任务',
				'ServiceTask': '服务任务',
				'ScriptTask': '脚本任务',
				'CallActivity': '调用活动',
				'ExclusiveGateway': '互斥网关',
				'ParallelGateway': '并行网关',
				'InclusiveGateway': '包容网关',
				'SequenceFlow': '连线',
				'Transaction':'转运',
				'Participant': '参与者',
				'SubProcess': '子流程',
				'IntermediateThrowEvent': '中间抛出事件',
				'IntermediateCatchEvent': '消息中间捕获事件',
				'BoundaryEvent': '边界事件',
				'DataObjectReference': '数据对象',
				'DataStoreReference': '数据存储',
				'Group': '分组',
				'TextAnnotation': '文本注释'
			};
			return typeMap[type] || type;
		},
		// 初始化数据
		initFormOnChanged(element) {
			let activatedElement = element;
			if (!activatedElement) {
				activatedElement =
					window.bpmnInstances.elementRegistry.find((el) => el.type === 'bpmn:Process') ??
					window.bpmnInstances.elementRegistry.find((el) => el.type === 'bpmn:Collaboration');
			}
			if (!activatedElement || !activatedElement.businessObject) return;

			// If the activated element is a Participant, resolve to its referenced Process
			if (activatedElement.businessObject.$type === 'bpmn:Participant') {
				const processRefId = activatedElement.businessObject.processRef?.id;
				if (processRefId) {
					const referencedProcess = window.bpmnInstances.elementRegistry.get(processRefId);
					if (referencedProcess) {
						activatedElement = referencedProcess; // Use the referenced Process element
					}
				}
			}

			Log.printBack(`select element changed: id: ${activatedElement.id} , type: ${activatedElement.businessObject.$type}`);
			Log.prettyInfo('businessObject', activatedElement.businessObject);

			window.bpmnInstances.bpmnElement = activatedElement;
			this.bpmnElement = activatedElement;
			this.elementId = activatedElement.id;
			this.elementType = activatedElement.type.split(':')[1] || '';
			this.elementTypeName = this.getElementTypeName(this.elementType);
			this.elementBusinessObject = JSON.parse(JSON.stringify(activatedElement.businessObject));
			this.resolvedBpmnElement = activatedElement; // New: Store the resolved element

			// Explicitly re-evaluate message and signal event flags to ensure they are up-to-date
			this.checkIsMessageEvent();
			this.checkIsSignalEvent();
			this.checkIsEscalationEvent(); // New: Explicitly re-evaluate escalation event flag
			this.checkIsErrorEvent(); // New: Explicitly re-evaluate error event flag
			this.checkIsTimerEvent(); // New: Explicitly re-evaluate timer event flag
			this.checkIsConditionalEvent(); // New: Explicitly re-evaluate conditional event flag
			this.judgeFlowableType();

			this.conditionFormVisible = !!(
				this.elementType === 'SequenceFlow' &&
				activatedElement.source &&
				activatedElement.source.type.indexOf('StartEvent') === -1
			);
			this.formVisible = this.elementType === 'UserTask' || this.elementType === 'StartEvent';
			// 添加调用活动类型判断
			if (this.elementType === 'CallActivity') {
				const calledElement = activatedElement.businessObject.get('calledElement');
				if (calledElement) {
					this.calledElement = calledElement;
				}
			}
			// 回显消息事件的messageRef
			if (this.isMessageEvent) {
				const bo = activatedElement.businessObject;
				const eventDef = bo.eventDefinitions && bo.eventDefinitions[0];
				this.selectedMessageId = eventDef && eventDef.messageRef ? eventDef.messageRef.id : '';
			}
			// 回显信号事件的signalRef (New)
			if (this.isSignalEvent) {
				const bo = activatedElement.businessObject;
				const eventDef = bo.eventDefinitions && bo.eventDefinitions[0];
				this.selectedSignalId = eventDef && eventDef.signalRef ? eventDef.signalRef.id : '';
			}
			// 回显升级事件的escalationRef (New)
			if (this.isEscalationEvent) {
				const bo = activatedElement.businessObject;
				const eventDef = bo.eventDefinitions && bo.eventDefinitions[0];
				this.selectedEscalationId = eventDef && eventDef.escalationRef ? eventDef.escalationRef.id : '';
			}
			// 回显错误事件的errorRef (New)
			if (this.isErrorEvent) {
				const bo = activatedElement.businessObject;
				const eventDef = bo.eventDefinitions && bo.eventDefinitions[0];
				this.selectedErrorId = eventDef && eventDef.errorRef ? eventDef.errorRef.id : '';
			}
		},
		beforeUnmount() {
			window.bpmnInstances = null;
		},
		checkIsMessageEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};

			// 定义支持消息事件的节点类型
			const MESSAGE_EVENT_TYPES = [
				'StartEvent',
				'EndEvent',
				'IntermediateCatchEvent',
				'IntermediateThrowEvent',
				'BoundaryEvent',
				'MessageStartEvent',
				'MessageEndEvent'
			];

			// 定义支持消息的任务类型
			const MESSAGE_TASK_TYPES = ['ReceiveTask'];

			// 检查是否为消息任务类型
			const isMessageTask = MESSAGE_TASK_TYPES.includes(type);

			// 检查是否为消息事件类型且包含消息定义
			const isMessageEventType = MESSAGE_EVENT_TYPES.includes(type);
			const hasMessageDefinition = this.hasMessageEventDefinition(bo);
			const isMessageEvent = isMessageEventType && hasMessageDefinition;

			this.isMessageEvent = isMessageTask || isMessageEvent;
		},

		// 辅助方法：检查是否包含消息事件定义
		hasMessageEventDefinition(businessObject) {
			// 检查 eventDefinitionType 属性
			if (businessObject.eventDefinitionType === 'message') {
				return true;
			}

			// 检查 eventDefinitions 数组
			const eventDefinitions = businessObject.eventDefinitions;
			if (!Array.isArray(eventDefinitions) || eventDefinitions.length === 0) {
				return false;
			}

			const firstEventDef = eventDefinitions[0];
			return firstEventDef?.$type?.includes('MessageEventDefinition') || false;
		},
		updateMessageOptions() {
			// 从全局rootElements中提取bpmn:Message
			try {
				const rootElements = window.bpmnInstances?.modeler?.getDefinitions()?.rootElements || [];
				this.messageOptions = rootElements
					.filter(el => el.$type === 'bpmn:Message')
					.map(el => ({ label: el.name || el.id, value: el.id }));
			} catch (e) {
				this.messageOptions = [];
			}
		},
		updateNodeMessageRef(messageId) {
			if (!this.isMessageEvent) return;
			const modeling = window.bpmnInstances.modeling;
			const elementRegistry = window.bpmnInstances.elementRegistry;
			const element = elementRegistry.get(this.elementId);
			if (!element) return;
			const bo = element.businessObject;
			if (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type === 'bpmn:MessageEventDefinition') {
				modeling.updateModdleProperties(element, bo.eventDefinitions[0], {
					messageRef: messageId ? { id: messageId } : null
				});
			}
		},
		// New: Check for signal event type
		checkIsSignalEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};

			// Debugging: Log values used in checkIsSignalEvent
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsSignalEvent - type:', type);
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsSignalEvent - bo.eventDefinitionType:', bo.eventDefinitionType);
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsSignalEvent - bo.eventDefinitions:', bo.eventDefinitions);

			this.isSignalEvent =
				(type === 'StartEvent' || type === 'EndEvent' || type === 'IntermediateThrowEvent' || type === 'IntermediateCatchEvent' || type === 'BoundaryEvent') &&
				(bo.eventDefinitionType === 'signal' || (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type && bo.eventDefinitions[0].$type.indexOf('SignalEventDefinition') !== -1));
		},
		// New: Update signal options from global root elements
		updateSignalOptions() {
			try {
				const rootElements = window.bpmnInstances?.modeler?.getDefinitions()?.rootElements || [];
				this.signalOptions = rootElements
					.filter(el => el.$type === 'bpmn:Signal')
					.map(el => ({ label: el.name || el.id, value: el.id }));
			} catch (e) {
				this.signalOptions = [];
			}
		},
		// New: Update the signalRef of the current node
		updateNodeSignalRef(signalId) {
			if (!this.isSignalEvent) return;
			const modeling = window.bpmnInstances.modeling;
			const elementRegistry = window.bpmnInstances.elementRegistry;
			const element = elementRegistry.get(this.elementId);
			if (!element) return;
			const bo = element.businessObject;
			if (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type === 'bpmn:SignalEventDefinition') {
				modeling.updateModdleProperties(element, bo.eventDefinitions[0], {
					signalRef: signalId ? { id: signalId } : null
				});
			}
		},
		// New: Check for escalation event type
		checkIsEscalationEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};

			// Debugging: Log values used in checkIsEscalationEvent
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsEscalationEvent - type:', type);
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsEscalationEvent - bo.eventDefinitionType:', bo.eventDefinitionType);
			// eslint-disable-next-line no-console
			console.log('Debug: checkIsEscalationEvent - bo.eventDefinitions:', bo.eventDefinitions);

			this.isEscalationEvent =
				(type === 'StartEvent' || type === 'EndEvent' || type === 'IntermediateThrowEvent' || type === 'IntermediateCatchEvent' || type === 'BoundaryEvent') &&
				(bo.eventDefinitionType === 'escalation' || (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type && bo.eventDefinitions[0].$type.indexOf('EscalationEventDefinition') !== -1));
		},
		// New: Update escalation options from global root elements
		updateEscalationOptions() {
			try {
				const rootElements = window.bpmnInstances?.modeler?.getDefinitions()?.rootElements || [];
				this.escalationOptions = rootElements
					.filter(el => el.$type === 'bpmn:Escalation')
					.map(el => ({ label: el.name || el.id, value: el.id }));
			} catch (e) {
				this.escalationOptions = [];
			}
		},
		// New: Update the escalationRef of the current node
		updateNodeEscalationRef(escalationId) {
			if (!this.isEscalationEvent) return;
			const modeling = window.bpmnInstances.modeling;
			const elementRegistry = window.bpmnInstances.elementRegistry;
			const element = elementRegistry.get(this.elementId);
			if (!element) return;
			const bo = element.businessObject;
			if (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type === 'bpmn:EscalationEventDefinition') {
				modeling.updateModdleProperties(element, bo.eventDefinitions[0], {
					escalationRef: escalationId ? { id: escalationId } : null
				});
			}
		},
		// New: Check for error event type
		checkIsErrorEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};
			this.isErrorEvent =
				(type === 'StartEvent' || type === 'EndEvent' || type === 'IntermediateThrowEvent' || type === 'IntermediateCatchEvent' || type === 'BoundaryEvent') &&
				(bo.eventDefinitionType === 'error' || (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type && bo.eventDefinitions[0].$type.indexOf('ErrorEventDefinition') !== -1));
		},
		// New: Update error options from global root elements
		updateErrorOptions() {
			try {
				const rootElements = window.bpmnInstances?.modeler?.getDefinitions()?.rootElements || [];
				this.errorOptions = rootElements
					.filter(el => el.$type === 'bpmn:Error')
					.map(el => ({ label: el.name || el.id, value: el.id }));
			} catch (e) {
				this.errorOptions = [];
			}
		},
		// New: Update the errorRef of the current node
		updateNodeErrorRef(errorId) {
			if (!this.isErrorEvent) return;
			const modeling = window.bpmnInstances.modeling;
			const elementRegistry = window.bpmnInstances.elementRegistry;
			const element = elementRegistry.get(this.elementId);
			if (!element) return;
			const bo = element.businessObject;
			if (bo.eventDefinitions && bo.eventDefinitions[0] && bo.eventDefinitions[0].$type === 'bpmn:ErrorEventDefinition') {
				modeling.updateModdleProperties(element, bo.eventDefinitions[0], {
					errorRef: errorId ? { id: errorId } : null
				});
			}
		},
		// New: Check for timer event type
		checkIsTimerEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};

			// 定义支持时间事件的节点类型
			const TIMER_EVENT_TYPES = [
				'StartEvent',
				'EndEvent',
				'IntermediateCatchEvent',
				'IntermediateThrowEvent',
				'BoundaryEvent'
			];

			// 检查是否为时间事件类型且包含时间定义
			const isTimerEventType = TIMER_EVENT_TYPES.includes(type);
			const hasTimerDefinition = this.hasTimerEventDefinition(bo);

			this.isTimerEvent = isTimerEventType && hasTimerDefinition;
		},
		// New: 辅助方法：检查是否包含时间事件定义
		hasTimerEventDefinition(businessObject) {
			// 检查 eventDefinitionType 属性
			if (businessObject.eventDefinitionType === 'timer') {
				return true;
			}

			// 检查 eventDefinitions 数组
			const eventDefinitions = businessObject.eventDefinitions;
			if (!Array.isArray(eventDefinitions) || eventDefinitions.length === 0) {
				return false;
			}

			const firstEventDef = eventDefinitions[0];
			return firstEventDef?.$type?.includes('TimerEventDefinition') || false;
		},
		// New: Check for conditional event type
		checkIsConditionalEvent() {
			const type = this.elementType;
			const bo = this.elementBusinessObject || {};

			// 定义支持条件事件的节点类型
			const CONDITIONAL_EVENT_TYPES = [
				'StartEvent',
				'EndEvent',
				'IntermediateCatchEvent',
				'IntermediateThrowEvent',
				'BoundaryEvent'
			];

			// 检查是否为条件事件类型且包含条件定义
			const isConditionalEventType = CONDITIONAL_EVENT_TYPES.includes(type);
			const hasConditionalDefinition = this.hasConditionalEventDefinition(bo);

			this.isConditionalEvent = isConditionalEventType && hasConditionalDefinition;
		},
		// New: 辅助方法：检查是否包含条件事件定义
		hasConditionalEventDefinition(businessObject) {
			// 检查 eventDefinitionType 属性
			if (businessObject.eventDefinitionType === 'conditional') {
				return true;
			}

			// 检查 eventDefinitions 数组
			const eventDefinitions = businessObject.eventDefinitions;
			if (!Array.isArray(eventDefinitions) || eventDefinitions.length === 0) {
				return false;
			}

			const firstEventDef = eventDefinitions[0];
			return firstEventDef?.$type?.includes('ConditionalEventDefinition') || false;
		},
	},
};
</script>

<style lang="scss" scoped>
.panel-header {
	padding: 16px;
	border-bottom: 1px solid var(--el-border-color-light);
	background-color: var(--el-bg-color);

	&__title {
		margin: 0;
		font-size: 16px;
		font-weight: 600;
		color: var(--el-text-color-primary);
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}
}

.process-panel__container {
	height: 100%;
	overflow-y: auto;
	background-color: var(--el-bg-color);
	border-left: 1px solid var(--el-border-color-light);
}
</style>
