<template>
  <div class="panel-tab__content">
    <div class="panel-tab__content--title">
      <span><el-icon style="margin-right: 8px; color: #555555"><Menu /></el-icon>消息列表</span>
      <el-button size="small" v-if="!isFormDisabled" type="primary" :icon="Plus" @click="openModel('message')">创建新消息</el-button>
    </div>
    <el-table :data="messageList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="消息ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="消息名称" prop="name" max-width="300px" show-overflow-tooltip />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button type="text" @click="editObject('message', scope.row)" v-if="!isFormDisabled">编辑</el-button>
          <el-button type="text" style="color: #ff4d4f" @click="deleteObject('message', scope.row)" v-if="!isFormDisabled">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="panel-tab__content--title" style="padding-top: 8px; margin-top: 8px; border-top: 1px solid #eeeeee">
      <span><el-icon style="margin-right: 8px; color: #555555"><Menu /></el-icon>信号列表</span>
      <el-button size="small" v-if="!isFormDisabled" type="primary" :icon="Plus" @click="openModel('signal')">创建新信号</el-button>
    </div>
    <el-table :data="signalList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="信号ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="信号名称" prop="name" max-width="300px" show-overflow-tooltip />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button type="text" @click="editObject('signal', scope.row)" v-if="!isFormDisabled">编辑</el-button>
          <el-button  type="text" style="color: #ff4d4f" @click="deleteObject('signal', scope.row)" v-if="!isFormDisabled">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="panel-tab__content--title" style="padding-top: 8px; margin-top: 8px; border-top: 1px solid #eeeeee">
      <span><el-icon style="margin-right: 8px; color: #555555"><Menu /></el-icon>错误列表</span>
      <el-button size="small" v-if="!isFormDisabled" type="primary" :icon="Plus" @click="openModel('error')">创建新错误</el-button>
    </div>
    <el-table :data="errorList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="错误ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="错误名称" prop="name" max-width="300px" show-overflow-tooltip />
      <el-table-column label="错误编码" prop="errorCode" max-width="300px" show-overflow-tooltip />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button type="text" @click="editObject('error', scope.row)" v-if="!isFormDisabled">编辑</el-button>
          <el-button type="text" style="color: #ff4d4f" @click="deleteObject('error', scope.row)" v-if="!isFormDisabled">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="panel-tab__content--title" style="padding-top: 8px; margin-top: 8px; border-top: 1px solid #eeeeee">
      <span><el-icon style="margin-right: 8px; color: #555555"><Menu /></el-icon>升级列表</span>
      <el-button size="small" v-if="!isFormDisabled" type="primary" :icon="Plus" @click="openModel('escalation')">创建新升级</el-button>
    </div>
    <el-table :data="escalationList" size="small" border>
      <el-table-column type="index" label="序号" width="60px" />
      <el-table-column label="升级ID" prop="id" max-width="300px" show-overflow-tooltip />
      <el-table-column label="升级名称" prop="name" max-width="300px" show-overflow-tooltip />
      <el-table-column label="升级编码" prop="escalationCode" max-width="300px" show-overflow-tooltip />
      <el-table-column label="操作" width="120">
        <template #default="scope">
          <el-button type="text" @click="editObject('escalation', scope.row)" v-if="!isFormDisabled">编辑</el-button>
          <el-button type="text" style="color: #ff4d4f" @click="deleteObject('escalation', scope.row)" v-if="!isFormDisabled">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-drawer
      v-model="modelVisible"
      :title="modelConfig.title"
      size="400px"
      :close-on-click-modal="false"
      direction="rtl"
      destroy-on-close
      append-to-body
    >
      <el-form :model="modelObjectForm" size="small" label-width="90px" @submit.prevent>
        <el-form-item :label="modelConfig.idLabel">
          <el-input v-model="modelObjectForm.id" clearable />
        </el-form-item>
        <el-form-item :label="modelConfig.nameLabel">
          <el-input v-model="modelObjectForm.name" clearable />
        </el-form-item>
        <el-form-item v-if="modelType === 'error'" :label="modelConfig.codeLabel">
          <el-input v-model="modelObjectForm.errorCode" :placeholder="modelConfig.codePlaceholder" clearable />
        </el-form-item>
        <el-form-item v-if="modelType === 'escalation'" :label="modelConfig.codeLabel">
          <el-input v-model="modelObjectForm.escalationCode" :placeholder="modelConfig.codePlaceholder" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button size="small" @click="modelVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="addNewObject">保 存</el-button>
      </template>
    </el-drawer>
  </div>
</template>
<script>
import { Plus } from '@element-plus/icons-vue'
export default {
  name: 'SignalAndMassage',
  setup() {
    return {
      Plus
    }
  },
  data() {
    return {
      signalList: [],
      messageList: [],
      errorList: [],
      escalationList: [],
      modelVisible: false,
      modelType: '',
      modelObjectForm: {},
      isEdit: false,
      editOriginId: ''
    };
  },
  props: {
    scene: {
      type: String,
      default: null
    }
  },
  computed: {
    modelConfig() {
      if (this.modelType === 'message') {
        return { title: '创建消息', idLabel: '消息ID', nameLabel: '消息名称' };
      } else if (this.modelType === 'signal') {
        return { title: '创建信号', idLabel: '信号ID', nameLabel: '信号名称' };
      } else if (this.modelType === 'error') {
        return { title: '创建错误', idLabel: '错误ID', nameLabel: '错误名称', codeLabel: '错误编码', codeProp: 'errorCode', codePlaceholder: '请输入编码' };
      } else {
        return { title: '创建升级', idLabel: '升级ID', nameLabel: '升级名称', codeLabel: '升级编码', codeProp: 'escalationCode', codePlaceholder: '请输入编码' };
      }
    },
    isFormDisabled() {
      return !!(this.scene === 'DETAIL')
    }
  },
  mounted() {
    this.initDataList();
  },
  methods: {
    initDataList() {
      this.rootElements = window.bpmnInstances.modeler.getDefinitions().rootElements;
      this.messageIdMap = {};
      this.signalIdMap = {};
      this.errorIdMap = {};
      this.escalationIdMap = {};
      this.messageList = [];
      this.signalList = [];
      this.errorList = [];
      this.escalationList = [];
      this.rootElements.forEach(el => {
        if (el.$type === 'bpmn:Message') {
          this.messageIdMap[el.id] = true;
          this.messageList.push({ ...el });
        }
        if (el.$type === 'bpmn:Signal') {
          this.signalIdMap[el.id] = true;
          this.signalList.push({ ...el });
        }
        if (el.$type === 'bpmn:Error') {
          this.errorIdMap[el.id] = true;
          this.errorList.push({ ...el });
        }
        if (el.$type === 'bpmn:Escalation') {
          this.escalationIdMap[el.id] = true;
          this.escalationList.push({ ...el });
        }
      });
    },
    openModel(type) {
      this.modelType = type;
      this.modelObjectForm = {};
      this.isEdit = false;
      this.editOriginId = '';
      this.modelVisible = true;
    },
    editObject(type, row) {
      this.modelType = type;
      this.modelObjectForm = { ...row };
      this.isEdit = true;
      this.editOriginId = row.id;
      this.modelVisible = true;
    },
    deleteObject(type, row) {
      this.$confirm('确定要删除吗？', '提示', {
        type: 'warning'
      }).then(() => {
        const rootType = type === 'message' ? 'bpmn:Message' : type === 'signal' ? 'bpmn:Signal' : type === 'error' ? 'bpmn:Error' : 'bpmn:Escalation';
        const idx = this.rootElements.findIndex(el => el.$type === rootType && el.id === row.id);
        if (idx !== -1) this.rootElements.splice(idx, 1);
        this.initDataList();
        if(type === 'message') this.$emit('message-list-updated');
        if(type === 'signal') this.$emit('signal-list-updated');
        if(type === 'error') this.$emit('error-list-updated');
        if(type === 'escalation') this.$emit('escalation-list-updated');
      });
    },
    addNewObject() {
      if (this.modelType === 'message') {
        if (!this.isEdit && this.messageIdMap[this.modelObjectForm.id]) {
          return this.$message.error('该消息已存在，请修改id后重新保存');
        }
        if (this.isEdit) {
          const idx = this.rootElements.findIndex(el => el.$type === 'bpmn:Message' && el.id === this.editOriginId);
          if (idx !== -1) {
            this.rootElements[idx].id = this.modelObjectForm.id;
            this.rootElements[idx].name = this.modelObjectForm.name;
          }
        } else {
          const messageRef = window.bpmnInstances.moddle.create('bpmn:Message', this.modelObjectForm);
          this.rootElements.push(messageRef);
        }
        this.$emit('message-list-updated');
      } else if (this.modelType === 'signal') {
        if (!this.isEdit && this.signalIdMap[this.modelObjectForm.id]) {
          return this.$message.error('该信号已存在，请修改id后重新保存');
        }
        if (this.isEdit) {
          const idx = this.rootElements.findIndex(el => el.$type === 'bpmn:Signal' && el.id === this.editOriginId);
          if (idx !== -1) {
            this.rootElements[idx].id = this.modelObjectForm.id;
            this.rootElements[idx].name = this.modelObjectForm.name;
          }
        } else {
          const signalRef = window.bpmnInstances.moddle.create('bpmn:Signal', this.modelObjectForm);
          this.rootElements.push(signalRef);
        }
        this.$emit('signal-list-updated');
      } else if (this.modelType === 'error') {
        if (!this.isEdit && this.errorIdMap[this.modelObjectForm.id]) {
          return this.$message.error('该错误已存在，请修改id后重新保存');
        }
        if (this.isEdit) {
          const idx = this.rootElements.findIndex(el => el.$type === 'bpmn:Error' && el.id === this.editOriginId);
          if (idx !== -1) {
            this.rootElements[idx].id = this.modelObjectForm.id;
            this.rootElements[idx].name = this.modelObjectForm.name;
            this.rootElements[idx].errorCode = this.modelObjectForm.errorCode;
          }
        } else {
          const errorRef = window.bpmnInstances.moddle.create('bpmn:Error', this.modelObjectForm);
          this.rootElements.push(errorRef);
        }
        this.$emit('error-list-updated');
      } else if (this.modelType === 'escalation') {
        if (!this.isEdit && this.escalationIdMap[this.modelObjectForm.id]) {
          return this.$message.error('该升级已存在，请修改id后重新保存');
        }
        if (this.isEdit) {
          const idx = this.rootElements.findIndex(el => el.$type === 'bpmn:Escalation' && el.id === this.editOriginId);
          if (idx !== -1) {
            this.rootElements[idx].id = this.modelObjectForm.id;
            this.rootElements[idx].name = this.modelObjectForm.name;
            this.rootElements[idx].escalationCode = this.modelObjectForm.escalationCode;
          }
        } else {
          const escalationRef = window.bpmnInstances.moddle.create('bpmn:Escalation', this.modelObjectForm);
          this.rootElements.push(escalationRef);
        }
        this.$emit('escalation-list-updated');
      }
      this.modelVisible = false;
      this.initDataList();
    }
  }
};
</script>
