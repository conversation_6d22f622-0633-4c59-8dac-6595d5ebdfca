<template>
  <div class="panel-tab__content">
    <!-- 事件监听器列表 -->
    <el-table :data="elementListenersList" size="small" border>
      <el-table-column label="序号" width="50px" type="index" />
      <el-table-column label="事件类型" min-width="100px" prop="event" show-overflow-tooltip />
      <el-table-column label="监听器类型" min-width="100px" prop="listenerType" show-overflow-tooltip />
      <el-table-column label="操作" width="150px" v-if="!isFormDisabled">
        <template #default="{ row, $index }">
          <el-button link type="text" size="small" @click="openListenerForm(row, $index)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button link type="text" style="color: #ff4d4f" size="small"  @click="removeListener(row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加监听器按钮 -->
    <div class="element-drawer__button" v-if="!isFormDisabled">
      <el-button size="small" type="primary" :icon="Plus" @click="openListenerForm(null)">添加监听器</el-button>
    </div>

    <!-- 监听器配置抽屉 -->
    <el-drawer
      v-model="listenerFormModelVisible"
      title="事件监听器配置"
      :size="`${width}px`"
      append-to-body
      destroy-on-close
    >
      <el-form size="small" label-width="90px" ref="listenerFormRef" :rules="rules" :model="listenerForm">
        <el-form-item label="事件类型" prop="event">
          <el-select v-model="listenerForm.event">
             <el-option label="PROCESS_COMPLETED" value="PROCESS_COMPLETED"></el-option>
             <el-option label="start" value="start" />
             <el-option label="end" value="end" />
            <!-- 添加其他可能的事件类型 -->
          </el-select>
        </el-form-item>

        <el-form-item label="抛出事件" prop="throwEvent">
          <el-radio-group v-model="listenerForm.throwEvent">
            <el-radio label="是" :value="true"></el-radio>
            <el-radio label="否" :value="false"></el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="监听器类型" prop="listenerType">
          <el-select v-model="listenerForm.listenerType">
            <el-option label="Java 类" value="class"></el-option>
            <el-option label="表达式" value="expression"></el-option>
            <el-option label="代理表达式" value="delegateExpression"></el-option>
            <!-- 添加其他可能的监听器类型 -->
          </el-select>
        </el-form-item>

        <el-form-item
          v-if="listenerForm.listenerType === 'class'"
          label="Java 类"
          prop="class"
        >
          <el-input v-model="listenerForm.class" clearable />
        </el-form-item>

        <el-form-item
          v-if="listenerForm.listenerType === 'expression'"
          label="表达式"
          prop="expression"
        >
          <el-input v-model="listenerForm.expression" clearable />
        </el-form-item>

        <el-form-item
          v-if="listenerForm.listenerType === 'delegateExpression'"
          label="代理表达式"
          prop="delegateExpression"
        >
          <el-input v-model="listenerForm.delegateExpression" clearable />
        </el-form-item>

        <el-form-item label="实体类型" prop="entityType">
          <el-input v-model="listenerForm.entityType" clearable />
        </el-form-item>

      </el-form>

      <template #footer>
         <div class="element-drawer__button">
           <el-button size="small" @click="listenerFormModelVisible = false">取 消</el-button>
           <el-button size="small" type="primary" @click="saveListener">保 存</el-button>
         </div>
       </template>

    </el-drawer>

  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue';
import { getCurrentBpmnElement, getProcessElement } from "../../utils";

export default {
  name: "ProcessEventListeners",
   setup() {
    return { Plus };
  },
  props: {
    id: String,
    scene: String
  },
   inject: {
    prefix: "prefix",
    width: "width"
  },
  data() {
    return {
      elementListenersList: [], // List of process event listeners
      listenerFormModelVisible: false, // Drawer visibility
      listenerForm: { // Data for the listener form
        event: '',
        throwEvent: false,
        listenerType: '',
        class: '',
        expression: '',
        delegateExpression: '',
        entityType: '',
      },
      editingListenerIndex: -1, // Index of the listener being edited, -1 for new
      bpmnElement: null,
      bpmnInstances: null,
      rules: {
        event: [{ required: true, message: '请选择事件类型', trigger: 'change' }],
        listenerType: [{ required: true, message: '请选择监听器类型', trigger: 'change' }],
        class: [
          {
            required: true,
            message: '请填写 Java 类',
            trigger: 'blur',
          },
        ],
        expression: [
          {
            required: true,
            message: '请填写表达式',
            trigger: 'blur',
          },
        ],
        delegateExpression: [
          {
            required: true,
            message: '请填写代理表达式',
            trigger: 'blur',
          },
        ],
        entityType: [{ required: true, message: '请填写实体类型', trigger: 'blur' }],
      },
    };
  },
   computed: {
    isFormDisabled() {
      return !!(this.scene === 'DETAIL')
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(val) {
        if (val) {
          this.$nextTick(() => {
            this.bpmnInstances = window.bpmnInstances;
            this.bpmnElement = this.bpmnInstances.bpmnElement;
            this.readProcessEventListeners();
          });
        }
      }
    }
  },
  methods: {
    readProcessEventListeners() {
      this.elementListenersList = []; // Clear the list
      const businessObject = this.bpmnElement.businessObject;
      let extensionElements = businessObject?.extensionElements || {};

      /**兼容Participant节点 */
      const ins = getCurrentBpmnElement();
      const insType = ins?.type;
      if (insType === 'bpmn:Participant') {
        const bpmnEle = getProcessElement(ins);
        extensionElements = bpmnEle?.extensionElements;
      }
      /**兼容Participant节点 */

      if (extensionElements && extensionElements.values) {
        const processListeners = extensionElements.values.filter(
          (ex) => ex.$type === `${this.prefix}:EventListener`
        );

        this.elementListenersList = processListeners.map(listener => {
            const mappedListener = {
              event: listener.event || '',
              throwEvent: listener.throwEvent !== undefined ? listener.throwEvent : false,
              listenerType: '',
              class: listener.class || '',
              expression: listener.expression || '',
              delegateExpression: listener.delegateExpression || '',
              entityType: listener.entityType || '',
            };

            if (mappedListener.class) mappedListener.listenerType = 'class';
            else if (mappedListener.expression) mappedListener.listenerType = 'expression';
            else if (mappedListener.delegateExpression) mappedListener.listenerType = 'delegateExpression';

           return mappedListener;
        });
      }
    },
    openListenerForm(listener, index) {
      if (listener) {
        this.listenerForm = JSON.parse(JSON.stringify(listener)); // Deep copy
        this.editingListenerIndex = index;
      } else {
        this.resetListenerForm();
        this.editingListenerIndex = -1;
      }
       this.$nextTick(() => {
         if (this.$refs["listenerFormRef"]) this.$refs["listenerFormRef"].clearValidate();
       });
      this.listenerFormModelVisible = true;
    },
    saveListener() {
      this.$refs["listenerFormRef"].validate(valid => {
        if (valid) {
          const bpmnFactory = this.bpmnInstances.bpmnFactory;
          const modeling = this.bpmnInstances.modeling;
          let businessObject = this.bpmnElement.businessObject;
          let extensionElements = businessObject.extensionElements;
          let values = [];
          let bpmnEle = window.bpmnInstances.bpmnElement;

          /**兼容Participant节点 */
          const ins = getCurrentBpmnElement();
          const insType = ins?.type;
          if (insType === 'bpmn:Participant') {
            // const bpmnFactory = window.bpmnInstances?.bpmnFactory;
            bpmnEle = getProcessElement(ins);
            businessObject = ins?.businessObject;
            extensionElements = bpmnEle?.extensionElements || null;
          }
          /**兼容Participant节点 */


          if (extensionElements) {
            values = extensionElements.values || [];
          } else {
            // 如果不存在 extensionElements，创建一个新的
            extensionElements = bpmnFactory.create('bpmn:ExtensionElements');
            modeling.updateModdleProperties(bpmnEle, businessObject, {
              extensionElements: extensionElements
            });
          }

          if (this.editingListenerIndex !== -1) {
            // 编辑现有监听器
            console.log(this.editingListenerIndex,'this.editingListenerIndex')
            const existingListener = values.find(value => {
              if (value.$type !== `${this.prefix}:EventListener`) return false;

              const currentListener = this.elementListenersList[this.editingListenerIndex];
              const valueType = value.class ? 'class' :
                              value.expression ? 'expression' :
                              value.delegateExpression ? 'delegateExpression' : '';

              return value.event === currentListener.event &&
                     valueType === currentListener.listenerType &&
                     value.entityType === currentListener.entityType;
            });

            if (existingListener) {
              // 更新现有监听器的属性
              modeling.updateModdleProperties(existingListener, existingListener, {
                event: this.listenerForm.event,
                throwEvent: this.listenerForm.throwEvent,
                entityType: this.listenerForm.entityType,
                class: this.listenerForm.listenerType === 'class' ? this.listenerForm.class : null,
                expression: this.listenerForm.listenerType === 'expression' ? this.listenerForm.expression : null,
                delegateExpression: this.listenerForm.listenerType === 'delegateExpression' ? this.listenerForm.delegateExpression : null
              });
            }
          } else {
            // 创建新的监听器对象
            const newListenerObject = bpmnFactory.create(`${this.prefix}:EventListener`, {
              event: this.listenerForm.event,
              throwEvent: this.listenerForm.throwEvent,
              entityType: this.listenerForm.entityType,
              ...(this.listenerForm.listenerType === 'class' && { class: this.listenerForm.class }),
              ...(this.listenerForm.listenerType === 'expression' && { expression: this.listenerForm.expression }),
              ...(this.listenerForm.listenerType === 'delegateExpression' && { delegateExpression: this.listenerForm.delegateExpression }),
            });
            values.push(newListenerObject);
            modeling.updateModdleProperties(extensionElements, extensionElements, {
              values: values
            });
          }

          this.listenerFormModelVisible = false;
          this.readProcessEventListeners();
          this.$nextTick(() => {
            if (this.$refs["listenerFormRef"]) {
              this.$refs["listenerFormRef"].clearValidate();
            }
          });
        }
      });
    },
    removeListener(listener) {
      this.$confirm("确认移除该监听器吗？", "提示", {
        confirmButtonText: "确 认",
        cancelButtonText: "取 消"
      })
        .then(() => {
          const modeling = this.bpmnInstances.modeling;
          let businessObject = this.bpmnElement.businessObject;
          let extensionElements = businessObject.extensionElements;

          /**兼容Participant节点 */
          const ins = getCurrentBpmnElement();
          const insType = ins?.type;
          if (insType === 'bpmn:Participant') {
            // const bpmnFactory = window.bpmnInstances?.bpmnFactory;
            const bpmnEle = getProcessElement(ins);
            businessObject = ins?.businessObject;
            extensionElements = bpmnEle?.extensionElements || {};
          }
          /**兼容Participant节点 */

          if (extensionElements && extensionElements.values) {
            const updatedValues = extensionElements.values.filter(value => {
              if (value.$type !== `${this.prefix}:EventListener`) return true;
              const valueType = value.class ? 'class' :
                              value.expression ? 'expression' :
                              value.delegateExpression ? 'delegateExpression' : '';
              return !(value.event === listener.event &&
                      valueType === listener.listenerType &&
                      value.entityType === listener.entityType);
            });
            modeling.updateModdleProperties(extensionElements, extensionElements, {
              values: updatedValues
            });
            this.readProcessEventListeners();
          }
        })
        .catch(() => console.info("操作取消"));
    },
    resetListenerForm() {
       this.listenerForm = {
         event: '',
         throwEvent: false,
         listenerType: '',
         class: '',
         expression: '',
         delegateExpression: '',
         entityType: '',
       };
    },

  }
};
</script>

<style scoped>
.panel-tab__content h4 {
  margin-top: 20px;
}
.element-drawer__button {
    text-align: right;
    padding: 8px 0;
}
</style>