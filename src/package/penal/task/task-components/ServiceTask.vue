<template>
  <el-form size="small" label-width="90px" @submit.prevent>
    <el-form-item label="服务类型">
      <el-select v-model="serviceType" placeholder="请选择服务类型" @change="updateServiceTask">
        <el-option label="Java类" value="java" />
        <el-option label="表达式" value="expression" />
        <el-option label="代理表达式" value="delegateExpression" />
      </el-select>
    </el-form-item>
    <el-form-item :label="serviceTypeDisplayName">
      <el-input v-model="serviceImplementation" clearable @change="updateServiceTask" />
    </el-form-item>
    <el-form-item label="结果变量" v-if="serviceType === 'expression'">
      <el-input v-model="resultVariable" clearable @change="updateServiceTask" />
    </el-form-item>

    <div class="panel-tab__content">
      <el-divider>
        <el-icon><Menu /></el-icon>注入字段
      </el-divider>
      <el-table :data="fieldList" size="small" max-height="240" border fit>
        <el-table-column label="序号" width="50px" type="index" />
        <el-table-column label="字段名" prop="name" min-width="80px" show-overflow-tooltip />
        <el-table-column label="字段类型" prop="fieldType" min-width="80px" show-overflow-tooltip />
        <el-table-column label="字段值" prop="fieldValue" min-width="80px" show-overflow-tooltip />
        <el-table-column label="操作" width="130px">
          <template #default="{ row }">
            <el-button link type="text" @click="openFieldForm(row)">编辑</el-button>
            <el-divider direction="vertical" />
            <el-button link type="text" style="color: #ff4d4f" @click="removeField(row)">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="element-drawer__button">
        <el-button size="small" type="primary" :icon="Plus" @click="openFieldForm(null)">创建字段</el-button>
      </div>
    </div>

    <el-drawer
      v-model="fieldFormModelVisible"
      title="字段配置"
      :size="`${width}px`"
      append-to-body
      destroy-on-close
    >
      <el-form :model="fieldForm" label-width="90px" size="small" ref="fieldFormRef" @submit.prevent>
        <el-form-item label="字段名称：" prop="name" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-input v-model="fieldForm.name" clearable />
        </el-form-item>
        <el-form-item label="字段类型：" prop="fieldType" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-select v-model="fieldForm.fieldType" placeholder="请选择字段类型">
            <el-option label="字符串" value="string" />
            <el-option label="表达式" value="expression" />
          </el-select>
        </el-form-item>
        <el-form-item label="字段值：" prop="fieldValue" :rules="{ required: true, trigger: ['blur', 'change'] }">
          <el-input v-model="fieldForm.fieldValue" type="textarea" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
         <div class="element-drawer__button">
           <el-button size="small" @click="fieldFormModelVisible = false">取 消</el-button>
           <el-button size="small" type="primary" @click="saveField">确 定</el-button>
         </div>
       </template>
    </el-drawer>
  </el-form>
</template>

<script>
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';
import { Plus, Menu } from '@element-plus/icons-vue';

export default {
  name: "ServiceTask",
  setup() {
    return { Plus, Menu };
  },
  props: {
    id: String,
    type: String,
    scene: String,
  },
  inject: {
    prefix: "prefix",
    width: "width",
  },
  data() {
    return {
      serviceType: "java",
      serviceImplementation: "",
      resultVariable: "",
      fieldList: [],
      fieldForm: {},
      fieldFormModelVisible: false,
      editingFieldIndex: -1,
    };
  },
  computed: {
    serviceTypeDisplayName() {
      switch (this.serviceType) {
        case "java":
          return "Java类";
        case "expression":
          return "表达式";
        case "delegateExpression":
          return "代理表达式";
        default:
          return "服务实现";
      }
    },
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        const businessObject = this.bpmnElement?.businessObject;
        if (businessObject) {
          this.serviceImplementation = businessObject.get("class") || businessObject.get("expression") || businessObject.get("delegateExpression") || "";
          if (businessObject.get("class")) {
            this.serviceType = "java";
          } else if (businessObject.get("expression")) {
            this.serviceType = "expression";
          } else if (businessObject.get("delegateExpression")) {
            this.serviceType = "delegateExpression";
          } else {
            this.serviceType = "java";
          }
          this.resultVariable = (this.serviceType === 'expression' && businessObject.get("resultVariable")) || "";
        }
        this.$nextTick(() => {
          this.readFields();
        });
      },
    },
    serviceType: {
      handler(newType) {
        if (newType !== 'expression') {
          this.resultVariable = "";
        }
      }
    }
  },
  methods: {
    updateServiceTask() {
      let properties = {};
      if (this.serviceType === "java") {
        properties = {
          "flowable:class": this.serviceImplementation,
          "flowable:expression": undefined,
          "flowable:delegateExpression": undefined,
          "flowable:resultVariable": undefined,
        };
      } else if (this.serviceType === "expression") {
        properties = {
          "flowable:expression": this.serviceImplementation,
          "flowable:resultVariable": this.resultVariable,
          "flowable:class": undefined,
          "flowable:delegateExpression": undefined,
          implementation: undefined,
          type: undefined,
        };
      } else if (this.serviceType === "delegateExpression") {
        properties = {
          "flowable:delegateExpression": this.serviceImplementation,
          "flowable:resultVariable": undefined,
          "flowable:class": undefined,
          "flowable:expression": undefined,
          implementation: undefined,
          type: undefined,
        };
      }
      window.bpmnInstances.modeling.updateProperties(this.bpmnElement, properties);
    },
    readFields() {
      this.fieldList = [];
      const businessObject = getBusinessObject(this.bpmnElement);
      if (!businessObject || !businessObject.extensionElements) return;

      const extensionElements = businessObject.extensionElements.values;
      if (!extensionElements || extensionElements.length === 0) return;

      const fields = extensionElements.filter(
        (ex) => ex.$type === `${this.prefix}:Field`
      );

      this.fieldList = fields.map((field) => ({
        name: field.name,
        fieldType:
          (field.string && "string") ||
          (field.expression && "expression") ||
          (field.htmlVar && "html"),
        fieldValue: field.string?.body || field.expression?.body || field.htmlVar?.body || "",
      }));
    },
    openFieldForm(field) {
      this.editingFieldIndex = this.fieldList.indexOf(field);
      this.fieldForm = field ? JSON.parse(JSON.stringify(field)) : {};
      this.fieldFormModelVisible = true;
      this.$nextTick(() => {
        if (this.$refs["fieldFormRef"]) this.$refs["fieldFormRef"].clearValidate();
      });
    },
    saveField() {
      this.$refs["fieldFormRef"].validate((valid) => {
        if (valid) {
          const bpmnFactory = window.bpmnInstances.bpmnFactory;
          const modeling = window.bpmnInstances.modeling;
          const businessObject = getBusinessObject(this.bpmnElement);

          let extensionElements = businessObject.extensionElements;
          if (!extensionElements) {
            extensionElements = bpmnFactory.create("bpmn:ExtensionElements", {
              values: [],
            });
            modeling.updateModdleProperties(this.bpmnElement, businessObject, {
              extensionElements: extensionElements,
            });
            extensionElements = businessObject.extensionElements;
          }

          const fieldBodyType = {
            string: "flowable:string",
            expression: "flowable:expression",
            html: "flowable:string",
          };

          const newFieldBody = bpmnFactory.create(fieldBodyType[this.fieldForm.fieldType], {
            body: this.fieldForm.fieldValue,
          });

          const newField = bpmnFactory.create(`${this.prefix}:Field`, {
            name: this.fieldForm.name,
            [this.fieldForm.fieldType === 'html' ? 'htmlVar' : this.fieldForm.fieldType]: newFieldBody,
          });

          let updatedExtensionValues = [...extensionElements.values];

          if (this.editingFieldIndex !== -1) {
            const existingField = updatedExtensionValues.find(
              (ex) => ex.$type === `${this.prefix}:Field` && ex.name === this.fieldForm.name
            );
            if (existingField) {
              const index = updatedExtensionValues.indexOf(existingField);
              updatedExtensionValues[index] = newField;
            }
          } else {
            updatedExtensionValues.push(newField);
          }

          modeling.updateModdleProperties(this.bpmnElement, extensionElements, {
            values: updatedExtensionValues,
          });

          this.fieldFormModelVisible = false;
          this.readFields();
        }
      });
    },
    removeField(field) {
      this.$confirm("确认移除该字段吗？", "提示", {
        confirmButtonText: "确 认",
        cancelButtonText: "取 消",
      })
        .then(() => {
          const modeling = window.bpmnInstances.modeling;
          const businessObject = getBusinessObject(this.bpmnElement);
          let extensionElements = businessObject.extensionElements;

          if (!extensionElements || !extensionElements.values) return;

          const updatedExtensionValues = extensionElements.values.filter(
            (ex) => !(ex.$type === `${this.prefix}:Field` && ex.name === field.name)
          );

          modeling.updateModdleProperties(this.bpmnElement, extensionElements, {
            values: updatedExtensionValues,
          });

          this.readFields();
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.panel-tab__content {
  .el-divider {
    margin-top: 20px;
    margin-bottom: 20px;

    .el-divider__text {
      font-size: 14px;
      font-weight: 600;
    }
  }
}

.element-drawer__button {
  width: 100%;
  border-top: 1px solid var(--el-border-color-light);
  padding: 16px;
  text-align: right;
  background-color: var(--el-bg-color);
}

.el-drawer__body {
  padding-bottom: 70px; /* 为底部按钮留出空间，具体数值可能需要根据实际情况微调 */
  box-sizing: border-box; /* 确保 padding 包含在高度计算中 */
  /* 允许抽屉内容区域垂直滚动，而不是整个抽屉滚动 */
  overflow-y: auto;
}

.el-drawer__body > .el-form {
  height: 100%; /* Make the form take up the full height of the body */
  display: flex;
  flex-direction: column;
}

.el-drawer__body > .el-form > .el-form-item {
  flex-shrink: 0; /* Prevent form items from shrinking if content is long */
}

.el-drawer__body > .el-form > .el-form-item.is-required.el-form-item--feedback {
  flex-grow: 1; /* Allow the last form item (textarea) to grow */
}
</style>
