<template>
  <el-form size="small" label-width="90px" @submit.prevent>
    <el-form-item label="服务ID">
      <el-input v-model="serviceId" clearable @change="updateMicroServiceTask" />
    </el-form-item>
    <el-form-item label="请求地址">
      <el-input v-model="url" clearable @change="updateMicroServiceTask" />
    </el-form-item>
    <el-form-item label="请求方式">
      <el-select v-model="method" @change="updateMicroServiceTask">
        <el-option label="GET" value="GET" />
        <el-option label="POST" value="POST" />
        <el-option label="PUT" value="PUT" />
        <el-option label="DELETE" value="DELETE" />
      </el-select>
    </el-form-item>
    <el-form-item label="请求头">
      <el-input 
        type="textarea" 
        v-model="headers" 
        :rows="2" 
        placeholder="JSON格式，如：{'Content-Type':'application/json'}"
        @change="updateMicroServiceTask" 
      />
    </el-form-item>
    <el-form-item label="请求参数">
      <el-input 
        type="textarea" 
        v-model="params" 
        :rows="3" 
        placeholder="JSON格式参数"
        @change="updateMicroServiceTask" 
      />
    </el-form-item>
    <el-form-item label="保存响应">
      <el-switch v-model="saveResponseParameters" @change="updateMicroServiceTask" />
    </el-form-item>
    <el-form-item label="忽略异常">
      <el-switch v-model="ignoreException" @change="updateMicroServiceTask" />
    </el-form-item>
    <el-form-item label="响应变量名">
      <el-input v-model="responseVariableName" clearable @change="updateMicroServiceTask" />
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  name: "MicroServiceTask",
  props: {
    id: String,
    type: String
  },
  data() {
    return {
      serviceId: "",
      url: "",
      method: "POST",
      headers: "",
      params: "",
      saveResponseParameters: false,
      ignoreException: false,
      responseVariableName: ""
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        const businessObject = this.bpmnElement?.businessObject;
        if (businessObject && businessObject.extensionElements) {
          const fields = businessObject.extensionElements.values;
          fields.forEach(field => {
            if (field.$type === 'flowable:Field') {
              this[field.name] = field.stringValue || field.expression || '';
            }
          });
        }
      }
    }
  },
  methods: {
    updateMicroServiceTask() {
      const moddle = window.bpmnInstances.moddle;
      const extensionElements = moddle.create('bpmn:ExtensionElements', {
        values: [
          this.createFieldWithString('serviceId', this.serviceId || ''),
          this.createFieldWithString('url', this.url || ''),
          this.createFieldWithString('method', this.method || 'POST'),
          this.createFieldWithString('headers', this.headers || ''),
          this.createFieldWithExpression('params', this.params || ''),
          this.createFieldWithString('saveResponseParameters', 
            this.saveResponseParameters !== undefined ? this.saveResponseParameters.toString() : 'false'),
          this.createFieldWithString('ignoreException', 
            this.ignoreException !== undefined ? this.ignoreException.toString() : 'false')
        ]
      });

      window.bpmnInstances.modeling.updateProperties(window.bpmnInstances.bpmnElement, {
        extensionElements: extensionElements
      });
    },
    createFieldWithString(name, value) {
      const moddle = window.bpmnInstances.moddle;
      const stringElement = moddle.create('flowable:string', {
        body: String(value || '')
      });
      return moddle.create('flowable:Field', {
        name: name,
        string: stringElement
      });
    },
    createFieldWithExpression(name, value) {
      const moddle = window.bpmnInstances.moddle;
      const expressionElement = moddle.create('flowable:expression', {
        body: String(value || '')
      });
      return moddle.create('flowable:Field', {
        name: name,
        expression: expressionElement
      });
    }
  }
};
</script>
