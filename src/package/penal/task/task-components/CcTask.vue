<template>
  <div>
    <el-form label-width="80px" size="small">
      <el-form-item label="抄送人员">
        <!-- 显示已选人员名称 -->
        <el-tag
          v-for="user in selectedUsers.list"
          :key="user.id"
          effect="plain"
          closable
          @close="handleRemoveUser(user)"
        >
          {{ user.name }}
        </el-tag>
        <!-- 触发人员选择弹窗的按钮/图标 -->
        <el-button
          type="primary"
          :icon="Search"
          circle
          size="small"
          @click="openUserDialog"
        ></el-button>
      </el-form-item>
      <el-form-item label="抄送内容">
        <el-input
          type="textarea"
          :rows="4"
          v-model="ccContent"
          placeholder="请输入抄送内容"
          @change="updateContent"
        ></el-input>
      </el-form-item>
    </el-form>

    <!-- 候选用户弹窗 -->
    <el-dialog title="选择抄送人员" v-model="userDialogVisible" width="60%" append-to-body>
      <el-row type="flex" :gutter="20">
        <!--部门数据-->
        <el-col :span="7">
          <el-card shadow="never" style="height: 100%">
            <div class="head-container">
              <el-input
                v-model="deptName"
                placeholder="请输入部门名称"
                clearable
                size="small"
                :prefix-icon="Search"
                style="margin-bottom: 20px"
                @input="filterDeptNode"
              />
              <el-tree
                :data="deptOptions"
                :props="deptProps"
                :expand-on-click-node="false"
                :filter-node-method="filterNode"
                ref="deptTreeRef"
                :default-expand-all="false"
                node-key="id"
                highlight-current
                @current-change="handleDeptNodeClick"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="17">
          <el-form
            :model="queryParams"
            ref="queryFormRef"
            size="small"
            :inline="true"
            label-width="68px"
          >
            <el-form-item label="用户名称" prop="name">
              <el-input
                v-model="queryParams.userName"
                placeholder="请输入用户名称"
                clearable
                style="width: 150px"
                @keyup.enter="handleQueryUsers"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                :icon="Search"
                @click="handleQueryUsers"
              >搜索</el-button>
              <el-button
                :icon="Refresh"
                @click="resetUserQuery"
              >重置</el-button>
            </el-form-item>
          </el-form>
          <el-table
            ref="userTableRef"
            height="600"
            v-loading="userTableLoading"
            :data="userList"
            border
            @selection-change="handleUserSelectionChange"
          >
            <el-table-column type="selection" width="50" align="center" />
            <el-table-column label="用户名" align="center" prop="name" />
            <el-table-column label="部门" align="center" prop="deptName" />
          </el-table>
          <pagination
            v-show="userTotal>0"
            :total="userTotal"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getUserList"
          />
        </el-col>
      </el-row>
      <template #footer>
        <el-button size="default" @click="userDialogVisible = false">取 消</el-button>
        <el-button type="primary" size="default" @click="handleUserSelectConfirm">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';
import { Search, Refresh } from '@element-plus/icons-vue'
// Assuming these API calls exist based on UserTask.vue reference
import { deptTree as deptTreeSelect } from '/@/api/admin/dept';
import { pageList as listUser } from '/@/api/admin/user';
// Assuming pagination component is available
import Pagination from '/@/components/Pagination/index.vue';

const props = defineProps({
  element: Object,
});

const ccUserNames = ref(''); // This will still be used to display names string in the input (or tags)
const ccContent = ref(''); // 用于绑定抄送内容文本域
const selectedUsers = ref({ list: [] }); // Store selected user objects
const temporarySelectedUsers = ref([]); // Temporary storage for selected users in dialog

const userDialogVisible = ref(false);
const deptName = ref('');
const deptOptions = ref([]);
const deptProps = ref({
  children: "children",
  label: "name",
  value: 'id'
});
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userName: undefined,
  deptId: undefined
});
const userTableLoading = ref(false);
const userList = ref([]);
const userTotal = ref(0);
const userTableRef = ref(null); // Ref for the user table
const deptTreeRef = ref(null); // Ref for the department tree

// Get BPMN instances from window
const bpmnInstances = window.bpmnInstances;
const moddle = bpmnInstances.moddle;

// Helper function to create a flowable:Field with flowable:String
function createFieldWithString(name, value) {
  const stringElement = moddle.create('flowable:String', {
    body: String(value || '')
  });
  return moddle.create('flowable:Field', {
    name: name,
    string: stringElement
  });
}

// Load properties from BPMN element
function loadProperties(element) {
  const businessObject = element.businessObject;
  const extensionElements = businessObject.extensionElements;
  selectedUsers.value.list = []; // Reset selected users
  ccContent.value = ''; // Reset content

  if (extensionElements) {
    const fields = extensionElements.values.filter(v => v.$type === 'flowable:Field');
    const transferToUserNosField = fields.find(f => f.name === 'transferToUserNos');
    const contentField = fields.find(f => f.name === 'content');
    const transferToUsersElement = extensionElements.values.find(v => v.$type === 'flowable:transferToUsers');

    // Load ccUserNames (display string)
    if (transferToUserNosField && transferToUserNosField.string && transferToUserNosField.string.body) {
      // Note: MicroServiceTask uses string, not expression for string values
      ccUserNames.value = transferToUserNosField.string.body;
    }

    // Load ccContent
    if (contentField && contentField.string && contentField.string.body) {
       // Note: MicroServiceTask uses string, not expression for string values
      ccContent.value = contentField.string.body;
    }

    // Load selected user IDs from flowable:transferToUsers
    if (transferToUsersElement && transferToUsersElement.body) {
      try {
        const userIds = JSON.parse(transferToUsersElement.body);
        if (Array.isArray(userIds)) {
          // Here you would typically fetch user details based on IDs
          // For now, let's assume you have a way to get user objects by ID
          // This part needs actual implementation based on your user data structure and API
          // For demonstration, I'll just create dummy user objects
          // In a real scenario, you would fetch user details here based on userIds
          selectedUsers.value.list = userIds.map(id => ({ id: id, name: `User_${id}` })); // Replace with actual fetch
        }
      } catch (e) {
        selectedUsers.value.list = [];
      }
    }
  }
   // Initialize temporary selection when dialog opens based on current selectedUsers
   // This is handled in openUserDialog now
}

// Update BPMN element properties
function updateElementProperties() {
  if (!props.element) return;

  const businessObject = props.element.businessObject;

  // Always create a new extensionElements object
  const extensionElements = moddle.create('bpmn:ExtensionElements');

  // Create and add flowable:Field for transferToUserNos (display names)
  const transferToUserNosField = createFieldWithString('transferToUserNos', selectedUsers.value.list.map(user => user.name).join(','));
  extensionElements.get('values').push(transferToUserNosField);

  // Create and add flowable:transferToUsers element (user IDs as JSON string)
  const transferToUsersElement = moddle.create('flowable:transferToUsers', {
     body: JSON.stringify(selectedUsers.value.list.map(user => user.id))
  });
  extensionElements.get('values').push(transferToUsersElement);

  // Create and add flowable:Field for content
   const contentField = createFieldWithString('content', ccContent.value);
   extensionElements.get('values').push(contentField);

  // Update the BPMN element with the new extensionElements object
  bpmnInstances.modeling.updateProperties(businessObject, {
    extensionElements: extensionElements
  });
}

// Open user selection dialog
function openUserDialog() {
  userDialogVisible.value = true;
  // Initialize temporary selection from current selectedUsers
  temporarySelectedUsers.value = [...selectedUsers.value.list];
  getDeptTree();
  getUserList();
  // Pre-select users in the table based on temporarySelectedUsers
  // This requires accessing the table ref after data is loaded and rendered
}

// Handle user selection change in the table
function handleUserSelectionChange(_selection) {
  // Store the temporary selection from the table
  temporarySelectedUsers.value = _selection;
}

// Handle user selection confirmation
function handleUserSelectConfirm() {
  // Update selectedUsers.value.list from the temporary selection
  selectedUsers.value.list = temporarySelectedUsers.value;
  updateElementProperties();
  userDialogVisible.value = false;
}

// Remove selected user tag
function handleRemoveUser(user) {
  selectedUsers.value.list = selectedUsers.value.list.filter(u => u.id !== user.id);
  updateElementProperties();
}

// Handle content change and update properties
function updateContent() {
    updateElementProperties();
}

// Fetch department tree
async function getDeptTree() {
  const res = await deptTreeSelect();
  deptOptions.value = res.data;
}

// Fetch user list
async function getUserList() {
  userTableLoading.value = true;
  const res = await listUser(queryParams.value);
  userTableLoading.value = false;
  userList.value = res.data.records;
  userTotal.value = res.data.total;
  // After loading users, pre-select currently selected users in the table
  preselectUsersInTable();
}

// Filter department tree nodes
function filterNode(value, data) {
  if (!value) return true;
  return data.label.indexOf(value) !== -1;
}

// Filter department tree input
function filterDeptNode() {
  if (deptTreeRef.value) {
     deptTreeRef.value.filter(deptName.value);
  }
}

// Handle department node click
function handleDeptNodeClick(data) {
  queryParams.value.deptId = data.id;
  getUserList();
}

// Handle user search query
function handleQueryUsers() {
  queryParams.value.pageNum = 1;
  getUserList();
}

// Reset user search query
function resetUserQuery() {
  queryParams.value.userName = undefined;
  queryParams.value.deptId = undefined;
   if (deptTreeRef.value) {
     // Assuming there's a method to clear selection or highlighted node
     // deptTreeRef.value.setCurrentKey(null);
   }
  handleQueryUsers();
}

// Function to pre-select users in the table when dialog opens or user list updates
function preselectUsersInTable() {
    if (userTableRef.value && temporarySelectedUsers.value.length > 0 && userList.value.length > 0) {
        userTableRef.value.clearSelection(); // Clear existing selection
        userList.value.forEach(user => {
            if (temporarySelectedUsers.value.some(selectedUser => selectedUser.id === user.id)) {
                userTableRef.value.toggleRowSelection(user, true);
            }
        });
    }
}

// Watch for element changes to load properties
watch(() => props.element, (element) => {
  if (element) {
    loadProperties(element);
  }
}, { immediate: true });

// Watch for ccContent changes to update element properties
watch(ccContent, () => {
  updateElementProperties();
});

// Watch for changes in userList (when fetched) and userDialogVisible to pre-select users
watch([userList, userDialogVisible], ([newUserList, newUserDialogVisible]) => {
    if (newUserList.length > 0 && newUserDialogVisible) {
        preselectUsersInTable();
    }
}, { immediate: true });

</script>

<style scoped>
/* 在这里添加样式 */
</style> 