<template>
  <div class="panel-tab__content">
    <el-form size="small" label-width="90px" @submit.prevent :disabled="isFormDisabled">
<!--      <el-form-item label="异步延续">-->
<!--        <el-checkbox v-model="taskConfigForm.asyncBefore" label="异步前" @change="changeTaskAsync" />-->
<!--        <el-checkbox v-model="taskConfigForm.asyncAfter" label="异步后" @change="changeTaskAsync" />-->
<!--        <el-checkbox v-model="taskConfigForm.exclusive" v-if="taskConfigForm.asyncAfter || taskConfigForm.asyncBefore" label="排除" @change="changeTaskAsync" />-->
<!--      </el-form-item>-->
      <component :is="witchTaskComponent" v-bind="$props" />
    </el-form>
  </div>
</template>

<script>
import UserTask from "./task-components/UserTask.vue";
import ScriptTask from "./task-components/ScriptTask.vue";
import ReceiveTask from "./task-components/ReceiveTask.vue";
import ServiceTask from "./task-components/ServiceTask.vue";
import MicroServiceTask from "./task-components/MicroServiceTask.vue";
import CcTask from "./task-components/CcTask.vue";

export default {
  name: "ElementTaskConfig",
  components: { UserTask, ScriptTask, ReceiveTask, ServiceTask, MicroServiceTask, CcTask },
  props: {
    id: String,
    type: String,
    scene: String
  },
  data() {
    return {
      taskConfigForm: {
        asyncAfter: false,
        asyncBefore: false,
        exclusive: false
      },
      comType: this.type,
      witchTaskComponent: "",
      installedComponent: {
        UserTask: "UserTask",
        ScriptTask: "ScriptTask",
        ReceiveTask: "ReceiveTask",
        ServiceTask: "ServiceTask",
        MicroServiceTask: "MicroServiceTask",
        CcTask: "CcTask"
      }
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.bpmnElement = window.bpmnInstances.bpmnElement;
        const businessObject = this.bpmnElement?.businessObject;
        this.comType = this.type
        if (businessObject && businessObject.$type === 'bpmn:ServiceTask') {
          if (businessObject.get('flowable:type') === 'sc') {
            this.comType = 'MicroServiceTask';
          } else if (businessObject.get('flowable:type') === 'copy') {
            this.comType = 'CcTask';
          } else {
            this.comType = 'ServiceTask';
          }
        }
      }
    },
    type: {
      immediate: true,
      handler() {
        this.witchTaskComponent = this.installedComponent[this.comType];
      }
    }
  },
  computed: {
    isFormDisabled() {
      return !!(this.scene === 'DETAIL')
    }
  },
  methods: {
    changeTaskAsync() {
      if (!this.taskConfigForm.asyncBefore && !this.taskConfigForm.asyncAfter) {
        this.taskConfigForm.exclusive = false;
      }
      window.bpmnInstances.modeling.updateProperties(window.bpmnInstances.bpmnElement, {
        ...this.taskConfigForm
      });
    }
  }
};
</script>
