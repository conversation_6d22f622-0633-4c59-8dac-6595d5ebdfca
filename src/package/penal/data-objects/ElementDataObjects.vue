<template>
  <div class="panel-tab__content">
    <!-- 数据对象列表 -->
    <el-table :data="dataObjectsList" size="small" border>
      <el-table-column label="序号" width="50px" type="index" />
      <el-table-column label="名称" min-width="100px" prop="name" show-overflow-tooltip />
      <el-table-column label="数据类型" min-width="100px" show-overflow-tooltip>
        <template #default="{ row }">
          {{ getDataTypeLabel(row.itemSubjectRef) }}
        </template>
      </el-table-column>
      <el-table-column label="默认值" min-width="100px" prop="value" show-overflow-tooltip />
      <el-table-column label="操作" width="130px">
        <template #default="{ row, $index }">
          <el-button link type="text" size="small" @click="openDataObjectForm(row, $index)">编辑</el-button>
          <el-divider direction="vertical" />
          <el-button link type="text" style="color:#ff4d4f" size="small" @click="removeDataObject(row)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加数据对象按钮 -->
    <div class="element-drawer__button">
      <el-button size="small" type="primary" :icon="Plus" @click="openDataObjectForm(null)">添加数据对象</el-button>
    </div>

    <!-- 数据对象配置抽屉 -->
    <el-drawer
      v-model="dataObjectFormModelVisible"
      title="数据对象配置"
      :size="`${width}px`"
      append-to-body
      destroy-on-close
    >
      <el-form size="small" label-width="90px" ref="dataObjectFormRef" :rules="rules" :model="dataObjectForm">
        <el-form-item label="名称" prop="name">
          <el-input v-model="dataObjectForm.name" clearable />
        </el-form-item>

        <el-form-item label="数据类型" prop="itemSubjectRef">
          <el-select v-model="dataObjectForm.itemSubjectRef">
            <el-option label="字符串" value="xsd:string"></el-option>
            <el-option label="布尔值" value="xsd:boolean"></el-option>
            <el-option label="时间" value="xsd:dateTime"></el-option>
            <el-option label="小数" value="xsd:double"></el-option>
            <el-option label="整数" value="xsd:integer"></el-option>
            <!-- 添加其他可能的类型 -->
          </el-select>
        </el-form-item>

         <el-form-item label="默认值" prop="value">
           <!-- 字符串类型 -->
           <el-input 
             v-if="dataObjectForm.itemSubjectRef === 'xsd:string'"
             v-model="dataObjectForm.value" 
             clearable 
           />
           <!-- 布尔值类型 -->
           <el-radio-group 
             v-else-if="dataObjectForm.itemSubjectRef === 'xsd:boolean'"
             v-model="dataObjectForm.value"
           >
             <el-radio label="true">是</el-radio>
             <el-radio label="false">否</el-radio>
           </el-radio-group>
           <!-- 时间类型 -->
           <el-date-picker
             v-else-if="dataObjectForm.itemSubjectRef === 'xsd:dateTime'"
             v-model="dataObjectForm.value"
             type="datetime"
             placeholder="选择日期时间"
             format="YYYY-MM-DD HH:mm:ss"
             value-format="YYYY-MM-DD HH:mm:ss"
           />
           <!-- 小数类型 -->
           <el-input-number
             v-else-if="dataObjectForm.itemSubjectRef === 'xsd:double'"
             v-model="dataObjectForm.value"
             :precision="2"
             :step="0.1"
             :controls="false"
           />
           <!-- 整数类型 -->
           <el-input-number
             v-else-if="dataObjectForm.itemSubjectRef === 'xsd:integer'"
             v-model="dataObjectForm.value"
             :precision="0"
             :step="1"
             :controls="false"
           />
         </el-form-item>

      </el-form>

      <template #footer>
         <div class="element-drawer__button">
           <el-button size="small" @click="dataObjectFormModelVisible = false">取 消</el-button>
           <el-button size="small" type="primary" @click="saveDataObject">保 存</el-button>
         </div>
       </template>

    </el-drawer>

  </div>
</template>

<script>
import { Plus } from '@element-plus/icons-vue';
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

export default {
  name: "ElementDataObjects",
   setup() {
    return { Plus };
  },
  props: {
    id: String,
  },
   inject: {
    prefix: "prefix",
    width: "width"
  },
  data() {
    return {
      dataObjectsList: [], // List of data objects
      dataObjectFormModelVisible: false, // Drawer visibility
      dataObjectForm: { // Data for the data object form
        name: '',
        itemSubjectRef: '',
        value: '',
      },
      editingDataObjectIndex: -1, // Index of the data object being edited, -1 for new
      rules: {
        name: [{ required: true, message: '请填写名称', trigger: 'blur' }],
        itemSubjectRef: [{ required: true, message: '请选择数据类型', trigger: 'change' }],
      },
      dataTypeMap: {
        'xsd:string': '字符串',
        'xsd:boolean': '布尔值',
        'xsd:dateTime': '时间',
        'xsd:double': '小数',
        'xsd:integer': '整数'
      }
    };
  },
  watch: {
    // Removed: The component will now get its BPMN context dynamically in each method.
    // id: {
    //   immediate: true,
    //   handler(val) {
    //     if (val) {
    //       this.$nextTick(() => {
    //         this.bpmnInstances = window.bpmnInstances;
    //         const rawElement = this.bpmnInstances.elementRegistry.get(val);
    //         this.bpmnElement = this.resolveProcessElement(rawElement);
    //         this.readDataObjects();
    //       });
    //     }
    //   }
    // }
  },
  methods: {
    getDataTypeLabel(value) {
      return this.dataTypeMap[value] || value;
    },
    // Helper to resolve the actual process element from a participant
    resolveProcessElement(currentElement) {
      const bpmnInstances = window.bpmnInstances;
      if (!bpmnInstances) return null;

      if (currentElement.businessObject && currentElement.businessObject.$type === 'bpmn:Participant') {
        const processRef = currentElement.businessObject.processRef;
        if (!processRef) {
          return null;
        }
        const processId = processRef.id;
        // 先尝试 elementRegistry
        let processElement = bpmnInstances.elementRegistry.get(processId);
        // 如果 elementRegistry 没有，再从 rootElements 查找
        if (!processElement && bpmnInstances.modeler) {
          const rootElements = bpmnInstances.modeler.getDefinitions().rootElements || [];
          processElement = rootElements.find(el => el.id === processId && el.$type === 'bpmn:Process');
        }
        if (!processElement) {
          return null;
        }
        return processElement;
      }
      return currentElement;
    },
    readDataObjects() {
      this.dataObjectsList = []; // Clear the list
      
      const bpmnInstances = window.bpmnInstances;
      if (!bpmnInstances || !bpmnInstances.elementRegistry || !this.id) {
        return;
      }

      const rawElement = bpmnInstances.elementRegistry.get(this.id);
      const actualProcessElement = this.resolveProcessElement(rawElement);
      const businessObject = getBusinessObject(actualProcessElement);

      if (!businessObject || !businessObject.dataObjects) {
        return;
      }

      this.dataObjectsList = businessObject.dataObjects.map(dataObject => {
        if (!dataObject) return null;
        
        // 获取 itemSubjectRef
        const itemSubjectRef = dataObject.itemSubjectRef?.id || dataObject.itemSubjectRef || '';
        
        // 获取 value
        let value = '';
        const extensionElements = dataObject.extensionElements;
        if (extensionElements && extensionElements.values) {
          const valueElement = extensionElements.values.find(val => val.$type === 'flowable:value');
          if (valueElement) {
            value = valueElement.body || valueElement.$body || '';
          }
        }

        return {
          id: dataObject.id,
          name: dataObject.name || '',
          itemSubjectRef: itemSubjectRef,
          value: value
        };
      }).filter(Boolean); // Remove any null entries
    },
    openDataObjectForm(dataObject, index) {
      if (dataObject) {
        this.dataObjectForm = JSON.parse(JSON.stringify(dataObject)); // Deep copy
        this.editingDataObjectIndex = index;
      } else {
        this.resetDataObjectForm();
        this.editingDataObjectIndex = -1;
      }
      this.dataObjectFormModelVisible = true;
       this.$nextTick(() => {
         if (this.$refs["dataObjectFormRef"]) this.$refs["dataObjectFormRef"].clearValidate();
       });
    },
    saveDataObject() {
      this.$refs["dataObjectFormRef"].validate(valid => {
        if (valid) {
          const bpmnInstances = window.bpmnInstances;
          if (!bpmnInstances || !bpmnInstances.bpmnFactory || !bpmnInstances.modeling || !bpmnInstances.elementRegistry || !bpmnInstances.moddle || !this.id) {
            return;
          }

          const bpmnFactory = bpmnInstances.bpmnFactory;
          const modeling = bpmnInstances.modeling;

          const rawElement = bpmnInstances.elementRegistry.get(this.id);
          const actualProcessElement = this.resolveProcessElement(rawElement);
          let processBusinessObject = getBusinessObject(actualProcessElement);

          if (!processBusinessObject) {
            return;
          }

          // 确保值被转换为字符串
          const valueString = String(this.dataObjectForm.value);

          if (this.editingDataObjectIndex !== -1) {
            // 编辑现有数据对象
            const existingDataObject = processBusinessObject.dataObjects[this.editingDataObjectIndex];
            if (!existingDataObject) {
              return;
            }

            // Update basic properties
            modeling.updateModdleProperties(existingDataObject, existingDataObject, {
              name: this.dataObjectForm.name,
              itemSubjectRef: this.dataObjectForm.itemSubjectRef
            });

            // Handle extensionElements
            let extensionElements = existingDataObject.extensionElements;
            if (!extensionElements) {
              extensionElements = bpmnFactory.create('bpmn:ExtensionElements');
              modeling.updateModdleProperties(existingDataObject, existingDataObject, {
                extensionElements: extensionElements
              });
            }

            // Find or create value element
            let valueElement = extensionElements.values?.find(val => val.$type === 'flowable:value');
            if (!valueElement) {
              valueElement = bpmnInstances.moddle.create('flowable:value', {
                body: valueString
              });
              const values = extensionElements.values || [];
              modeling.updateModdleProperties(extensionElements, extensionElements, {
                values: [...values, valueElement]
              });
            } else {
              modeling.updateModdleProperties(valueElement, valueElement, {
                body: valueString
              });
            }
          } else {
            // 创建新的数据对象
            const newDataObject = bpmnFactory.create('bpmn:DataObject', {
              id: `DataObject_${Math.random().toString(36).substr(2, 9)}`,
              name: this.dataObjectForm.name,
              itemSubjectRef: this.dataObjectForm.itemSubjectRef
            });

            // 创建 extensionElements 和 value
            const extensionElements = bpmnFactory.create('bpmn:ExtensionElements');
            const valueElement = bpmnInstances.moddle.create('flowable:value', {
              body: valueString
            });
            extensionElements.values = [valueElement];

            // 设置 extensionElements
            modeling.updateModdleProperties(newDataObject, newDataObject, {
              extensionElements: extensionElements
            });

            // 添加到数据对象列表
            const currentDataObjects = processBusinessObject.dataObjects || [];
            // 使用 modeling.updateModdleProperties 来更新 moddle 对象
            modeling.updateModdleProperties(actualProcessElement, processBusinessObject, {
              dataObjects: [...currentDataObjects, newDataObject]
            });
          }

          this.dataObjectFormModelVisible = false;
          this.readDataObjects();
          this.$nextTick(() => {
            if (this.$refs["dataObjectFormRef"]) {
              this.$refs["dataObjectFormRef"].clearValidate();
            }
          });
        }
      });
    },
    removeDataObject(dataObject) {
       const bpmnInstances = window.bpmnInstances;
       if (!bpmnInstances || !bpmnInstances.modeling || !bpmnInstances.elementRegistry || !this.id) {
           return;
       }
       const modeling = bpmnInstances.modeling;

       const rawElement = bpmnInstances.elementRegistry.get(this.id);
       const actualProcessElement = this.resolveProcessElement(rawElement);
       const businessObject = getBusinessObject(actualProcessElement);

       if (!businessObject || !businessObject.dataObjects) {
           return;
       }

       const updatedDataObjects = businessObject.dataObjects.filter(obj => obj.id !== dataObject.id);
       modeling.updateModdleProperties(actualProcessElement, businessObject, {
         dataObjects: updatedDataObjects
       });
       this.readDataObjects(); // Refresh the list
    },
    resetDataObjectForm() {
       this.dataObjectForm = {
         name: '',
         itemSubjectRef: '',
         value: '',
       };
    }
  }
};
</script>

<style scoped>
.element-drawer__button {
    text-align: right;
    padding: 8px 0;
}
</style> 