<template>
  <div class="panel-tab__content">
    <el-form label-width="90px" size="small">
      <el-form-item label="多实例类型">
        <el-radio-group v-model="multiInstanceType" @change="changeMultiInstanceType">
          <el-radio label="None">无</el-radio>
          <el-radio label="Parallel">并行</el-radio>
          <el-radio label="Sequential">串行</el-radio>
        </el-radio-group>
      </el-form-item>
      <template v-if="multiInstanceType !== 'None'">
        <el-form-item label="基数">
          <el-input v-model="loopCardinality" placeholder="例如: ${nrOfCompletedInstances/nrOfInstances == 1}" clearable @change="updateElement" />
        </el-form-item>
        <el-form-item label="集合">
          <el-input v-model="collection" placeholder="例如: assigneeList" clearable @change="updateElement" />
        </el-form-item>
        <el-form-item label="元素变量">
          <el-input v-model="elementVariable" placeholder="例如: assignee" clearable @change="updateElement" />
        </el-form-item>
        <el-divider>完成条件</el-divider>
        <el-form-item label="完成条件类型">
          <el-radio-group v-model="completionConditionType" @change="updateElement">
            <el-radio label="all">全部完成</el-radio>
            <el-radio label="percentage">完成百分比</el-radio>
            <el-radio label="count">完成数</el-radio>
            <el-radio label="expression">表达式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="completionConditionType !== 'all'" label="完成条件">
          <template v-if="completionConditionType === 'expression'">
            <el-input v-model="completionConditionExpression" placeholder="请输入完成条件表达式" clearable @change="updateElement" />
          </template>
          <template v-else-if="completionConditionType === 'count'">
            <div class="flex items-center">
              <el-select v-model="completionConditionOperator" placeholder="选择" class="w-20 mr-2" @change="updateElement">
                <el-option label="等于" value="=="></el-option>
                <el-option label="大于" value=">"></el-option>
                <el-option label="大于等于" value=">="></el-option>
                <el-option label="小于" value="<"></el-option>
                <el-option label="小于等于" value="<="></el-option>
              </el-select>
              <el-input v-model.number="completionConditionExpression" type="number" placeholder="请输入完成数" clearable @change="updateElement" class="flex-1" />
            </div>
          </template>
          <template v-else-if="completionConditionType === 'percentage'">
            <div class="flex items-center">
              <el-select v-model="completionConditionOperator" placeholder="选择" class="w-20 mr-2" @change="updateElement">
                <el-option label="等于" value="=="></el-option>
                <el-option label="大于" value=">"></el-option>
                <el-option label="大于等于" value=">="></el-option>
                <el-option label="小于" value="<"></el-option>
                <el-option label="小于等于" value="<="></el-option>
              </el-select>
              <el-input v-model.number="completionConditionExpression" type="number" placeholder="请输入完成百分比" clearable @change="updateElement" class="flex-1">
                <template #append>%</template>
              </el-input>
            </div>
          </template>
        </el-form-item>
      </template>
    </el-form>
  </div>
</template>

<script>
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

export default {
  name: "ElementMultiInstance",
  props: {
    id: String,
    type: String,
    scene: String,
  },
  inject: ["prefix"],
  data() {
    return {
      bpmnInstances: null,
      multiInstanceType: 'None', // 'None', 'Parallel', 'Sequential'
      loopCardinality: '',
      collection: '',
      elementVariable: '',
      completionConditionType: 'all',
      completionConditionExpression: '',
      completionConditionOperator: '==' // 新增：完成条件比较运算符
    };
  },
  watch: {
    id: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.bpmnInstances = window.bpmnInstances;
          // 确保我们获取的是当前选中的最新元素来初始化
          const selectedElements = this.bpmnInstances?.selection?.get();
          const currentBpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;
          this.initMultiInstance(currentBpmnElement);
        });
      },
    },
  },
  methods: {
    initMultiInstance(bpmnElement) {
      if (!bpmnElement) {
        this.multiInstanceType = 'None';
        this.loopCardinality = '';
        this.collection = '';
        this.elementVariable = '';
        this.completionConditionType = 'all';
        this.completionConditionExpression = '';
        this.completionConditionOperator = '==';
        return;
      }
      const businessObject = getBusinessObject(bpmnElement);
      const loopCharacteristics = businessObject.loopCharacteristics;
      const extensionElements = businessObject.extensionElements;

      if (loopCharacteristics) {
        this.multiInstanceType = loopCharacteristics.isSequential ? 'Sequential' : 'Parallel';

        this.loopCardinality = loopCharacteristics.loopCardinality?.body || '';
        this.collection = loopCharacteristics.get(`${this.prefix}:collection`) || '';
        this.elementVariable = loopCharacteristics.get(`${this.prefix}:elementVariable`) || '';

        // Default values
        this.completionConditionType = 'all';
        this.completionConditionExpression = '';
        this.completionConditionOperator = '==';

        // Check for flowable:multiCompletionCondition extension element first
        let multiCompletionConditionExt = null;
        if (extensionElements && extensionElements.values) {
          multiCompletionConditionExt = extensionElements.values.find(e => e.$type === `${this.prefix}:multiCompletionCondition`);
        }

        if (multiCompletionConditionExt) {
          const extType = multiCompletionConditionExt.type;
          const extConditionType = multiCompletionConditionExt.conditionType;
          const extNum = multiCompletionConditionExt.num;

          this.completionConditionOperator = extConditionType || '==';

          if (extType === 'number') {
            this.completionConditionType = 'count';
            this.completionConditionExpression = extNum;
          } else if (extType === 'percentage') {
            this.completionConditionType = 'percentage';
            this.completionConditionExpression = extNum; // Store as raw percentage (e.g., 50 for 50%)
          } else if (extType === 'expression') {
            this.completionConditionType = 'expression';
            if (loopCharacteristics.completionCondition) {
              this.completionConditionExpression = loopCharacteristics.completionCondition.body;
            }
          }
        } else if (loopCharacteristics.completionCondition) {
          // If no extension element, try to infer from loopCharacteristics.completionCondition.body
          const condition = loopCharacteristics.completionCondition.body;

          if (condition === '${nrOfCompletedInstances/nrOfInstances == 1}') {
            this.completionConditionType = 'all';
          } else if (condition.includes('nrOfCompletedInstances/nrOfInstances')) {
            const percentageMatch = condition.match(/\$\{nrOfCompletedInstances\/nrOfInstances\s*(==|>|>=|<|<=)\s*(\d+(\.\d+)?)}\}/);
            if (percentageMatch) {
              this.completionConditionType = 'percentage';
              this.completionConditionOperator = percentageMatch[1];
              this.completionConditionExpression = parseFloat(percentageMatch[2]) * 100; // Convert back to percentage for UI
            } else {
              this.completionConditionType = 'expression';
              this.completionConditionExpression = condition;
            }
          } else if (condition.includes('nrOfCompletedInstances')) {
            const countMatch = condition.match(/\$\{nrOfCompletedInstances\s*(==|>|>=|<|<=)\s*(\d+)}\}/);
            if (countMatch) {
              this.completionConditionType = 'count';
              this.completionConditionOperator = countMatch[1];
              this.completionConditionExpression = parseFloat(countMatch[2]);
            } else {
              this.completionConditionType = 'expression';
              this.completionConditionExpression = condition;
            }
          } else {
            this.completionConditionType = 'expression';
            this.completionConditionExpression = condition;
          }
        }
      } else {
        this.multiInstanceType = 'None';
        this.loopCardinality = '';
        this.collection = '';
        this.elementVariable = '';
        this.completionConditionType = 'all';
        this.completionConditionExpression = '';
        this.completionConditionOperator = '==';
      }
    },
    changeMultiInstanceType() {
      if (this.multiInstanceType === 'None') {
        this.removeMultiInstance();
      } else {
        this.updateElement();
      }
    },
    updateElement() {
      const currentBpmnInstances = window.bpmnInstances;
      // 从 selection service 获取当前选中的元素，确保操作的是活跃且有效的元素
      const selectedElements = currentBpmnInstances?.selection?.get();
      const currentBpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;

      // 关键：在进行任何建模操作之前，确保 BPMN 实例的所有必要部分都可用，并且当前元素是有效的对象
      if (!currentBpmnInstances || !currentBpmnInstances.modeling || !currentBpmnElement || typeof currentBpmnElement !== 'object' || !currentBpmnElement.id) {
        // 如果 BPMN 实例或元素无效，则直接返回，不执行任何建模操作
        return;
      }

      const modeling = currentBpmnInstances.modeling;
      const bpmnFactory = currentBpmnInstances.bpmnFactory;
      const businessObject = getBusinessObject(currentBpmnElement); // 使用最新的元素来获取业务对象

      let loopCharacteristics = businessObject.loopCharacteristics;

      // 移除旧的 loopCharacteristics
      if (loopCharacteristics && this.multiInstanceType === 'None') {
        modeling.updateProperties(currentBpmnElement, { loopCharacteristics: undefined });
        this.initMultiInstance(null); // 重新初始化表单
        return;
      }

      // 创建或更新 loopCharacteristics
      if (!loopCharacteristics) {
        loopCharacteristics = bpmnFactory.create('bpmn:MultiInstanceLoopCharacteristics');
        modeling.updateProperties(currentBpmnElement, { loopCharacteristics: loopCharacteristics });
      }

      // 设置并行或串行
      modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, {
        isSequential: this.multiInstanceType === 'Sequential',
        collection: undefined, // Clear existing Flowable attributes
        elementVariable: undefined, // Clear existing Flowable attributes
      });

      // 设置 Flowable 扩展属性 (collection, elementVariable)
      if (this.collection) {
        modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, {
          [`${this.prefix}:collection`]: this.collection,
        });
      } else {
         modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, { [`${this.prefix}:collection`]: undefined });
      }
      if (this.elementVariable) {
        modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, {
          [`${this.prefix}:elementVariable`]: this.elementVariable,
        });
      } else {
         modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, { [`${this.prefix}:elementVariable`]: undefined });
      }

      // 设置 loopCardinality (基数)
      if (this.loopCardinality) {
        const loopCardinalityElement = bpmnFactory.create('bpmn:FormalExpression', { body: this.loopCardinality });
        modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, { loopCardinality: loopCardinalityElement });
      } else {
        modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, { loopCardinality: undefined });
      }

      // 设置 completionCondition (完成条件)
      let completionConditionElement = undefined;
      let conditionText = '';

      if (this.completionConditionType === 'all') {
        // 当为 'all' 时，completionCondition 不应存在，直接将 completionConditionElement 保持为 undefined
        // 同时确保 conditionText 为空，避免后续创建 FormalExpression
        conditionText = ''; 
      } else if (this.completionConditionType === 'percentage') {
        conditionText = `\${nrOfCompletedInstances/nrOfInstances ${this.completionConditionOperator} ${this.completionConditionExpression ? (this.completionConditionExpression / 100) : 0}}`; 
      } else if (this.completionConditionType === 'count') {
        conditionText = `\${nrOfCompletedInstances ${this.completionConditionOperator} ${this.completionConditionExpression || 0}}`;
      } else if (this.completionConditionType === 'expression') {
        conditionText = this.completionConditionExpression;
      }

      if (conditionText) {
        completionConditionElement = bpmnFactory.create('bpmn:FormalExpression', { body: conditionText });
      }
      modeling.updateModdleProperties(currentBpmnElement, loopCharacteristics, { completionCondition: completionConditionElement });

      // 更新 extensionElements 中的 flowable:multiInstanceVariables 和 flowable:multiCompletionCondition
      let extensionElements = businessObject.extensionElements;
      if (!extensionElements) {
        extensionElements = bpmnFactory.create('bpmn:ExtensionElements');
        modeling.updateProperties(currentBpmnElement, { extensionElements: extensionElements });
      }

      // 清除旧的 flowable:multiInstanceVariables 和 flowable:multiCompletionCondition
      extensionElements.values = extensionElements.values?.filter(
        (e) => e.$type !== `${this.prefix}:multiInstanceVariables` && e.$type !== `${this.prefix}:multiCompletionCondition`
      ) || [];

      // 添加 flowable:multiInstanceVariables (注意：这里直接设置 body 为 '[]' 来匹配目标XML格式)
      const multiInstanceVariables = bpmnFactory.create(`${this.prefix}:multiInstanceVariables`, { body: '[]' });
      extensionElements.values.push(multiInstanceVariables);

      // 添加 flowable:multiCompletionCondition
      if (this.multiInstanceType !== 'None') {
        const conditionProps = {};
        if (this.completionConditionType === 'all') {
          conditionProps.type = 'all';
          conditionProps.conditionType = '=';
        } else if (this.completionConditionType === 'count') {
          conditionProps.type = 'number'; 
          conditionProps.conditionType = this.completionConditionOperator;
          conditionProps.num = this.completionConditionExpression;
        } else if (this.completionConditionType === 'percentage') {
          conditionProps.type = 'percentage'; 
          conditionProps.conditionType = this.completionConditionOperator;
          conditionProps.num = this.completionConditionExpression;
        } else if (this.completionConditionType === 'expression') {
          conditionProps.type = 'expression';
          conditionProps.conditionType = this.completionConditionOperator; 
        }

        const multiCompletionCondition = bpmnFactory.create(`${this.prefix}:multiCompletionCondition`, conditionProps);
        extensionElements.values.push(multiCompletionCondition);
      }

      modeling.updateProperties(currentBpmnElement, { extensionElements: extensionElements });
    },
    removeMultiInstance() {
      const currentBpmnInstances = window.bpmnInstances;
      // 从 selection service 获取当前选中的元素，确保操作的是活跃且有效的元素
      const selectedElements = currentBpmnInstances?.selection?.get();
      const currentBpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;

      // 关键：在进行任何建模操作之前，确保 BPMN 实例的所有必要部分都可用，并且当前元素是有效的对象
      if (!currentBpmnInstances || !currentBpmnInstances.modeling || !currentBpmnElement || typeof currentBpmnElement !== 'object' || !currentBpmnElement.id) {
        // 如果 BPMN 实例或元素无效，则直接返回
        return;
      }

      const modeling = currentBpmnInstances.modeling;
      const businessObject = getBusinessObject(currentBpmnElement);

      // 移除 loopCharacteristics
      modeling.updateProperties(currentBpmnElement, { loopCharacteristics: undefined });

      // 移除 extensionElements 中的相关部分
      let extensionElements = businessObject.extensionElements;
      if (extensionElements) {
        extensionElements.values = extensionElements.values?.filter(
          (e) => e.$type !== `${this.prefix}:multiInstanceVariables` && e.$type !== `${this.prefix}:multiCompletionCondition`
        ) || [];
        modeling.updateProperties(currentBpmnElement, { extensionElements: extensionElements.values.length > 0 ? extensionElements : undefined });
      }
      this.initMultiInstance(null); // 重新初始化表单
    },
  },
};
</script>

<style lang="scss" scoped>
.panel-tab__content {
  @apply p-4;
}
.el-form-item {
  margin-bottom: 12px;
}
.el-radio-group {
  @apply w-full;
}
.el-radio {
  @apply mr-4;
}
</style>
