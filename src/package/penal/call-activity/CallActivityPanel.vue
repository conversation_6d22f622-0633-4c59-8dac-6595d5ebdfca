<template>
  <div class="panel-tab__content">
    <el-form label-width="120px" size="small" :disabled="isFormDisabled">
      <el-form-item label="子流程ID">
        <el-input v-model="calledElement" placeholder="请输入子流程ID" />
      </el-form-item>

      <el-divider>输入参数映射</el-divider>
      <el-table :data="inputMappings" size="small" style="margin-bottom: 10px;">
        <el-table-column prop="source" label="源变量" />
        <el-table-column prop="target" label="目标变量" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button @click="openMappingDrawer('in', scope.$index)" type="text" link size="small">编辑</el-button>
            <el-button @click="removeMapping('in', scope.$index)" type="text" link size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button @click="openMappingDrawer('in')" type="primary" plain>添加输入映射</el-button>

      <el-divider>输出参数映射</el-divider>
      <el-table :data="outputMappings" size="small" style="margin-bottom: 10px;">
        <el-table-column prop="source" label="源变量" />
        <el-table-column prop="target" label="目标变量" />
        <el-table-column label="操作" width="120">
          <template #default="scope">
            <el-button @click="openMappingDrawer('out', scope.$index)" type="text" link size="small">编辑</el-button>
            <el-button @click="removeMapping('out', scope.$index)" type="text" link size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-button @click="openMappingDrawer('out')" type="primary" plain>添加输出映射</el-button>
    </el-form>

    <el-drawer
      v-model="drawerVisible"
      :title="drawerType === 'in' ? (editingIndex === -1 ? '添加输入映射' : '编辑输入映射') : (editingIndex === -1 ? '添加输出映射' : '编辑输出映射')"
      size="400px"
      :with-header="true"
      class="custom-mapping-drawer"
      :show-footer="true"
    >
      <el-form
        label-position="left"
        label-width="90px"
        class="px-6 pt-4 pb-2"
        size="small"
      >
        <el-form-item
          label="源变量"
          :required="true"
          class="mb-4"
        >
          <el-input v-model="mappingForm.source" placeholder="请输入源变量" class="w-full max-w-xs" clearable />
        </el-form-item>
        <el-form-item
          label="目标变量"
          :required="true"
          class="mb-4"
        >
          <el-input v-model="mappingForm.target" placeholder="请输入目标变量" class="w-full max-w-xs" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="element-drawer__button" style="padding: 12px 24px; border-top: 1px solid #f0f0f0; background: #fff;">
          <el-button size="small" @click="drawerVisible = false">取消</el-button>
          <el-button size="small" type="primary" @click="confirmAddMapping">保存</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script>
import { getBusinessObject } from 'bpmn-js/lib/util/ModelUtil';

export default {
  props: {
    id: String,
    type: String,
    scene: String
  },
  data() {
    return {
      bpmnElement: null,
      calledElement: '',
      inputMappings: [],
      outputMappings: [],
      drawerVisible: false,
      drawerType: '',
      mappingForm: { source: '', target: '' },
      editingIndex: -1,
    }
  },
  computed: {
    isFormDisabled() {
      return !!(this.scene === 'DETAIL')
    }
  },
  inject: ['prefix'],
  watch: {
    id: {
      immediate: true,
      handler() {
        this.$nextTick(() => {
          this.initCalledElementAndMappings();
        });
      },
    },
    calledElement: {
      handler() {
        this.saveConfig();
      }
    },
    inputMappings: {
      handler() {
        this.saveConfig();
      },
      deep: true
    },
    outputMappings: {
      handler() {
        this.saveConfig();
      },
      deep: true
    },
  },
  methods: {
    initCalledElementAndMappings() {
      const currentBpmnInstances = window.bpmnInstances;
      const selectedElements = currentBpmnInstances?.selection?.get();
      this.bpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;

      if (!this.bpmnElement) {
        this.calledElement = '';
        this.inputMappings = [];
        this.outputMappings = [];
        return;
      }

      const businessObject = getBusinessObject(this.bpmnElement);

      this.calledElement = businessObject.calledElement || '';

      this.inputMappings = [];
      this.outputMappings = [];

      const extensions = businessObject.extensionElements?.values || [];
      extensions.forEach(ext => {
        if (ext.$type === `${this.prefix}:In`) {
          this.inputMappings.push({ source: ext.source, target: ext.target });
        } else if (ext.$type === `${this.prefix}:Out`) {
          this.outputMappings.push({ source: ext.source, target: ext.target });
        }
      });
    },
    openMappingDrawer(type, index = -1) {
      this.drawerType = type;
      this.editingIndex = index;

      if (index !== -1) {
        const mappings = type === 'in' ? this.inputMappings : this.outputMappings;
        this.mappingForm = { ...mappings[index] };
      } else {
        this.mappingForm = { source: '', target: '' };
      }
      this.drawerVisible = true;
    },
    confirmAddMapping() {
      if (this.mappingForm.source && this.mappingForm.target) {
        if (this.editingIndex !== -1) {
          if (this.drawerType === 'in') {
            this.inputMappings.splice(this.editingIndex, 1, { ...this.mappingForm });
          } else {
            this.outputMappings.splice(this.editingIndex, 1, { ...this.mappingForm });
          }
        } else {
          if (this.drawerType === 'in') {
            this.inputMappings.push({ ...this.mappingForm });
          } else {
            this.outputMappings.push({ ...this.mappingForm });
          }
        }
        this.drawerVisible = false;
      } else {
        this.$message.warning('请填写完整的源变量和目标变量');
      }
    },
    removeMapping(type, index) {
      if (type === 'in') {
        this.inputMappings.splice(index, 1);
      } else {
        this.outputMappings.splice(index, 1);
      }
    },
    saveConfig() {
      const currentBpmnInstances = window.bpmnInstances;
      const selectedElements = currentBpmnInstances?.selection?.get();
      const currentBpmnElement = selectedElements?.length > 0 ? selectedElements[0] : null;

      if (!currentBpmnElement) return;

      const prefix = this.prefix || 'flowable';
      const modeling = currentBpmnInstances.modeling;
      const bpmnFactory = currentBpmnInstances.bpmnFactory;

      modeling.updateProperties(currentBpmnElement, { calledElement: this.calledElement });

      let businessObject = getBusinessObject(currentBpmnElement);

      const newExtensionsValues = [];
      if (businessObject.extensionElements && businessObject.extensionElements.values) {
        businessObject.extensionElements.values.forEach(ext => {
          if (ext.$type !== `${prefix}:In` && ext.$type !== `${prefix}:Out`) {
            const recreatedExt = bpmnFactory.create(ext.$type, { ...ext });
            newExtensionsValues.push(recreatedExt);
          }
        });
      }

      this.inputMappings.forEach(mapping => {
        if (mapping.source && mapping.target) {
          const inMapping = bpmnFactory.create(`${prefix}:In`, {
            source: mapping.source,
            target: mapping.target
          });
          newExtensionsValues.push(inMapping);
        }
      });

      this.outputMappings.forEach(mapping => {
        if (mapping.source && mapping.target) {
          const outMapping = bpmnFactory.create(`${prefix}:Out`, {
            source: mapping.source,
            target: mapping.target
          });
          newExtensionsValues.push(outMapping);
        }
      });

      if (newExtensionsValues.length > 0) {
        if (businessObject.extensionElements) {
          modeling.updateModdleProperties(currentBpmnElement, businessObject.extensionElements, { values: newExtensionsValues });
        } else {
          const newExtElements = bpmnFactory.create('bpmn:ExtensionElements', { values: newExtensionsValues });
          modeling.updateProperties(currentBpmnElement, { extensionElements: newExtElements });
        }
      } else if (businessObject.extensionElements) {
        modeling.updateProperties(currentBpmnElement, { extensionElements: undefined });
      }
    }
  },
};
</script>

<style scoped>
.mapping-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px dashed #eee;
  border-radius: 4px;
}

/* 抽屉标题样式 */
.custom-mapping-drawer >>> .el-drawer__header {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  padding-bottom: 8px;
}

/* 按钮区样式 */
.element-drawer__button {
  text-align: right;
  padding: 8px 0;
}
</style>
