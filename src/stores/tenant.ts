import {defineStore} from 'pinia';
import Cookies from 'js-cookie';
import { Local, Session } from '/@/utils/storage';
import { getTenantTree } from '/@/api/admin/tenant';


// 定义租户接口
interface Tenant {
	id: string;
	name: string;
	tenantDomain?: string;
	websiteName?: string;
	footer?: string;
	background?: string;
	miniQr?: string;
}
/**
 * 创建并导出字典存储的 Vue3 store 对象
 * @function
 * @returns {DictionaryStore} - 返回创建的字典存储对象
 */
export const useTenant = defineStore('tenant', {
    state: () => ({
        tree: [] as any[],
        tenantId: Local.get('tenantId'),
        flatTree: [] as any[],
    }),
    actions: {
        async getTenantTree(obj){
          if (!obj) {
            obj = {
              parentId: Session.getTenant(),
            }
          }
          const res = await getTenantTree(obj);
          this.tree = res.data;
          this.flatTree = this.flattenTree(res.data);
        },
        // 扁平化数组
        flattenTree(tree: any[], result: any[] = []): any[] {
          tree.forEach((node) => {
            result.push(node);
            if (node.children && node.children.length > 0) {
              this.flattenTree(node.children, result);
            }
          });
          return result;
        },
        // changeTenant(tenant: Tenant) {
        //   Session.set('tenantId', tenant.id);
        //   Local.set('tenantId', tenant.id);
        //   Cookies.set('tenantId', tenant.id);
        //   this.tenantId = tenant.id;
        //   window.location.reload();
        // }
    },
});
