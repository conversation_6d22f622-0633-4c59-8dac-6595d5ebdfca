import { defineStore } from 'pinia'

interface ContractState {
  shouldResetForm: boolean
}

export const useContractStore = defineStore('contract', {
  state: (): ContractState => ({
    shouldResetForm: false,
  }),

  actions: {
    setShouldResetForm(value: boolean) {
      this.shouldResetForm = value
    },

    getAndClearResetForm(): boolean {
      const shouldReset = this.shouldResetForm
      this.shouldResetForm = false
      return shouldReset
    },
  },
}) 