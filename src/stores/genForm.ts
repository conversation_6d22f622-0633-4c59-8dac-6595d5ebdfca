/*
 * @Author: chenting<PERSON> <EMAIL>
 * @Date: 2025-05-22 09:44:26
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-05-23 10:24:36
 * @FilePath: \fe-dcrg-admin\src\stores\genForm.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';

/**
 * 创建并导出生成表单存储的 Vue3 store 对象
 * @function
 * @returns {GenFormStore} - 返回生成表单存储对象
 */
export const useGenForm = defineStore('genForm', {
  state: () => ({
    menuInfo: sessionStorage.getItem('menuInfo') ? JSON.parse(sessionStorage.getItem('menuInfo') as string) : {} as Record<string, any>,
  }),
  actions: {
    setMenuInfo(menuInfo: any[]) {
      this.menuInfo = menuInfo;
    },
    getMenuInfo() {
      return this.menuInfo;
    },
  },
});
