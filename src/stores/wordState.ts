import { defineStore } from 'pinia';
import {
	TemplateData,
	WordDocumentState,
	WordTemplate,
} from '../views/procurementSourcing/biddingProcess/components/announcement/ProcurementAnnouncement/types';

export const useWordState = defineStore('wordState', {
	state: () => ({
		wordState: {
			templates: [],
			currentTemplate: null,
			templateData: {},
			generatedDocuments: [],
			isGenerating: false,
			editorContent: '',
			loading: false,
			previewContent: '',
			isDownloading: false,
		} as WordDocumentState,
	}),
	actions: {
		setTemplates(templates: WordTemplate[]) {
			this.wordState.templates = templates;
		},
		setCurrentTemplate(template: WordTemplate) {
			this.wordState.currentTemplate = template;
		},
		setTemplateData(data: TemplateData) {
			this.wordState.templateData = data;
		},
		handleTemplateUploaded(template: WordTemplate) {
			this.wordState.templates.push(template);
			if (!this.wordState.currentTemplate) {
				this.handleTemplateSelected(template);
			}
		},
		handleTemplateUpdated(template: WordTemplate) {
			const index = this.wordState.templates.findIndex((t) => t.id === template.id);
			if (index !== -1) {
				this.wordState.templates[index] = template;
				if (this.wordState.currentTemplate?.id === template.id) {
					this.wordState.currentTemplate = template;
				}
			}
		},
		handleTemplateDeleted(templateId: string) {
			this.wordState.templates = this.wordState.templates.filter((t) => t.id !== templateId);
			if (this.wordState.currentTemplate?.id === templateId) {
				this.wordState.currentTemplate = null;
				this.wordState.templateData = {};
				this.wordState.editorContent = '';
			}
		},
		handleTemplateSelected(template: WordTemplate) {
			this.wordState.currentTemplate = template;
			this.initializeTemplateData();
		},
		initializeTemplateData() {
			if (!this.wordState.currentTemplate) return;

			const data: TemplateData = {};
			this.wordState.currentTemplate.variables.forEach((variable) => {
				if (variable.defaultValue !== undefined) {
					data[variable.key] = variable.defaultValue;
				} else {
					switch (variable.type) {
						case 'array':
							// 根据不同的数组类型初始化不同的数据结构
							if (variable.key === 'qualificationRequirements') {
								data[variable.key] = [];
							} else if (variable.key === 'quotationRequirements') {
								data[variable.key] = [];
							} else {
								data[variable.key] = [];
							}
							break;
						case 'number':
							data[variable.key] = 0;
							break;
						default:
							data[variable.key] = '';
					}
				}
			});

			this.wordState.templateData = data;
		},
	},
});
