/*
 * @Author: chenting<PERSON> <EMAIL>
 * @Date: 2025-06-11 09:52:17
 * @LastEditors: chentinghong <EMAIL>
 * @LastEditTime: 2025-06-13 16:05:49
 * @FilePath: \fe-saas-monomer-admin\src\stores\aiChat.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia';
import { Style } from '/@/hooks/drag';

interface chatResult {
	字段名称: string;
	字段描述: string;
	字段类型: string;
	是否必填: string;
}

interface DataFormResult {
	name: string;
	description: string;
	dataType: string;
	isRequired: boolean;
	modify?: boolean;
}

export const useAiChat = defineStore('aiChat', {
	state: () => ({
		loading: false,
		isCollapse: false,
		width: 440,
		chartStyle: undefined as Style | undefined,
		conversationId: undefined as string | number | undefined,
		aiDataForm: {
			chatId: '' as string, // 会话ID
			tableName: '' as string, // 表名称
			result: [] as DataFormResult[], // 解析结果
		},
	}),
	actions: {
		resetAiChatTable() {
			this.aiDataForm.tableName = '';
			this.aiDataForm.result = [];
		},
		updateAiDataFormResult(result: any[]) {
			this.aiDataForm.result = result.map((item: chatResult) => {
				return {
					name: item['字段名称'],
					description: item['字段描述'],
					dataType: item['字段类型'],
					isRequired: item['是否必填'] === '是',
					modify: true,
				};
			});
		},
		updateAiDataFormTableName(title: string) {
			this.aiDataForm.tableName = title;
		},
		setChatId(id: string) {
			this.aiDataForm.chatId = id;
		},
		setLoading(val: boolean) {
			this.loading = val;
		},
		toggleCollapse(val = false) {
			this.isCollapse = val;
		},
		setWidth(width: number) {
			this.width = width;
		},
		setChartStyle(style: Style) {
			this.chartStyle = style;
		},
		setConversationId(id?: string | number) {
			this.conversationId = id;
		},
	},
	persist: {
		enabled: true,
		strategies: [
			{
				storage: sessionStorage,
			},
		],
	},
});
