<template>
  <div
    v-if="tableData.length > 1"
    class="award-result-container"
  >
    <!-- 当数据长度大于1时显示表格 -->
    <el-table
      :data="tableData"
      v-loading="loading"
      stripe
      class="editable-table"
    >
      <el-table-column
        label="序号"
        type="index"
        width="60"
        align="center"
      />

      <el-table-column
        label="项目名称"
        prop="projectName"
        min-width="150"
        show-overflow-tooltip
      />

      <el-table-column
        label="标段名称"
        prop="sectionName"
        min-width="120"
        show-overflow-tooltip
      >
      </el-table-column>

      <el-table-column
        label="定标状态"
        prop="awardReportStatus"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <el-tag
            :type="getStatusTagType(row.awardReportStatus)"
            size="small"
          >
            {{ getStatusText(row.awardReportStatus) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column
        label="审批状态"
        prop="awardReportStatus"
        width="100"
        align="center"
      >
        <template #default="{ row }">
          <span>{{ getApprovalStatus(row.awardReportStatus) }}</span>
        </template>
      </el-table-column>

      <el-table-column
        label="定标人"
        prop="userName"
        width="200"
        align="center"
      />

      <el-table-column
        label="定标时间"
        prop="awardReportTime"
        width="180"
        align="center"
      />

      <el-table-column
        label="操作"
        width="180"
        align="center"
        fixed="right"
      >
        <template #default="{ row }">
          <el-button
            v-if="!row.awardReportStatus && isProjectLeader"
            type="text"
            @click="handleEdit(row)"
          >
            定标
          </el-button>

          <el-button
            v-else-if="['APPROVE_REVOKE', 'APPROVE_REJECT'].includes(row.awardReportStatus) && isProjectLeader"
            type="text"
            @click="handleEdit(row)"
          >
            编辑
          </el-button>

          <el-button
            v-else
            type="text"
            @click="handleView(row)"
          >
            查看
          </el-button>
          <el-button
            type="text"
            v-if="['TO_APPROVE', 'APPROVING'].includes(row.awardReportStatus) && isProjectLeader"
            @click="handleRevoke(row)"
          >
            撤销审批
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 抽屉 - 仅在数据长度大于1时显示 -->
    <el-drawer
      v-model="drawerVisible"
      :title="drawerTitle"
      size="80%"
      direction="rtl"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <template #default>
        <!-- 竞谈项目使用专用组件 -->
        <NegotiationDetail
          v-if="drawerVisible && isNegotiationProject"
          :sectionId="sectionId"
          :isChangeMode="false"
          :isViewMode="false"
          @on-success="handleSuccess"
        />
        <!-- 普通招标项目使用原组件 -->
        <AwardResultDetail
          v-else-if="drawerVisible"
          :sectionId="sectionId"
          :isChangeMode="false"
          :isViewMode="false"
          @on-success="handleSuccess"
        />
      </template>
    </el-drawer>
  </div>

  <!-- 当数据长度等于1时，直接显示抽屉内容 -->
  <div
    v-else-if="tableData.length === 1"
    class="single-section-content"
  >
    <!-- 竞谈项目使用专用组件 -->
    <NegotiationDetail
      v-if="isNegotiationProject"
      :sectionId="selectedSectionId"
      :isChangeMode="false"
      :isViewMode="false"
      @on-success="handleSuccess"
    />
    <!-- 普通招标项目使用原组件 -->
    <AwardResultDetail
      v-else
      :sectionId="selectedSectionId"
      :isChangeMode="false"
      :isViewMode="false"
      @on-success="handleSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import AwardResultDetail from './detail.vue';
import NegotiationDetail from './negotiationDetail.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getTenderBidList } from '../../../api/award';
import { ElMessage, ElMessageBox } from 'yun-design';
import { cancelApprovePlan } from '@/api/purchasing/plan';

interface TenderBidItem {
  projectCode: string;
  projectName: string;
  sectionId: number;
  awardReportStatus: string;
  userId: number;
  awardReportTime: string;
  awardReportContent: string;
  traceId: string;
}

const biddingStore = useBiddingStore();
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);
const isProjectLeader = computed(() => biddingStore?.isProjectLeader);

// 判断是否为竞争性采购项目（JZTP 或 ZB）
const isNegotiationProject = computed(() => {
  return biddingStore?.isCompetitiveBidding;
});

// 表格数据
const tableData = ref<TenderBidItem[]>([]);
const loading = ref(false);

// 抽屉相关
const drawerVisible = ref(false);
const drawerTitle = ref('');
const sectionId = ref<number | undefined>(undefined);

// 选中标段ID - 用于单标段模式
const selectedSectionId = computed(() => {
  if (tableData.value.length === 1) {
    return tableData.value[0].sectionId;
  }
  return undefined;
});

// 获取列表数据
const fetchTableData = async () => {
  if (!projectId.value || !noticeId.value) {
    return;
  }

  try {
    loading.value = true;
    const response = await getTenderBidList({
      projectId: projectId.value,
      noticeId: noticeId.value,
    });

    if (response.code === 0) {
      tableData.value = response.data || [];
    } else {
      ElMessage.error(response.msg || '获取数据失败');
    }
  } catch (error) {
    console.error('获取定标结果列表失败:', error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

// 状态标签类型
const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    TO_APPROVE: 'warning',
    APPROVING: 'primary',
    APPROVE: 'success',
    APPROVE_REJECT: 'danger',
    APPROVE_REVOKE: 'info',
  };
  return statusMap[status] || 'info';
};

// 状态文本
const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    TO_APPROVE: '待审批',
    APPROVING: '审批中',
    APPROVE: '审批通过',
    APPROVE_REJECT: '审批驳回',
    APPROVE_REVOKE: '审批撤销',
  };
  return statusMap[status] || '待定标';
};

// 审批状态
const getApprovalStatus = (status: string) => {
  const approvalMap: Record<string, string> = {
    TO_APPROVE: '待审批',
    APPROVING: '审批中',
    APPROVE: '审批通过',
    APPROVE_REJECT: '审批驳回',
    APPROVE_REVOKE: '审批撤销',
  };
  return approvalMap[status] || '-';
};

// 查看操作
const handleView = (row: TenderBidItem) => {
  drawerTitle.value = '查看定标结果';
  drawerVisible.value = true;
  sectionId.value = row.sectionId;
};

// 编辑操作
const handleEdit = (row: TenderBidItem) => {
  drawerTitle.value = '编辑定标结果';
  drawerVisible.value = true;
  sectionId.value = row.sectionId;
};

const handleRevoke = async (row: any) => {
  try {
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApprovePlan({ bizType: 'SRM_TENDER_BID_AUDIT', bizKey: `${projectId.value}#${noticeId.value}#${row.sectionId}` }).then(() => {
          fetchTableData();
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
        });
      })
      .catch(() => {});
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销审批失败:', error);
    }
  }
};

const handleSuccess = () => {
  drawerVisible.value = false;
  fetchTableData();
};

onMounted(() => {
  fetchTableData();
});
</script>

<style scoped lang="scss">
@import '../../../styles/collapse-panel.scss';
.award-result-container {
  background-color: #fff;
  min-height: 100%;
  display: flex;
  flex-direction: column;
  border-radius: 4px;
  padding: 16px;
}

.single-section-content {
  flex: 1;
  min-height: 100%;
}

.award-table {
  width: 100%;

  :deep(.el-table__header-wrapper) {
    background-color: #f5f7fa;
  }

  :deep(.el-table__row) {
    &:hover {
      background-color: #f5f7fa;
    }
  }
}

:deep(.el-drawer__header) {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-drawer__body) {
  padding: 0 20px 20px;
}
</style>
