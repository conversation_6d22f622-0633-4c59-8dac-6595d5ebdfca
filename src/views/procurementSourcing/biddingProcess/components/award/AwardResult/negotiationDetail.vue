<template>
  <div class="award-result-container">
    <!-- 定标基本信息 - 只有在有定标详情时才显示 -->
    <div
      v-if="isEditMode"
      class="award-info-section"
    >
      <div class="award-info-content">
        <div class="info-item">
          <div class="info-label">定标人</div>
          <div class="info-value">{{ awardInfo.awardPerson }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">定标时间</div>
          <div class="info-value">{{ awardInfo.awardTime }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">审批状态</div>
          <div class="info-value">
            <el-tag
              :type="getApprovalStatusType(approvalStatus)"
              size="small"
            >
              {{ getApprovalStatusText(approvalStatus) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 定标清单 -->
    <div class="award-list-section">
      <div class="section-header">
        <div class="header-title">定标清单</div>
      </div>

      <StageTabs
        v-if="!props.sectionId"
        v-model="activeTabIndex"
        @update:sectionId="handleSectionChange"
      />

      <div class="flex justify-between items-center my-4">
        <div class="flex items-center gap-4">
          <div class="font-[12px]">
            已发起
            <span class="award-text-primary">{{ roundNum }}</span>
            轮报价
          </div>
        </div>
      </div>

      <!-- 竞谈项目定标清单表格 -->
      <el-table
        :data="filteredNegotiationList"
        style="width: 100%"
        class="editable-table award-container"
        v-loading="loading"
        size="small"
      >
        <el-table-column
          label="序号"
          type="index"
          width="80"
          fixed="left"
        />
        <el-table-column
          label="供应商名称"
          prop="supplierName"
          min-width="200"
          show-overflow-tooltip
        />
        <el-table-column
          label="联系人"
          prop="contactPerson"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="联系电话"
          prop="contactPhone"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="报价轮次"
          prop="quoteRoundCount"
          min-width="100"
        />
        <el-table-column
          label="报价总价"
          prop="totalQuoteAmount"
          min-width="140"
        >
          <template #default="{ row }">
            <span>{{ formatPrice(row.totalQuoteAmount || 0) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="报价IP"
          prop="quoteIp"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column width="180" label="中标候选人顺序" prop="winnerCandidateOrder"></el-table-column>
        <el-table-column
          label="是否中标"
          prop="awarded"
          width="100"
          fixed="right"
        >
          <template #default="{ row }">
            <el-checkbox
              :model-value="row.awarded === 1"
              @update:model-value="updateAwardStatus(row, $event)"
              :disabled="isEditMode"
            />
          </template>
        </el-table-column>

      </el-table>
    </div>

    <!-- 定标公告 -->
    <div class="award-announcement-section">
      <div class="section-header">
        <div class="header-title">定标公告</div>
      </div>

      <el-form
        ref="announcementFormRef"
        :model="announcementForm"
        :rules="announcementRules"
        label-width="120px"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item
              label="引用模版"
              prop="template"
            >
              <el-select
                v-model="announcementForm.template"
                placeholder="请选择公告模版"
                @change="handleTemplateChange"
              >
                <el-option
                  v-for="template in templateOptions"
                  :key="template.id"
                  :label="template.templateName"
                  :value="template.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item
          v-if="isEditMode"
          label="公告内容"
        >
          <el-button
            type="text"
            @click="handleEditContent"
            class="edit-button"
          >
            <el-icon class="mr-[6px]"><edit-pen /></el-icon>
            查看
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 定标附件 -->
    <div class="award-attachments-section">
      <div class="section-header">
        <div class="header-title">定标附件</div>
      </div>

      <div class="attachment-form">
        <el-form label-width="120px">
          <el-row>
            <el-col
              v-if="!isEditMode"
              :span="12"
            >
              <el-form-item label="定标附件">
                <!-- 编辑模式：显示上传组件 -->
                <div class="upload-wrapper">
                  <YunUpload
                    v-model="awardFileList"
                    @change="handelUploadFile"
                    :limit="5"
                    :multiple="true"
                  ></YunUpload>
                </div>
              </el-form-item>
            </el-col>

            <el-col
              v-if="isEditMode"
              :span="12"
            >
              <div class="flex gap-4 mt-3">
                <div class="file-label">定标结果附件：</div>
                <div
                  class="flex flex-col gap-2"
                  v-if="awardFileList.length > 0"
                >
                  <div
                    class="flex gap-4 items-center"
                    v-for="(item, index) in awardFileList"
                    :key="index"
                  >
                    <div
                      class="file-item-label"
                      @click="handleDownload(item)"
                    >
                      {{ item.fileName || item.name }}
                    </div>
                  </div>
                </div>
                <div
                  v-else
                  class="no-files"
                >
                  <span>暂无附件</span>
                </div>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 定标备注 -->
    <div class="award-remark-section">
      <div class="section-header">
        <div class="header-title">定标备注</div>
      </div>

      <el-row>
        <el-col :span="12">
          <el-form label-width="120px">
            <el-form-item label="定标备注">
              <el-input
                v-model="remarkInfo.remark"
                type="textarea"
                :rows="5"
                placeholder="请输入定标说明"
                show-word-limit
              />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </div>

  <div
    v-if="!isEditMode && !props.isChangeMode"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        :loading="saving"
        @click="handleSubmit"
      >
        {{ '生成定标公告' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>

  <div
    v-if="currentAward?.awardReportStatus === 'APPROVING' && !props.isChangeMode"
    class="form-actions-wrapper"
  >
    <div class="form-actions">
      <el-button
        type="primary"
        @click="handleRevoke"
      >
        {{ '撤销审批' }}
      </el-button>
      <el-button @click="handleCancel">取消</el-button>
    </div>
  </div>


  <!-- 公告内容编辑抽屉 -->
  <AnnouncementEditor
    ref="announcementEditorRef"
    :readonly="isEditMode"
    @save="handleSaveContent"
  />
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue';
import { debounce } from 'lodash-es';
import { ElMessage, ElMessageBox } from 'yun-design';
import Decimal from 'decimal.js';

// 高精度计算工具函数
const precisionMath = {
  // 乘法
  multiply: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).mul(new Decimal(b || 0)).toNumber();
  },
  // 加法
  add: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).add(new Decimal(b || 0)).toNumber();
  },
  // 减法
  subtract: (a: number | string, b: number | string): number => {
    return new Decimal(a || 0).sub(new Decimal(b || 0)).toNumber();
  },
  // 除法
  divide: (a: number | string, b: number | string): number => {
    if (Number(b) === 0) return 0;
    return new Decimal(a || 0).div(new Decimal(b)).toNumber();
  },
  // 保留指定位数小数
  toFixed: (value: number | string, decimals: number = 2): string => {
    return new Decimal(value || 0).toFixed(decimals);
  },
};

import StageTabs from '@/views/procurementSourcing/biddingProcess/components/bidding/StageTabs.vue';
import AnnouncementEditor from './components/AnnouncementEditor.vue';
import YunUpload from '@/components/YunUpload/index.vue';
import { getTemplateList, submitAwardReview } from '../../../api/award';
import { getTemplateDetail } from '../../../api/announcement';
import { templateContentFormatter } from '../../announcement/ProcurementDocument/templateFormatter';
import { batchAwardNotice, type BatchAwardNoticeItem } from '../../../api/WinnerNotification';
import type {
  TemplateOption,
  SubmitAwardReviewData,
  TenderBidPublicityDetail,
} from '../../../types/award';
import { cancelApprovePlan } from '@/api/purchasing/plan';

// 中标通知书接口
interface AwardNoticeItem {
  /** 必须 - 公告ID */
  noticeId: number;
  /** 必须 - 采购立项ID */
  projectId: number;
  /** 必须 - 标段ID */
  sectionId: number;
  /** 必须 - 评价结果ID */
  evaluationResultId: number;
  /** 必须 - 模板ID */
  templateId: number;
  /** 必须 - 租户供应商ID */
  tenantSupplierId: number;
  /** 必须 - 供应商名称 */
  supplierName: string;
  /** 必须 - 通知书内容 */
  noticeContent: string;
}

// 投标供应商接口
interface BidSupplierItem {
  /** 必须 - 租户供应商ID */
  tenantSupplierId: number;
  /** 非必须 - 供应商名称 */
  supplierName?: string;
  /** 必须 - 是否中标 */
  isBid: boolean;
  /** 非必须 - 中标金额（供应商对该标段所有物料的总报价金额） */
  bidAmount?: number;
  /** 非必须 - 定标备注 */
  awardRemark?: string;
  /** 非必须 - 支付方式ID */
  projectPaymentId?: number;
  /** 非必须 - 审批备注 */
  examineRemark?: string;
  /** 非必须 - 定标报告内容 */
  awardReportContent?: string;
  /** 非必须 - 定标报告时间 */
  awardReportTime?: string; // 格式: YYYY-MM-DD HH:mm:ss
  /** 非必须 - 定标报告模板ID */
  awardTemplateId?: number;
}
import downloadUrlFile from '@/utils/downloadUrl.js';
import moment from 'moment';
import { useBiddingStore, useAwardStore } from '@/views/procurementSourcing/biddingProcess/stores';
import { getNoticeChangeDetailInfo } from '@/api/purchasing/noticeChange';
import { queryQuoteListBySupplier } from '@/api/purchasing/bid';

// 定义组件 Props
interface Props {
  isChangeMode?: boolean; // 是否为变更模式
  isViewMode?: boolean; // 是否为查看模式
  sectionId?: number; // 标段ID，当传入时会隐藏StageTabs
}

const props = withDefaults(defineProps<Props>(), {
  isChangeMode: false,
  isViewMode: false,
  sectionId: undefined,
});

const emit = defineEmits<{
  (e: 'on-success'): void;
  (e: 'change-submit', data: { content: string; submitData: any }): void;
}>();

const biddingStore = useBiddingStore();
const awardStore = useAwardStore();

const projectDetail = computed(() => biddingStore?.projectDetail);

const noticeInfo = computed(() => {
  return biddingStore?.noticeInfo;
});
const noticeId = computed(() => biddingStore?.noticeId);
const projectId = computed(() => biddingStore?.projectId);

const roundNum = computed(() => {
  const sectionId = projectInfo.sectionId;
  return noticeInfo.value?.quoteRoundVoList?.find((item: any) => item.sectionId === sectionId)?.currentQuoteRound;
});

// 保存状态
const saving = ref(false);


const approvalStatus = computed(() => {
  return awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId)?.awardReportStatus || '';
});

const currentAward = computed(() => {
  return awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId);
});

// 页面状态：编辑模式还是新增模式 - 从 store 获取
const isEditMode = computed(() => {
  if (props.isViewMode) {
    return false;
  }

  const target = awardStore.tenderBidDetail?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId);

  if (!target?.awardReportContent) {
    return false;
  }

  if (!['APPROVE_REJECT', 'APPROVE_REVOKE'].includes(target.awardReportStatus) && !props.isChangeMode) {
    return true;
  }

  return false;
});

// 抽屉组件引用
const announcementEditorRef = ref();

// 表单引用
const announcementFormRef = ref();

// 模拟项目数据（实际应从路由参数或store获取）
const projectInfo = reactive({
  noticeId: noticeId.value, // 公告ID
  projectId: projectId.value, // 采购立项ID
  sectionId: props.sectionId || '', // 当前标段ID
});

// 定标基本信息
const awardInfo = reactive({
  awardPerson: projectDetail.value?.createBy || '',
  awardTime: '',
  projectName: projectDetail.value?.projectName || '',
  projectCode: projectDetail.value?.projectCode || '',
  procurementMethod: projectDetail.value?.procurementMethod || '',
  budgetAmount: projectDetail.value?.budgetAmount || 0,
});

const activeTabIndex = ref(0);

// 竞谈项目的供应商数据
const negotiationTableData = ref([]);
const loading = ref(false);

// 查看模式下的项目物料列表（用于获取中标数据）
const viewModeProjectItemList = ref<any[]>([]);

// 公告表单数据
const announcementForm = reactive({
  template: '',
  content: ``,
});

// 模板选项
const templateOptions = ref<TemplateOption[]>([]);

// 定标附件文件列表
const awardFileList = ref<any[]>([]);

// 备注信息
const remarkInfo = reactive({ remark: '' });

// 表单验证规则
const announcementRules = reactive({
  template: [{ required: true, message: '请选择公告模版', trigger: 'change' }],
});

// 计算属性 - 筛选后的竞谈列表
const filteredNegotiationList = computed(() => {
  return [...negotiationTableData.value];
});

// 格式化价格
function formatPrice(price: number): string {
  return `¥${price.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
}

const handleRevoke = async () => {
  try {
    ElMessageBox.confirm('确定要撤销审批吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
      .then(() => {
        cancelApprovePlan({ bizType: 'SRM_TENDER_BID_AUDIT', bizKey: `${projectId.value}#${noticeId.value}#${projectInfo.sectionId}` }).then(() => {
          ElMessage({
            type: 'success',
            message: '撤销审批成功！',
          });
          loadTenderBidDetail();
        });
      })
      .catch(() => {});
  } catch (error) {
    if (error !== 'cancel') {
      console.error('撤销审批失败:', error);
    }
  }
};

// 获取审批状态类型
function getApprovalStatusType(status: string): string {
  const typeMap: Record<string, string> = {
    TO_APPROVE: 'warning',
    APPROVING: 'warning',
    APPROVE: 'success',
    APPROVE_REJECT: 'danger',
    APPROVE_REVOKE: 'danger',
  };
  return typeMap[status] || '';
}

// 获取审批状态文本
function getApprovalStatusText(status: string): string {
  const textMap: Record<string, string> = {
    TO_APPROVE: '待审批',
    APPROVING: '审批中',
    APPROVE: '已审批',
    APPROVE_REJECT: '已驳回',
    APPROVE_REVOKE: '已撤销',
  };
  return textMap[status] || '未知';
}

// 处理标段切换
function handleSectionChange(sectionId: number) {
  projectInfo.sectionId = sectionId;
  // 重新查询定标详情（因为标段ID变化了）
  loadTenderBidDetail();
}

const handelUploadFile = (file: any[]) => {
  console.log(file, 'sadasd');
  awardFileList.value = file;
};

// 更新中标状态 - 竞谈项目只能选择一个供应商中标
function updateAwardStatus(row: any, awarded: boolean) {
  if (awarded) {
    // 如果选择中标，先取消其他供应商的中标状态
    negotiationTableData.value.forEach((item: any) => {
      if (item !== row) {
        item.awarded = 0;
      }
    });
    row.awarded = 1;
  } else {
    row.awarded = 0;
  }
}

// 查询竞谈项目供应商数据
async function loadNegotiationData(awardDetailData?: any) {
  try {
    loading.value = true;
    const { data } = await queryQuoteListBySupplier({
      sectionId: projectInfo.sectionId,
      roundNo: roundNum.value,
      noticeId: noticeId.value,
      projectId: projectId.value,
    });
    const list = data || [];
    negotiationTableData.value = list?.map((row: any) => {
      return {
        ...row,
        awarded: 0, // 默认未中标
      };
    });

    // 如果是编辑模式，需要回显中标状态
    if (isEditMode.value) {
      // 竞谈项目的中标信息可能在不同的位置
      let awardDetail = null;

      // 优先使用传入的数据
      if (awardDetailData) {
        // 首先尝试从 projectItemList 中查找
        if (awardDetailData.projectItemList?.length > 0) {
          awardDetail = awardDetailData.projectItemList.find((item: any) => item.sectionId === projectInfo.sectionId);
        }

        // 如果 projectItemList 中没有找到，尝试从根级别数据中获取
        if (!awardDetail && awardDetailData.tenantSupplierId) {
          awardDetail = awardDetailData;
        }
      } else {
        // 如果没有传入数据，从 store 中获取
        if (awardStore.tenderBidDetail?.projectItemList?.length > 0) {
          awardDetail = awardStore.tenderBidDetail.projectItemList.find((item: any) => item.sectionId === projectInfo.sectionId);
        }

        if (!awardDetail && awardStore.tenderBidDetail?.tenantSupplierId) {
          awardDetail = awardStore.tenderBidDetail;
        }
      }

      if (awardDetail && awardDetail.tenantSupplierId) {
        // 找到中标的供应商并设置中标状态
        const awardedSupplier = negotiationTableData.value.find((supplier: any) =>
          supplier.tenantSupplierId.toString() === awardDetail.tenantSupplierId.toString()
        );
        if (awardedSupplier) {
          awardedSupplier.awarded = 1;
          // 回显中标金额等信息
          if (awardDetail.bidAmount) {
            awardedSupplier.bidAmount = awardDetail.bidAmount;
          }
        }
      }
    }
  } catch (e) {
    // 忽略错误，使用空数据
  } finally {
    loading.value = false;
  }
}

// 数据校验函数
function validateRequiredData(): boolean {
  // 检查是否有中标供应商
  const hasAwardedItems = negotiationTableData.value.some((item: any) => item.awarded === 1);
  if (!hasAwardedItems) {
    ElMessage.error('请至少选择一个供应商进行中标');
    return false;
  }

  // 检查是否只有一个供应商中标
  const awardedItems = negotiationTableData.value.filter((item: any) => item.awarded === 1);
  if (awardedItems.length > 1) {
    ElMessage.error('竞谈项目只能选择一个供应商中标');
    return false;
  }

  return true;
}

// 数据转换函数 - 将定标数据转换为模板格式
function transformAwardDataForTemplate() {
  // 获取中标项目
  const awardedItems = negotiationTableData.value.filter((item: any) => item.awarded === 1);

  const totalAwardAmount = awardedItems.reduce((sum: number, supplier: any) => precisionMath.add(sum, supplier.totalQuoteAmount || 0), 0);

  return {
    // 项目基本信息
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,
    projectName: awardInfo.projectName || '',
    projectCode: awardInfo.projectCode || '',
    procurementMethod: awardInfo.procurementMethod || '',
    budgetAmount: awardInfo.budgetAmount || 0,

    // 定标信息
    awardPerson: awardInfo.awardPerson || projectDetail.value?.createBy || '',
    awardTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    awardAmount: totalAwardAmount,
    formattedAwardAmount: formatPrice(totalAwardAmount),
    savingsAmount: precisionMath.subtract(awardInfo.budgetAmount, totalAwardAmount),
    formattedSavingsAmount: formatPrice(precisionMath.subtract(awardInfo.budgetAmount, totalAwardAmount)),

    // 中标供应商信息
    awardedSuppliers: awardedItems.map((item: any, index: number) => ({
      index: index + 1,
      supplierName: item.supplierName,
      contactPerson: item.contactPerson,
      contactPhone: item.contactPhone,
      quoteRoundCount: item.quoteRoundCount,
      totalQuoteAmount: item.totalQuoteAmount,
      formattedAmount: formatPrice(item.totalQuoteAmount || 0),
      quoteIp: item.quoteIp,
    })),
    totalSuppliers: awardedItems.length,

    // 时间信息
    generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
    currentDate: moment().format('YYYY年MM月DD日'),
  };
}

// 处理编辑公告内容
async function handleEditContent() {
  try {
    // 编辑模式：直接显示已保存的内容
    if (isEditMode.value) {
      if (!announcementForm.content) {
        ElMessage.error('暂无公告内容');
        return;
      }

      // 直接打开富文本编辑器，显示已保存的内容
      announcementEditorRef.value?.show(announcementForm.content, '查看定标公告');
      return;
    }
  } catch (error) {}
}

// 处理保存公告内容
async function handleSaveContent(content: string) {
  if (isEditMode.value) {
    return;
  }

  // 变更模式下，通过 emit 通知父组件处理
  if (props.isChangeMode) {
    // 1. 先校验数据
    if (!validateRequiredData()) {
      return;
    }

    // 2. 检查是否选择了模板
    if (!announcementForm.template) {
      ElMessage.error('请先选择定标公告模板');
      return;
    }

    // 3. 更新公告内容
    announcementForm.content = content;

    // 4. 验证其他表单
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }

    // 5. 组装提交数据（使用竞谈专用转换器）
    const submitData = await assembleNegotiationSubmitData();

    // 6. 通知父组件处理变更提交
    emit('change-submit', {
      content,
      submitData,
    });
    return;
  }

  // 原有的定标模式逻辑
  // 1. 先校验数据
  if (!validateRequiredData()) {
    return;
  }

  // 2. 检查是否选择了模板
  if (!announcementForm.template) {
    ElMessage.error('请先选择定标公告模板');
    return;
  }

  try {
    saving.value = true;

    // 3. 更新公告内容
    announcementForm.content = content;

    // 4. 验证其他表单
    const isValid = await validateForm();
    if (!isValid) {
      return;
    }
    // 5. 组装提交数据
    const submitData = await assembleNegotiationSubmitData();

    // 6. 调用竞谈项目专用接口保存定标结果
    const { code, msg } = await submitAwardReview({
      ...submitData,
      isCompetitive: true
    });

    if (code === 0) {
      ElMessage.success('竞谈定标成功');
      emit('on-success');
      await loadTenderBidDetail();
    } else {
      ElMessage.error(msg || '竞谈定标失败');
    }
  } catch (error) {
  } finally {
    saving.value = false;
  }
}

// 处理模板变更（参考原文件逻辑）
async function handleTemplateChange() {
  if (!announcementForm.template) return;

  try {
    const { data } = await getTemplateDetail(announcementForm.template);
    announcementForm.content = data.content;
  } catch (e) {}
}

// 验证表单
async function validateForm(): Promise<boolean> {
  try {
    // 验证公告表单
    if (announcementFormRef.value) {
      await announcementFormRef.value.validate();
    }

    // 验证是否有中标供应商
    const hasAwardedItems = negotiationTableData.value.some((item: any) => item.awarded === 1);
    if (!hasAwardedItems) {
      ElMessage.error('请至少选择一个供应商进行中标');
      return false;
    }

    return true;
  } catch (error) {
    return false;
  }
}

// 生成中标通知书列表
async function generateAwardNoticeList(): Promise<AwardNoticeItem[]> {
  const awardedItems = negotiationTableData.value.filter((item: any) => item.awarded === 1);

  if (awardedItems.length === 0 || !announcementForm.template) {
    return [];
  }

  try {
    // 为每个中标供应商生成中标通知书
    const notificationPromises = awardedItems.map(async (supplier: any) => {
      const notificationContent = await generateNotificationForSupplier(supplier);

      return {
        noticeId: projectInfo.noticeId,
        projectId: projectInfo.projectId,
        sectionId: projectInfo.sectionId,
        evaluationResultId: 0, // 竞谈项目可能没有评价结果ID，使用默认值
        templateId: parseInt(announcementForm.template),
        tenantSupplierId: supplier.tenantSupplierId,
        supplierName: supplier.supplierName,
        noticeContent: notificationContent,
      } as AwardNoticeItem;
    });

    return await Promise.all(notificationPromises);
  } catch (error) {
    console.error('生成中标通知书失败:', error);
    return [];
  }
}

// 为单个供应商生成中标通知书内容
async function generateNotificationForSupplier(supplier: any): Promise<string> {
  try {
    if (!announcementForm.template) {
      return '';
    }

    // 构建供应商的模板数据
    const templateData = {
      // 项目基本信息
      projectName: awardInfo.projectName || '',
      projectCode: awardInfo.projectCode || '',
      procurementMethod: awardInfo.procurementMethod || '',
      noticeId: projectInfo.noticeId,
      projectId: projectInfo.projectId,

      // 供应商信息
      supplierName: supplier.supplierName,
      contactPerson: supplier.contactPerson,
      contactPhone: supplier.contactPhone,

      // 中标信息
      totalQuoteAmount: supplier.totalQuoteAmount,
      formattedTotalAmount: formatPrice(supplier.totalQuoteAmount || 0),
      quoteRoundCount: supplier.quoteRoundCount,

      // 定标信息
      awardPerson: awardInfo.awardPerson || projectDetail.value?.createBy || '',
      awardTime: moment().format('YYYY-MM-DD HH:mm:ss'),

      // 时间信息
      generateTime: moment().format('YYYY-MM-DD HH:mm:ss'),
      currentDate: moment().format('YYYY年MM月DD日'),
    };

    // 获取模板内容
    const { data } = await getTemplateDetail(announcementForm.template);
    const templateContent = data.content;

    // 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(templateContent, templateData, {
      defaultValue: '-',
      keepUndefinedVariables: false,
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: false,
    });

    return formattedContent;
  } catch (error) {
    console.error('生成供应商通知书失败:', error);
    return '';
  }
}

// 生成投标供应商列表
function generateBidSupplierList(): BidSupplierItem[] {
  return negotiationTableData.value.map((supplier: any) => {
    const isAwarded = supplier.awarded === 1;

    return {
      tenantSupplierId: supplier.tenantSupplierId,
      supplierName: supplier.supplierName,
      isBid: isAwarded,
      sectionId: projectInfo.sectionId,
      bidAmount: isAwarded ? (supplier.bidAmount || supplier.totalQuoteAmount) : undefined,
      awardRemark: isAwarded ? remarkInfo.remark : undefined,
      projectPaymentId: supplier.projectPaymentId,
      examineRemark: undefined,
      awardReportContent: isAwarded ? announcementForm.content : undefined,
      awardReportTime: isAwarded ? (awardInfo.awardTime || moment().format('YYYY-MM-DD HH:mm:ss')) : undefined,
      awardTemplateId: isAwarded ? (announcementForm.template ? parseInt(announcementForm.template) : undefined) : undefined,
    };
  }).filter(item => item.isBid);
}

// 组装竞谈项目提交数据
async function assembleNegotiationSubmitData() {
  // 筛选中标的供应商
  const awardedItems = negotiationTableData.value.filter((item: any) => item.awarded === 1);
  const awardedSupplier = awardedItems[0]; // 竞谈项目只有一个中标供应商

  // 组装附件信息
  const awardAttachments = awardFileList.value.map((file: any) => ({
    fileName: file.fileName || file.name,
    filePath: file.filePath || file.url || '',
    fileType: file.fileType || file.type || '',
    fileSize: file.fileSize || file.size || 0,
  }));

  // 生成中标通知书列表
  const awardNoticeReqList = await generateAwardNoticeList();

  // 生成投标供应商列表
  const bidSupplierList = generateBidSupplierList();

  return {
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,
    sectionId: projectInfo.sectionId,
    tenantSupplierId: awardedSupplier?.tenantSupplierId,
    supplierName: awardedSupplier?.supplierName,
    awardTemplateId: announcementForm.template,
    awardReportContent: announcementForm.content,
    attachmentInfos: awardAttachments,
    awardRemark: remarkInfo.remark,
    awardNoticeReqList,
    projectItemList: bidSupplierList,
  };
}

// 组装提交数据（保留原函数用于变更模式）
async function assembleSubmitData(): Promise<SubmitAwardReviewData & { winnerNotifications?: BatchAwardNoticeItem[]; awardNoticeReqList?: AwardNoticeItem[]; bidSupplierList?: BidSupplierItem[] }> {
  // 筛选中标的供应商项目
  const awardedItems = negotiationTableData.value.filter((item: any) => item.awarded === 1);

  // 竞谈项目的项目明细列表需要根据实际业务逻辑构建
  // 这里暂时使用空数组，实际应该根据中标供应商的报价明细来构建
  const procurementItemList: any[] = [];

  // 组装附件信息
  const awardAttachments = awardFileList.value.map((file: any) => ({
    fileName: file.fileName || file.name,
    filePath: file.filePath || file.url || '',
    fileType: file.fileType || file.type || '',
    fileSize: file.fileSize || file.size || 0,
  }));

  // 生成中标通知书列表
  const awardNoticeReqList = await generateAwardNoticeList();

  // 生成投标供应商列表
  const bidSupplierList = generateBidSupplierList();

  return {
    noticeId: projectInfo.noticeId,
    projectId: projectInfo.projectId,
    awardTemplateId: parseInt(announcementForm.template),
    awardReportContent: announcementForm.content,
    attachmentInfos: awardAttachments,
    awardRemark: remarkInfo.remark,
    projectItemList: procurementItemList,
    awardNoticeReqList,
    bidSupplierList,
  };
}

// 处理提交（只处理数据校验和模板转换，然后打开编辑器）
async function handleSubmit() {
  // 1. 先校验数据
  if (!validateRequiredData()) {
    return;
  }

  // 2. 检查是否选择了模板
  if (!announcementForm.template) {
    ElMessage.error('请先选择定标公告模板');
    return;
  }

  try {
    // 3. 转换数据为模板格式
    const templateData = transformAwardDataForTemplate();

    // 4. 获取模板内容（如果还没有的话）
    if (!announcementForm.content || announcementForm.content.includes('<h2>XDMY-LYMG')) {
      const { data } = await getTemplateDetail(announcementForm.template);
      announcementForm.content = data.content;
    }

    // 5. 使用模板格式化器处理内容
    const formattedContent = templateContentFormatter(announcementForm.content, templateData, {
      defaultValue: '-',
      keepUndefinedVariables: false,
      removeEmptyLoops: true,
      removeEmptyTags: true,
      debug: true, // 调试模式，便于查看
    });

    // 6. 打开富文本编辑器进行最终编辑和保存
    announcementEditorRef.value?.show(formattedContent, '编辑定标公告');
  } catch (error) {}
}

// 处理取消
function handleCancel() {
  ElMessageBox.confirm('确定要取消吗？未保存的数据将丢失', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      // 可以添加页面跳转逻辑
      // router.back();
      ElMessage.info('已取消');
    })
    .catch(() => {
      // 用户取消
    });
}

// 查询定标详情 - 使用 store
async function loadTenderBidDetail() {
  try {
    if (!noticeId.value || !projectId.value) {
      return;
    }

    const service = props.isViewMode ? getNoticeChangeDetailInfo : awardStore.loadForAwardResult;
    const params = {
      noticeId: noticeId.value,
      projectId: projectId.value,
      changeTypeEnum: 'CHANGE_BID',
    };

    const result = props.isViewMode ? await service(params) : await service(noticeId.value, projectId.value, true);

    if (result.data) {
      announcementForm.content = result.data.awardReportContent;

      // 查看模式下保存项目物料列表
      if (result.data.projectItemList) {
        viewModeProjectItemList.value = result.data.projectItemList;
      }

      if (isEditMode.value) {
        // 竞谈项目的数据结构不同，直接使用根级别的数据
        if (props.isViewMode) {
          fillComponentSpecificData(result.data);
        } else {
          // 对于竞谈项目，如果有 projectItemList 且为当前标段，使用 projectItemList 中的数据
          // 否则使用根级别的数据
          const sectionData = result.data?.projectItemList?.find((item: any) => item.sectionId === projectInfo.sectionId);
          fillComponentSpecificData(sectionData || result.data);
        }
      }

      // 加载竞谈数据，并传递中标信息用于回显
      loadNegotiationData(result.data);
    }
  } catch (error) {}
}

// 回填组件特有数据 (公告表单数据已在 store 中处理)
function fillComponentSpecificData(data: TenderBidPublicityDetail) {
  // 回填定标基本信息
  awardInfo.awardPerson = data.createByName || '';
  awardInfo.awardTime = data.awardReportTime || '';
  announcementForm.template = data.awardTemplateId || '';

  // 回填公告内容
  if (data.awardReportContent) {
    announcementForm.content = data.awardReportContent;
  }

  // 回填定标附件
  if (data.awardAttachments && data.awardAttachments.length > 0) {
    awardFileList.value = data.awardAttachments.map((file) => ({
      fileName: file.fileName,
      filePath: file.filePath,
      fileType: file.fileType,
      fileSize: file.fileSize,
      name: file.fileName,
      url: file.filePath,
      type: file.fileType,
      size: file.fileSize,
    }));
  }

  // 回填定标备注
  if (data.awardRemark) {
    remarkInfo.remark = data.awardRemark;
  }
}

const handleDownload = (item: any) => {
  downloadUrlFile(item.filePath, item.fileName);
};

const init = async () => {
  // 获取定标公告模板列表
  const { data } = await getTemplateList({
    type: 'AWARD_RESULT',
  });
  templateOptions.value = data?.records || [];

  // 设置当前标段ID
  if (props.sectionId) {
    projectInfo.sectionId = props.sectionId;
  }

  // 加载定标详情
  await loadTenderBidDetail();
};

onMounted(() => {
  init();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

.award-info-section,
.award-list-section,
.award-announcement-section,
.award-attachments-section,
.award-remark-section {
  background: #fff;
  border-radius: 6px;
  margin-bottom: 12px;
  padding: 16px;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

.award-announcement-section,.award-remark-section, .award-attachments-section {
  .section-header {
    margin-bottom: 12px;
  }
}

.price-text {
  font-weight: 500;
  color: var(--Color-Primary-color-primary, #0069ff);
}

.savings-text {
  font-weight: 500;
  color: #52c41a;
}

.savings-rate {
  font-weight: 500;
  color: #52c41a;
}

.award-container {
  max-height: 400px;
  overflow-y: auto;
}

.award-info-content {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}


.info-item {
  display: flex;
  flex-direction: row;
  gap: 8px;
}

.info-label {
  color: #86909c;
  font-size: 14px;
  line-height: 20px;
}

.info-value {
  color: #1d2129;
  font-size: 14px;
  line-height: 20px;
  font-weight: 400;
}

.attachment-form {
  :deep(.el-form-item--default) {
    margin-bottom: 0 !important;
    //width: 100%;
  }
}

.editable-table {
  :deep(.el-table__header) {
    th {
      background: var(--Color-Fill-fill-color-light, #f5f7fa);
      padding: 6px 0;
      border-bottom: 1px solid var(--Color-Border-border-color-light, #ebeef5);

      .cell {
        color: var(---el-text-color-regular, #505762);
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
      }
    }
  }

  :deep(.el-table__body) {
    tr {
      &:hover {
        background-color: #fff !important;

        td {
          background-color: #fff !important;
        }
      }

      td {
        border-bottom: 1px solid #ebeef5;
        padding: 6px 0;
      }
    }
  }

  // 确保 Element Plus 的 hover 类也被覆盖
  :deep(.el-table__row) {
    &:hover {
      background-color: #fff !important;

      td {
        background-color: #fff !important;
      }
    }

    &.hover-row {
      background-color: transparent !important;
    }

    td {
      &.hover-cell {
        background-color: transparent !important;
      }
    }
  }

  // 表格内的控件样式
  .table-select,
  .table-input {
    width: 100%;

    :deep(.el-input__wrapper) {
      background-color: transparent !important;
      border: 1px solid transparent !important;
      box-shadow: none !important;
      padding: 4px 8px;

      &:hover,
      &:focus {
        border-color: var(--Color-Primary-color-primary, #0069ff) !important;
        background-color: #fff !important;
      }
    }

    :deep(.el-input__inner) {
      border-radius: var(--Radius-border-radius-small, 2px) !important;
      background: var(--Color-Fill-fill-color-light, #f5f7fa) !important;
      border: none !important;
      color: #1d2129 !important;
      font-size: 14px;
      height: 24px;

      &::placeholder {
        color: var(--Color-Text-text-color-regular, #4e5969);
        font-family: 'PingFang SC';
        font-size: 14px;
      }
    }
  }

  // 操作按钮样式
  .table-actions {
    display: flex;
    gap: 8px;
    align-items: center;

    .action-btn {
      padding: 4px 8px;
      height: 28px;
      font-size: 12px;
      border-radius: 4px;

      &.el-button--primary {
        background: var(--Color-Primary-color-primary, #0069ff);
        border-color: var(--Color-Primary-color-primary, #0069ff);

        &:hover {
          background: #1677ff;
          border-color: #1677ff;
        }
      }

      &.el-button--danger {
        background: #ff4d4f;
        border-color: #ff4d4f;

        &:hover {
          background: #ff7875;
          border-color: #ff7875;
        }
      }
    }
  }
}

.export-actions {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 16px;
}

.filter-section {
  //margin-bottom: 16px;
  //padding: 16px;
  //background: #f9fafb;
  border-radius: 6px;
  //border: 1px solid #e5e7eb;
}

.filter-form {
  margin: 0;

  .el-form-item {
    margin-bottom: 0;
    margin-right: 16px;

    &:last-child {
      margin-right: 0;
    }
  }
}

.content-editor-wrapper {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  min-height: 120px;
  background: #fff;

  .content-preview {
    padding: 12px;
    max-height: 200px;
    overflow-y: auto;

    .preview-text {
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
      word-break: break-word;
    }
  }

  .content-placeholder {
    padding: 12px;
    color: #c0c4cc;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 96px;
  }

  .edit-button {
    position: absolute;
    top: 8px;
    right: 8px;
    z-index: 10;
  }

  &:hover {
    border-color: #c0c4cc;

    .edit-button {
      opacity: 1;
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

.award-text-primary {
  color: var(--Color-Text-text-color-primary, #1d2129);

  /* regular/extra-small */
  font-family: 'PingFang SC';
  font-style: normal;
  font-weight: 600;
  line-height: 20px;
}

.file-label {
  color: var(--Color-Text-text-color-regular, #4e5969);
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}

.file-item-label {
  color: var(--Color-Primary-color-primary, #0069ff);
  font-size: 14px;
  font-weight: 400;
  cursor: pointer;
}

.header-title {
  color: var(--Color-Text-text-color-primary, #1d2129);
  font-family: 'PingFang SC';
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  line-height: 24px;
  padding-left: 10px;
  position: relative;
  &::before {
    content: '';
    display: inline-block;
    width: 2px;
    height: 14px;
    background: var(--Color-Primary-color-primary, #0069ff);
    margin-right: 8px;
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>

