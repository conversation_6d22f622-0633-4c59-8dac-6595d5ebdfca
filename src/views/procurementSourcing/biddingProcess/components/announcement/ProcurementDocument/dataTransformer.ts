/**
 * 数据转换工具
 * 用于在组件表单数据和API数据之间进行转换
 */
import type {
  AnnouncementFormData,
  BidsSegment,
  ApiPurchaseDateDemand,
  AttachmentInfo, ContactInfo, QuotationDemand,
} from './types';

/**
 * 将组件表单数据转换为API数据格式
 */
export function formToApiData(formData: AnnouncementFormData, projectId: string): {
  id: string;
  noticeTitle: string;
  noticeTemplateId: string;
  projectId: string;
  noticeContent: string;
  tenderWay: string;
  purchaseDateDemand: ApiPurchaseDateDemand;
  quotationDemand: QuotationDemand;
  bidsSegments: BidsSegment[];
  quotationNotice: string;
  evaluationMethod: "COMPREHENSIVE" | "LOWEST_PRICE" | "COMPREHENSIVE_SCORE" | "HIGHEST_PRICE";
  noticeStatus: 'TO_APPROVE' | 'APPROVING' | 'APPROVE' | 'APPROVE_REJECT' | 'APPROVE_REVOKE';
  contactInfo: ContactInfo;
  attachmentInfos: AttachmentInfo[]
} {
  // 转换时间格式：从范围格式转换为单独的开始和结束时间
  const purchaseDateDemand: ApiPurchaseDateDemand = {
    registerStartTime: formData.purchaseDateDemand.registerTimeRange?.[0] || '',
    registerEndTime: formData.purchaseDateDemand.registerTimeRange?.[1] || '',
    auditStartTime: formData.purchaseDateDemand.auditTimeRange?.[0] || '',
    auditEndTime: formData.purchaseDateDemand.auditTimeRange?.[1] || '',
    quoteStartTime: formData.purchaseDateDemand.quoteTimeRange?.[0] || '',
    quoteEndTime: formData.purchaseDateDemand.quoteTimeRange?.[1] || '',
    bidOpenTime: formData.purchaseDateDemand.bidOpenTime,
    bidOpener: formData.purchaseDateDemand.bidOpener,
    fileSubmissionAddress: formData.quotationDemand.fileSubmissionAddress,
    bidDocPayStartTime: formData.purchaseDateDemand.bidFeePaymentTimeRange?.[0] || '',
    bidDocPayEndTime: formData.purchaseDateDemand.bidFeePaymentTimeRange?.[1] || '',
    fileObtainStartTime: formData.purchaseDateDemand.documentObtainTimeRange?.[0] || '',
    fileObtainEndTime: formData.purchaseDateDemand.documentObtainTimeRange?.[1] || '',
  };

  return {
    id: formData.id,
    noticeStatus: formData.noticeStatus,
    noticeTitle: formData.announcementInfo.title,
    noticeTemplateId: formData.announcementInfo.template,
    projectId: projectId,
    tenderWay: formData.quotationDemand.tenderWay,
    noticeContent: formData.announcementInfo?.content || '', // 富文本内容，暂时使用默认值
    bidOpeningAddress: formData.purchaseDateDemand.bidOpeningAddress,
    purchaseDateDemand: purchaseDateDemand,
    quotationDemand: formData.quotationDemand,
    bidsSegments: formData.bidsSegments,
    quotationNotice: formData.quotationNotice,
    evaluationMethod: formData.evaluationMethod,
    contactInfo: formData.contactInfo,
    attachmentInfos: formData.attachmentInfos,
    approvalType: formData.approvalData?.approvalType || 0,
    specialProcessExecutorList: formData.approvalData?.specialProcessExecutorList || []
  };
}

/**
 * 根据标段ID和类型获取对应的标段数据
 */
export function getBidsSegmentsBySection(
  bidsSegments: BidsSegment[],
  sectionId: string,
  type: 'QUALIFICATION' | 'CONDITION' | 'FEE' | 'INVITE_SUPPLIERS'
): BidsSegment[] {
  return bidsSegments.filter(segment =>
    segment.sectionId === sectionId && segment.requirementType === type
  );
}

/**
 * 创建新的标段要求项
 */
export function createBidsSegment(
  type: 'QUALIFICATION' | 'CONDITION' | 'FEE' | 'INVITE_SUPPLIERS',
  sectionId: string,
  data: Partial<BidsSegment> = {}
): BidsSegment {
  const baseSegment: BidsSegment = {
    requirementType: type,
    sectionId: sectionId,
    ...data
  };

  if (type === 'FEE') {
    return {
      ...baseSegment,
      amountSet: data.amountSet || true,
      feeInfo: data.feeInfo || {
        guaranteeAmount: 0,
        payAccount: '',
        payBank: '',
        openAccountBank: ''
      }
    };
  }

  if (type === 'INVITE_SUPPLIERS') {
    return {
      ...baseSegment,
      inviteSuppliers: data.inviteSuppliers || []
    };
  }

  return {
    ...baseSegment,
    requirementName: data.requirementName || '',
    requirementContent: data.requirementContent || ''
  };
}

/**
 * 更新标段要求项
 */
export function updateBidsSegment(
  bidsSegments: BidsSegment[],
  index: number,
  updates: Partial<BidsSegment>
): BidsSegment[] {
  const newSegments = [...bidsSegments];
  newSegments[index] = { ...newSegments[index], ...updates };
  return newSegments;
}

/**
 * 删除标段要求项
 */
export function removeBidsSegment(
  bidsSegments: BidsSegment[],
  index: number
): BidsSegment[] {
  const newSegments = [...bidsSegments];
  newSegments.splice(index, 1);
  return newSegments;
}

/**
 * 添加标段要求项
 */
export function addBidsSegment(
  bidsSegments: BidsSegment[],
  newSegment: BidsSegment,
  index?: number
): BidsSegment[] {
  const newSegments = [...bidsSegments];
  if (index !== undefined) {
    newSegments.splice(index + 1, 0, newSegment);
  } else {
    newSegments.push(newSegment);
  }
  return newSegments;
}

/**
 * 验证表单数据 - 专注于业务逻辑验证
 * 注意：基础字段验证（必填、格式等）由 Element UI 表单验证处理
 */
export function validateFormData(
  formData: AnnouncementFormData,
  lotList?: Array<{ id: string; sectionName: string }>,
  isInviteMode: boolean = false
): {
  isValid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // 注意：时间范围的必填验证和时间逻辑关系验证已在 Element UI 表单验证中处理

  // 获取标段名称的辅助函数
  const getSectionName = (sectionId: string): string => {
    if (!lotList) return '标段';
    const lot = lotList.find(lot => lot.id === sectionId);
    return lot ? lot.sectionName : '标段';
  };

  // 验证标段数据完整性（业务逻辑验证）
  if (lotList && lotList.length > 0) {
    // 检查每个标段是否都有必要的数据结构
    lotList.forEach(lot => {
      const sectionName = getSectionName(lot.id);

      // 检查费用设置是否存在
      const feeSegments = formData.bidsSegments.filter(
        segment => segment.sectionId === lot.id && segment.requirementType === 'FEE'
      );

      if (feeSegments.length === 0) {
        errors.push(`${sectionName}缺少费用设置配置`);
      }

      // 在发标模式下检查资格要求和条件设置
      if (!isInviteMode) {
        const qualificationSegments = formData.bidsSegments.filter(
          segment => segment.sectionId === lot.id && segment.requirementType === 'QUALIFICATION'
        );
        const conditionSegments = formData.bidsSegments.filter(
          segment => segment.sectionId === lot.id && segment.requirementType === 'CONDITION'
        );

        if (qualificationSegments.length === 0) {
          errors.push(`${sectionName}缺少资格要求配置`);
        }

        if (conditionSegments.length === 0) {
          errors.push(`${sectionName}缺少报价响应条件配置`);
        }
      }
    });
  }

  // 在邀请函模式下验证邀请供应商业务逻辑
  if (isInviteMode && lotList && lotList.length > 0) {
    const inviteSegments = formData.bidsSegments.filter(segment => segment.requirementType === 'INVITE_SUPPLIERS');

    // 为每个标段检查是否有邀请供应商
    lotList.forEach(lot => {
      const sectionName = getSectionName(lot.id);
      const sectionInviteSegment = inviteSegments.find(segment => segment.sectionId === lot.id);

      if (!sectionInviteSegment || !sectionInviteSegment.inviteSuppliers || sectionInviteSegment.inviteSuppliers.length === 0) {
        errors.push(`${sectionName}必须至少邀请一个供应商`);
      } else {
        // 验证邀请供应商数量是否符合业务要求（例如：至少3家）
        if (sectionInviteSegment.inviteSuppliers.length < 3) {
          errors.push(`${sectionName}建议至少邀请3家供应商以确保竞争性`);
        }
      }
    });
  }

  // 验证费用设置的业务逻辑
  const feeSegments = formData.bidsSegments.filter(segment => segment.requirementType === 'FEE');
  feeSegments.forEach((segment) => {
    const sectionName = getSectionName(segment.sectionId);

    if (segment.amountSet && segment.feeInfo) {
      // 验证保证金金额的合理性
      if (segment.feeInfo.guaranteeAmount > 1000000) {
        errors.push(`${sectionName}的保证金金额过高，请确认是否合理`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * 确保每个标段都有完整的数据结构
 */
export function ensureCompleteBidsSegments(
  bidsSegments: BidsSegment[],
  lotIds: string[],
  isInviteMode: boolean = false
): BidsSegment[] {
  const result: BidsSegment[] = [...bidsSegments];

  // 确保每个标段都有对应类型的数据
  lotIds.forEach(lotId => {
    let types: Array<'QUALIFICATION' | 'CONDITION' | 'FEE' | 'INVITE_SUPPLIERS'>;

    if (isInviteMode) {
      types = ['FEE', 'INVITE_SUPPLIERS'];
    } else {
      types = ['QUALIFICATION', 'CONDITION', 'FEE'];
    }

    types.forEach(type => {
      const existing = result.find(segment =>
        segment.sectionId === lotId && segment.requirementType === type
      );

      if (!existing) {
        // 如果不存在，创建默认数据
        result.push(createBidsSegment(type, lotId));
      }
    });
  });

  return result;
}

/**
 * 获取评审规则选项
 */
export function getEvaluationRuleOptions() {
  return [
    { label: '综合评标低价法', value: 'COMPREHENSIVE' as const },
    { label: '最低价法', value: 'LOWEST_PRICE' as const },
    { label: '综合评分法', value: 'COMPREHENSIVE_SCORE' as const },
    { label: '最高价法', value: 'HIGHEST_PRICE' as const }
  ];
}

/**
 * 获取发票要求选项
 */
export function getCertificateTypeOptions() {
  return [
    { label: '专票', value: 'SPECIAL' as const },
    { label: '普票', value: 'NORMAL' as const },
    { label: '无要求', value: 'NONE' as const }
  ];
}

/**
 * 创建默认的表单数据
 */
export function createDefaultFormData(
  lotList: Array<{ id: string; sectionName: string }> = []
): AnnouncementFormData {
  // 创建默认的标段数据
  const bidsSegments: BidsSegment[] = [];

  if (lotList.length > 0) {
    lotList.forEach((lot) => {
      // 为每个标段添加默认的资格要求
      bidsSegments.push(
        createBidsSegment('QUALIFICATION', lot.id, {
          requirementName: '营业执照',
          requirementContent: '',
        })
      );

      // 为每个标段添加默认的响应条件
      bidsSegments.push(
        createBidsSegment('CONDITION', lot.id, {
          requirementName: '*资格条件',
          requirementContent: '',
        })
      );

      // 为每个标段添加默认的费用设置
      bidsSegments.push(
        createBidsSegment('FEE', lot.id, {
          amountSet: true,
          feeInfo: {
            guaranteeAmount: 0,
            payAccount: '',
            payBank: '',
            openAccountBank: '',
          },
        })
      );
    });
  }

  return {
    announcementInfo: {
      title: '',
      template: '',
      content: '',
    },
    purchaseDateDemand: {
      registerTimeRange: ['', ''],
      quoteTimeRange: ['', ''],
      bidOpenTime: '',
      bidOpener: '',
      documentObtainTimeRange: ['', ''],
      bidFeePaymentTimeRange: ['', ''],
    },
    quotationDemand: {
      province: '',
      city: '',
      district: '',
      address: '',
      fileSubmissionAddress: '',
      tenderWay: 'ONLINE',
      includeTax: '1',
      certificateType: 'SPECIAL',
    },
    bidsSegments,
    evaluationMethod: 'LOWEST_PRICE',
    quotationNotice: '',
    contactInfo: {
      contactPerson: '',
      contactPhone: '',
      contactFixedPhone: '',
      contactEmail: '',
    },
    publishMedia: [
      {
        id: '1',
        serialNumber: 1,
        mediaName: '中国采购与招标网',
        selected: false,
      },
      {
        id: '2',
        serialNumber: 2,
        mediaName: '中国招标投标公共服务平台',
        selected: false,
      },
    ],
    attachmentInfos: [],
    approvalData: {
      approvalType: 0, // 0: 系统自动发起, 1: 指定审批人
      specialProcessExecutorList: [], // 指定审批人列表
    }
  };
}
