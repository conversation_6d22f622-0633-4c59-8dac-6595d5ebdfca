/**
 * 详情数据转换工具
 * 专门用于处理从详情接口获取的数据并转换为表单可用的格式
 */

import type { AnnouncementFormData, BidsSegment } from './types';
import {
  ensureCompleteBidsSegments,
  createBidsSegment,
  createDefaultFormData
} from './dataTransformer';

/**
 * 将详情数据转换为表单数据格式的主要方法
 * @param detailData 从详情接口获取的原始数据
 * @param lotList 标段列表信息
 * @returns 转换后的表单数据
 */
export function convertDetailToFormData(
  detailData: any,
  lotList: Array<{ id: string; sectionName: string }> = []
): AnnouncementFormData {
  console.log('开始转换详情数据:', detailData);

  // 如果详情数据为空，返回默认的表单数据
  if (!detailData) {
    console.log('详情数据为空，返回默认表单数据');
    return createDefaultFormData(lotList);
  }

  try {
    // 转换公告信息
    const announcementInfo = {
      title: detailData.noticeTitle || '',
      template: detailData.noticeTemplateId || '',
      content: detailData.noticeContent || '',
    };

    // 转换时间信息：从单独的开始和结束时间转换为范围格式
    const purchaseDateDemand = {
      registerTimeRange: [
        detailData.purchaseDateDemand?.registerStartTime || '',
        detailData.purchaseDateDemand?.registerEndTime || ''
      ],
      auditTimeRange: [
        detailData.purchaseDateDemand?.auditStartTime || '',
        detailData.purchaseDateDemand?.auditEndTime || ''
      ],
      quoteTimeRange: [
        detailData.purchaseDateDemand?.quoteStartTime || '',
        detailData.purchaseDateDemand?.quoteEndTime || ''
      ],
      bidOpenTime: detailData.purchaseDateDemand?.bidOpenTime || '',
      bidOpener: detailData.purchaseDateDemand?.bidOpener || '',
      bidOpeningAddress: detailData.bidOpeningAddress || '',
      bidFeePaymentTimeRange: [
        detailData.purchaseDateDemand?.bidDocPayStartTime || '',
        detailData.purchaseDateDemand?.bidDocPayEndTime || '',
      ],
      documentObtainTimeRange: [
        detailData.purchaseDateDemand?.fileObtainStartTime || '',
        detailData.purchaseDateDemand?.fileObtainEndTime || '',
      ]
    };

    // 转换报价要求信息
    const quotationDemand = {
      province: detailData.quotationDemand?.province || '',
      city: detailData.quotationDemand?.city || '',
      district: detailData.quotationDemand?.district || '',
      address: detailData.quotationDemand?.address || '',
      fileSubmissionAddress: detailData.purchaseDateDemand?.fileSubmissionAddress || '',
      includeTax: detailData.quotationDemand?.includeTax || '1',
      certificateType: detailData.quotationDemand?.certificateType || 'SPECIAL',
      tenderWay: detailData.tenderWay || 'ONLINE'
    };

    // 转换联系方式信息
    const contactInfo = {
      contactPerson: detailData.contactInfo?.contactPerson || '',
      contactPhone: detailData.contactInfo?.contactPhone || '',
      contactFixedPhone: detailData.contactInfo?.contactFixedPhone || '',
      contactEmail: detailData.contactInfo?.contactEmail || ''
    };

    // 处理标段数据
    let bidsSegments: BidsSegment[] = [];

    if (detailData.bidsSegments && Array.isArray(detailData.bidsSegments)) {
      bidsSegments = detailData.bidsSegments.map((segment: any) => ({
        requirementType: segment.requirementType,
        sectionId: segment.sectionId,
        requirementName: segment.requirementName || '',
        requirementContent: segment.requirementContent || '',
        amountSet: segment.amountSet || false,
        feeInfo: segment.feeInfo ? {
          guaranteeAmount: segment.feeInfo.guaranteeAmount || 0,
          payAccount: segment.feeInfo.payAccount || '',
          payBank: segment.feeInfo.payBank || '',
          openAccountBank: segment.feeInfo.openAccountBank || ''
        } : undefined,
        inviteSuppliers: segment.inviteSuppliers || []
      }));
    }

    // 如果有标段列表，确保每个标段都有完整的数据结构
    if (lotList.length > 0) {
      bidsSegments = ensureCompleteBidsSegments(bidsSegments, lotList.map(lot => lot.id));
    }

    // 处理附件信息
    const attachmentInfos = Array.isArray(detailData.attachmentInfos)
      ? detailData.attachmentInfos.map((item: any) => ({
          fileName: item.fileName || '',
          filePath: item.filePath || ''
        }))
      : [];

    // 处理发布媒体信息
    const publishMedia = Array.isArray(detailData.publishMedia)
      ? detailData.publishMedia
      : [
          {
            id: '1',
            serialNumber: 1,
            mediaName: '中国采购与招标网',
            selected: false,
          },
          {
            id: '2',
            serialNumber: 2,
            mediaName: '中国招标投标公共服务平台',
            selected: false,
          },
        ];

    // 转换审批数据
    const approvalData = {
      approvalType: detailData.approvalType || 0,
      specialProcessExecutorList: detailData.specialProcessExecutorList || []
    };

    const formData: AnnouncementFormData = {
      id: detailData.id,
      announcementInfo,
      purchaseDateDemand,
      quotationDemand,
      bidsSegments,
      evaluationMethod: detailData.evaluationMethod || 'LOWEST_PRICE',
      quotationNotice: detailData.quotationNotice || '',
      noticeStatus: detailData.noticeStatus || 'TO_APPROVE',
      contactInfo,
      publishMedia,
      attachmentInfos,
      approvalData
    };

    console.log('转换完成的表单数据:', formData);
    return formData;

  } catch (error) {
    console.error('转换详情数据时发生错误:', error);
    // 发生错误时返回默认数据
    return createDefaultFormData(lotList);
  }
}

/**
 * 转换地区信息为级联选择器需要的路径格式
 * @param province 省份
 * @param city 城市
 * @param district 区县
 * @param addressOptions 地址选项数据
 * @returns 级联选择器路径数组
 */
export function convertAreaToPath(
  province: string,
  city: string,
  district: string,
  addressOptions: any[]
): string[] {
  if (!province || !city || !district || !addressOptions.length) {
    return [];
  }

  try {
    // 查找省份
    const provinceOption = addressOptions.find(option => option.label === province);
    if (!provinceOption) return [];

    // 查找城市
    const cityOption = provinceOption.children?.find((option: any) => option.label === city);
    if (!cityOption) return [];

    // 查找区县
    const districtOption = cityOption.children?.find((option: any) => option.label === district);
    if (!districtOption) return [];

    return [provinceOption.value, cityOption.value, districtOption.value];
  } catch (error) {
    console.error('转换地区路径时发生错误:', error);
    return [];
  }
}

/**
 * 转换附件信息为上传组件需要的格式
 * @param attachmentInfos 附件信息数组
 * @returns 上传组件格式的附件数组
 */
export function convertAttachmentsForUpload(attachmentInfos: Array<{ fileName: string; filePath: string }>) {
  return attachmentInfos.map(item => ({
    name: item.fileName,
    url: item.filePath
  }));
}

/**
 * 验证转换后的数据是否完整
 * @param formData 转换后的表单数据
 * @param lotList 标段列表
 * @returns 验证结果
 */
export function validateConvertedData(
  formData: AnnouncementFormData,
  lotList: Array<{ id: string; sectionName: string }>,
  isInviteMode: boolean = false
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // 验证基本信息
  if (!formData.announcementInfo.title) {
    errors.push(isInviteMode ? '邀请函标题缺失' : '公告标题缺失');
  }

  // 验证联系方式
  if (!formData.contactInfo.contactPerson) {
    errors.push('联系人信息缺失');
  }

  // 验证标段数据
  if (lotList.length > 0) {
    lotList.forEach(lot => {
      const feeSegments = formData.bidsSegments.filter(
        segment => segment.sectionId === lot.id && segment.requirementType === 'FEE'
      );

      if (feeSegments.length === 0) {
        errors.push(`${lot.sectionName}缺少费用设置数据`);
      }

      if (isInviteMode) {
        // 邀请函模式验证邀请供应商
        const inviteSegments = formData.bidsSegments.filter(
          segment => segment.sectionId === lot.id && segment.requirementType === 'INVITE_SUPPLIERS'
        );
        if (inviteSegments.length === 0) {
          errors.push(`${lot.sectionName}缺少邀请供应商数据`);
        }
      } else {
        // 发标模式验证资格要求和报价响应条件
        const qualificationSegments = formData.bidsSegments.filter(
          segment => segment.sectionId === lot.id && segment.requirementType === 'QUALIFICATION'
        );
        const conditionSegments = formData.bidsSegments.filter(
          segment => segment.sectionId === lot.id && segment.requirementType === 'CONDITION'
        );

        if (qualificationSegments.length === 0) {
          errors.push(`${lot.sectionName}缺少资格要求数据`);
        }
        if (conditionSegments.length === 0) {
          errors.push(`${lot.sectionName}缺少报价响应条件数据`);
        }
      }
    });
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}
