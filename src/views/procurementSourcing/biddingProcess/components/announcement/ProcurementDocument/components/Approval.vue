<template>
  <div
    :id="'section-' + 'approval'"
    class="content-section"
  >
    <el-collapse
      v-model="activeCollapse"
      class="section-collapse"
      expand-icon-position="left"
    >
      <el-collapse-item name="approval">
        <template #title>
          <span class="collapse-title">
            <el-icon class="collapse-icon"><CaretRight /></el-icon>
            审批设置
          </span>
        </template>
        <div class="form-content">
          <div class="approval-section">
            <el-form-item
              label="发起审批"
              prop="specialProcessExecutorList"
            >
              <div class="flex items-center gap-2">
                <el-radio-group
                  v-model="localApprovalData.approvalType"
                  @change="handleApprovalTypeChange"
                >
                  <el-radio :label="0">系统自动发起</el-radio>
                  <el-radio :label="1">指定审批人</el-radio>
                </el-radio-group>
                <UserSelector
                  v-model="localApprovalData.specialProcessExecutorList"
                  v-show="localApprovalData.approvalType === 1"
                  :multiple="true"
                  style="width: auto;"
                  placeholder="请选择审批人"
                  @update:model-value="handleApprovalDataChange"
                />
              </div>
            </el-form-item>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue';
import { CaretRight } from '@element-plus/icons-vue';
import UserSelector from '@/components/UserSelector/index.vue';

interface Props {
  approvalData: {
    approvalType: number;
    specialProcessExecutorList: any[];
  };
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'update:approval-data': [value: { approvalType: number; specialProcessExecutorList: any[] }];
}>();

const activeCollapse = ref(['approval']);

// 本地数据，用于双向绑定
const localApprovalData = reactive({
  approvalType: props.approvalData?.approvalType || 0,
  specialProcessExecutorList: props.approvalData?.specialProcessExecutorList || []
});

// 处理审批类型变化
function handleApprovalTypeChange(value: number) {
  localApprovalData.approvalType = value;
  if (value === 0) {
    // 系统自动发起时清空审批人列表
    localApprovalData.specialProcessExecutorList = [];
  }
  emitUpdate();
}

// 处理审批数据变化
function handleApprovalDataChange(userIds: string[]) {
  console.log('UserSelector update:modelValue event:', userIds);
  
  // update:modelValue 事件直接返回 userId 数组
  if (Array.isArray(userIds)) {
    localApprovalData.specialProcessExecutorList = userIds;
  } else {
    localApprovalData.specialProcessExecutorList = [];
  }
  
  console.log('Processed approval data:', localApprovalData.specialProcessExecutorList);
  emitUpdate();
}

// 向父组件发送更新
function emitUpdate() {
  console.log('Emitting approval data:', {
    approvalType: localApprovalData.approvalType,
    specialProcessExecutorList: localApprovalData.specialProcessExecutorList
  });
  emit('update:approval-data', {
    approvalType: localApprovalData.approvalType,
    specialProcessExecutorList: localApprovalData.specialProcessExecutorList
  });
}

// 监听父组件数据变化，更新本地数据
watch(() => props.approvalData, (newValue) => {
  if (newValue) {
    console.log('Props approval data changed:', newValue);
    localApprovalData.approvalType = newValue.approvalType || 0;
    localApprovalData.specialProcessExecutorList = newValue.specialProcessExecutorList || [];
  }
}, { deep: true, immediate: true });

// 监听本地数据变化，确保同步
watch(localApprovalData, (newValue) => {
  console.log('Local approval data changed:', newValue);
}, { deep: true });
</script>

<style lang="scss" scoped>
@import '../../../../styles/collapse-panel.scss';

.content-section {
  margin-bottom: 20px;
}

.approval-section {
  padding: 16px 0;

  .el-form-item {
    margin-bottom: 0;
  }

  .flex {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  @media (max-width: 576px) {
    .flex {
      flex-direction: column;
      align-items: flex-start;
      gap: 8px;
    }
  }
}
</style> 