// 公告信息表单数据结构

// 供应商信息
export interface InviteSupplier {
  id: string;
  supplierName: string;
  tenantSupplierId: string;
  contactName: string;
  contactPhone: string;
}

// 标段要求项（统一结构）
export interface BidsSegment {
  requirementType: 'QUALIFICATION' | 'CONDITION' | 'FEE' | 'INVITE_SUPPLIERS';
  requirementName?: string;
  sectionId: string;
  requirementContent?: string;
  amountSet?: boolean; // 仅FEE类型使用
  feeInfo?: FeeInfo; // 仅FEE类型使用
  inviteSuppliers?: InviteSupplier[]; // 仅INVITE_SUPPLIERS类型使用
}

// 费用信息
export interface FeeInfo {
  guaranteeAmount: number;
  payAccount: string;
  payBank: string;
  openAccountBank: string;
}

// 采购时间要求
export interface PurchaseDateDemand {
  registerTimeRange: string[]; // 报名时间范围 [开始时间, 结束时间]
  auditTimeRange: string[]; // 报名时间范围 [开始时间, 结束时间]
  quoteTimeRange: string[];    // 报价时间范围 [开始时间, 结束时间]
  bidOpenTime: string;         // 开标时间
  bidOpener: string;           // 开标人
  documentObtainTimeRange: string[];           // 文件获取起止时间范围 [开始时间, 结束时间]
  bidFeePaymentTimeRange: string[]           // 标书费缴纳起止时间范围 [开始时间, 结束时间]
}

// 报价要求
export interface QuotationDemand {
  province: string;
  city: string;
  district: string;
  address: string;
  fileSubmissionAddress: string; // 文件递交地址
  tenderWay: 'ONLINE' | 'OFFLINE';
  includeTax: string; // "1" 或 "0"
  certificateType: 'SPECIAL' | 'NORMAL' | 'NONE';
}

// 联系方式
export interface ContactInfo {
  contactPerson: string;
  contactPhone: string;
  contactFixedPhone: string;
  contactEmail: string;
}

// 发布媒体项（保留原有结构）
export interface PublishMedia {
  id: string;
  serialNumber: number;
  mediaName: string;
  selected: boolean;
}

export interface AttachmentInfo {
  fileName: string
  filePath: string
}

// 后端API需要的时间数据结构
export interface ApiPurchaseDateDemand {
  registerStartTime: string;
  registerEndTime: string;
  auditStartTime: string;
  auditEndTime: string;
  quoteStartTime: string;
  quoteEndTime: string;
  bidOpenTime: string;
  bidOpeningAddress: string;
  bidOpener: string;
  bidDocPayStartTime: string;
  bidDocPayEndTime: string;
  fileObtainStartTime: string;
  fileObtainEndTime: string;
}

// 真实API数据结构
export interface AnnouncementApiData {
  noticeTitle: string;
  noticeTemplateId: string;
  projectId: string;
  noticeContent: string;
  purchaseDateDemand: ApiPurchaseDateDemand; // 使用后端API格式
  quotationDemand: QuotationDemand;
  bidsSegments: BidsSegment[];
  quotationNotice: string;
  evaluationMethod: 'COMPREHENSIVE' | 'LOWEST_PRICE' | 'COMPREHENSIVE_SCORE' | 'HIGHEST_PRICE';
  contactInfo: ContactInfo;
  approvalType: number;
  specialProcessExecutorList: any[];
}

// 组件内部使用的表单数据结构（保持向后兼容）
export interface AnnouncementFormData {
  id: string;
  // 公告信息
  announcementInfo: {
    title: string;
    template: string;
    content?: string; // 模板内容
  };

  // 采购时间要求
  purchaseDateDemand: PurchaseDateDemand;

  // 报价要求
  quotationDemand: QuotationDemand;

  // 标段要求（按标段和类型组织）
  bidsSegments: BidsSegment[];

  // 评审规则
  evaluationMethod: 'COMPREHENSIVE' | 'LOWEST_PRICE' | 'COMPREHENSIVE_SCORE' | 'HIGHEST_PRICE';

  // 招标审核状态
  noticeStatus: 'TO_APPROVE' | 'APPROVING' | 'APPROVE' | 'APPROVE_REJECT' | 'APPROVE_REVOKE';

  // 供应商报价须知
  quotationNotice: string;

  // 联系方式
  contactInfo: ContactInfo;

  // 发布媒体
  publishMedia: PublishMedia[];

  // 采购公告附件
  attachmentInfos: AttachmentInfo[];
  
  // 审批数据
  approvalData: {
    approvalType: number; // 0: 系统自动发起, 1: 指定审批人
    specialProcessExecutorList: any[]; // 指定审批人列表
  };
}

// 模板选项
export interface TemplateOption {
  id: string;
  templateName: string;
}

// 评审规则选项
export interface EvaluationRuleOption {
  label: string;
  value: 'COMPREHENSIVE' | 'LOWEST_PRICE' | 'COMPREHENSIVE_SCORE' | 'HIGHEST_PRICE';
}

// 标段信息
export interface LotInfo {
  id: string;
  sectionName: string;
}

// 数据转换工具类型
export interface DataTransformer {
  // 将组件表单数据转换为API数据
  formToApi: (formData: AnnouncementFormData, projectId: string) => AnnouncementApiData;
  // 将API数据转换为组件表单数据
  apiToForm: (apiData: AnnouncementApiData) => AnnouncementFormData;
}
