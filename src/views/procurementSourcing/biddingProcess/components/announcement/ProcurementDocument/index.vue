<template>
  <el-form
    ref="mainFormRef"
    :model="announcementForm"
    :rules="formRules"
    label-width="140px"
  >
    <div class="procurement-project-container">
      <!-- 左侧锚点导航 -->
      <AnchorNavigation
        :anchor-list="anchorList"
        :active-anchor="activeAnchor"
        :slider-style="sliderStyle"
        :is-slider-ready="isSliderReady"
        @scroll-to-section="scrollToSection"
      />

      <!-- 右侧内容区域 -->
      <div class="content-area">
        <!-- 公告信息 -->
        <AnnouncementInfo
          :form-data="announcementForm.announcementInfo"
          :template-options="templateOptions"
          :is-invite-mode="isInviteMode"
          @update:title="announcementForm.announcementInfo.title = $event"
          @update:template="announcementForm.announcementInfo.template = $event"
        />

        <!-- 资格要求 -->
        <QualificationRequirements
          :is-invite-mode="isInviteMode"
          :lot-list="lotList"
          :active-lot-id="lotManagement.activeQualificationLotId.value"
          :qualification-data="lotManagement.getCurrentQualificationData.value"
          :certificate-options="certificateOptions"
          :certificate-loading="certificateLoading"
          @lot-click="lotManagement.handleQualificationLotClick"
          @add-item="lotManagement.addQualificationItem"
          @remove-item="lotManagement.removeQualificationItem"
          @certificate-search="handleCertificateSearch"
          @certificate-change="handleCertificateChange"
        />

        <!-- 报价要求 -->
        <QuotationDemand
          :form-data="{
            tenderWay: announcementForm.quotationDemand.tenderWay,
            bidOpeningAddress: announcementForm.purchaseDateDemand.bidOpeningAddress,
            fileSubmissionAddress: announcementForm.quotationDemand.fileSubmissionAddress,
            address: announcementForm.quotationDemand.address,
            includeTax: announcementForm.quotationDemand.includeTax,
            certificateType: announcementForm.quotationDemand.certificateType
          }"
          :sourcing-type="sourcingType"
          :selected-area-path="selectedAreaPath"
          :address-options="addressOptions"
          :certificate-type-options="certificateTypeOptions"
          @update:tender-way="announcementForm.quotationDemand.tenderWay = $event"
          @update:bid-opening-address="announcementForm.purchaseDateDemand.bidOpeningAddress = $event"
          @update:file-submission-address="announcementForm.quotationDemand.fileSubmissionAddress = $event"
          @update:address="announcementForm.quotationDemand.address = $event"
          @update:include-tax="announcementForm.quotationDemand.includeTax = $event"
          @update:certificate-type="announcementForm.quotationDemand.certificateType = $event"
          @area-change="handleAreaChange"
        />

        <!-- 报价响应条件 -->
        <QuotationConditions
          :is-invite-mode="isInviteMode"
          :lot-list="lotList"
          :active-lot-id="lotManagement.activeConditionLotId.value"
          :condition-data="lotManagement.getCurrentConditionData.value"
          @lot-click="lotManagement.handleConditionLotClick"
          @add-item="lotManagement.addConditionItem"
          @remove-item="lotManagement.removeConditionItem"
        />

        <!-- 采购时间要求 -->
        <TimeRequirements
          :form-data="announcementForm.purchaseDateDemand"
          :is-invite-mode="isInviteMode"
          :sourcing-type="sourcingType"
          :project-detail="projectDetail"
          @update:register-time-range="announcementForm.purchaseDateDemand.registerTimeRange = $event"
          @update:audit-time-range="announcementForm.purchaseDateDemand.auditTimeRange = $event"
          @update:quote-time-range="announcementForm.purchaseDateDemand.quoteTimeRange = $event"
          @update:bid-open-time="announcementForm.purchaseDateDemand.bidOpenTime = $event"
          @update:document-obtain-time-range="announcementForm.purchaseDateDemand.documentObtainTimeRange = $event"
          @update:bid-fee-payment-time-range="announcementForm.purchaseDateDemand.bidFeePaymentTimeRange = $event"
        />

        <!-- 评审规则 -->
        <EvaluationRules
          :evaluation-method="announcementForm.evaluationMethod"
          :evaluation-rule-options="evaluationRuleOptions"
          @update:evaluation-method="announcementForm.evaluationMethod = $event"
        />

        <!-- 费用设置 -->
        <FeeSettings
          :lot-list="lotList"
          :active-lot-id="lotManagement.activeFeeLotId.value"
          :fee-data="lotManagement.getCurrentFeeData.value"
          @lot-click="lotManagement.handleFeeLotClick"
          @update:amount-set="updateFeeAmountSet"
          @update:guarantee-amount="updateFeeGuaranteeAmount"
          @update:pay-bank="updateFeePayBank"
          @update:pay-account="updateFeePayAccount"
          @update:open-account-bank="updateFeeOpenAccountBank"
        />

        <!-- 供应商报价须知 -->
        <QuotationNotice
          :quotation-notice="announcementForm.quotationNotice"
          @update:quotation-notice="announcementForm.quotationNotice = $event"
        />

        <!-- 联系方式 -->
        <ContactInfo
          :contact-info="announcementForm.contactInfo"
          @update:contact-person="announcementForm.contactInfo.contactPerson = $event"
          @update:contact-phone="announcementForm.contactInfo.contactPhone = $event"
          @update:contact-fixed-phone="announcementForm.contactInfo.contactFixedPhone = $event"
          @update:contact-email="announcementForm.contactInfo.contactEmail = $event"
        />

        <!-- 发布媒体 -->
        <PublishMedia
          :is-invite-mode="isInviteMode"
          :publish-media="announcementForm.publishMedia"
          :selected-media="checkboxGroup1"
          @update:selected-media="checkboxGroup1 = $event"
        />

        <!-- 邀请供应商 -->
        <InviteSuppliers
          :is-invite-mode="isInviteMode"
          :lot-list="lotList"
          :active-lot-id="lotManagement.activeInviteLotId.value"
          :invite-suppliers="lotManagement.getCurrentInviteSuppliers.value"
          @lot-click="lotManagement.handleInviteLotClick"
          @open-supplier-modal="openSupplierModal"
          @remove-supplier="lotManagement.removeInviteSupplier"
        />

        <!-- 采购公告附件/邀请函附件 -->
        <AttachmentUpload
          :is-invite-mode="isInviteMode"
          :attachment-infos="attachmentInfos"
          @update:attachment-infos="attachmentInfos = $event"
          @upload-change="handelUploadFile"
        />

        <!-- 审批设置 -->
        <Approval
          :approval-data="announcementForm.approvalData"
          @update:approval-data="handleApprovalDataUpdate"
        />
        

      </div>
    </div>
    <div class="form-actions-wrapper">
      <div class="form-actions">
        <el-button
          type="primary"
          @click="handleSubmit"
          :loading="btnLoading"
        >
          {{ hasNotice && !isApproved ? '查看公告' : '生成公告' }}
        </el-button>
        <!--        <el-button @click="handleCancel">取消</el-button>-->
      </div>
    </div>

    <!-- 供应商选择模态框 -->
    <SupplierSelectModal
      v-if="supplierModalVisible"
      v-model="supplierModalVisible"
      :selected-suppliers="currentInviteSuppliers"
      @confirm="handleSupplierSelect"
    />
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, computed, watch } from 'vue';
import { useRoute } from 'vue-router';
import { ElMessage } from 'yun-design';
import type { BidsSegment, TemplateOption, LotInfo, InviteSupplier } from './types';
import { getAnnouncementData, getTemplateDetail, getTemplateList } from '@/views/procurementSourcing/biddingProcess/api';
import { fetchList as fetchCertificateList } from '@/api/lowcode/base-certificate/index';
import {
  formToApiData,
  createBidsSegment,
  validateFormData,
  getEvaluationRuleOptions,
  getCertificateTypeOptions,
  ensureCompleteBidsSegments,
} from './dataTransformer';
import { Session } from '@/utils/storage';
import {
  templateContentFormatter,
  transformApiDataForTemplate,
} from '@/views/procurementSourcing/biddingProcess/components/announcement/ProcurementDocument/templateFormatter';
import { convertDetailToFormData, convertAreaToPath, convertAttachmentsForUpload, validateConvertedData } from './detailDataConverter';
import SupplierSelectModal from './SupplierSelectModal.vue';
import { useBiddingStore } from '@/views/procurementSourcing/biddingProcess/stores';
import {useUserInfo} from "@/stores/userInfo";
import {storeToRefs} from "pinia";

// 导入新的组件
import AnchorNavigation from './components/AnchorNavigation.vue';
import AnnouncementInfo from './components/AnnouncementInfo.vue';
import QualificationRequirements from './components/QualificationRequirements.vue';
import QuotationDemand from './components/QuotationDemand.vue';
import QuotationConditions from './components/QuotationConditions.vue';
import TimeRequirements from './components/TimeRequirements.vue';
import EvaluationRules from './components/EvaluationRules.vue';
import FeeSettings from './components/FeeSettings.vue';
import QuotationNotice from './components/QuotationNotice.vue';
import ContactInfo from './components/ContactInfo.vue';
import PublishMedia from './components/PublishMedia.vue';
import InviteSuppliers from './components/InviteSuppliers.vue';
import AttachmentUpload from './components/AttachmentUpload.vue';
import Approval from './components/Approval.vue';

// 导入composables
import { useAnchorNavigation } from './composables/useAnchorNavigation';
import { useLotManagement } from './composables/useLotManagement';

const userInfoStore = useUserInfo();
const { userInfos } = storeToRefs(userInfoStore);

// 路由参数检测
const route = useRoute();
const isInviteMode = computed(() => projectDetail.value?.inviteMethod === 'INVITE');

const biddingStore = useBiddingStore();
const noticeId = computed(() => {
  return route.query.noticeId || biddingStore?.noticeId || biddingStore?.projectDetail?.tenderNotice?.id;
});
const projectId = computed(() => biddingStore?.projectId);
const projectDetail = computed(() => biddingStore?.projectDetail);
const sourcingType = computed(() => projectDetail.value?.sourcingType);
const hasNotice = computed(() => !!noticeId.value);
const isApproved = computed(() => {
  return ['TO_APPROVE', 'APPROVE_REJECT', 'APPROVE_REVOKE'].includes(announcementForm.noticeStatus);
});

// 使用锚点导航composable
const anchorNavigation = useAnchorNavigation(isInviteMode);
const {
  activeAnchor,
  sliderStyle,
  isSliderReady,
  anchorList,
  scrollToSection,
  initAnchorNavigation,
  cleanupAnchorNavigation,
} = anchorNavigation;

const btnLoading = ref(false);
const emits = defineEmits(['success']);
const checkboxGroup1 = ref([]);
const attachmentInfos = ref([]);

// 供应商选择相关
const supplierModalVisible = ref(false);
const currentInviteSuppliers = ref<InviteSupplier[]>([]);

// 证书选择相关
const certificateOptions = ref([]);
const certificateLoading = ref(false);
const allCertificates = ref([]); // 存储所有证书数据

// 折叠面板控制已移到各个子组件中

// 模板选项
const templateOptions = ref<TemplateOption[]>([]);

// 为每个API组件创建响应式变量
const addressOptions = ref([]);

// 地区选择器的值
const selectedAreaPath = ref([]);

// 主表单引用
const mainFormRef = ref();

// 标段列表
const lotList = ref<LotInfo[]>([]);

// 评审规则选项
const evaluationRuleOptions = getEvaluationRuleOptions();

// 发票要求选项
const certificateTypeOptions = getCertificateTypeOptions();

// 表单校验规则
const formRules = computed(() => {
  const baseRules = {
    'announcementInfo.title': [
      { required: true, message: isInviteMode.value ? '请输入邀请函标题' : '请输入公告标题', trigger: 'blur' },
      { message: '不能超过50个字符', trigger: 'blur', max: 50 }
    ],
    'announcementInfo.template': [{ required: true, message: '请选择公告模版', trigger: 'change' }],
    'quotationDemand.tenderWay': [{ required: true, message: '请选择开标方式', trigger: 'change' }],
    'purchaseDateDemand.bidOpeningAddress': [{ required: true, message: '请输入开标地点', trigger: 'blur' }],
    'quotationDemand.fileSubmissionAddress': [{ required: true, message: '请输入文件递交地址', trigger: 'blur' }],
    'quotationDemand.address': [
      { required: true, message: '请输入详细地址', trigger: 'blur' },
      { message: '不能超过100个字符', trigger: 'blur', max: 100 }
    ],
    'quotationDemand.includeTax': [{ required: true, message: '请选择报价含税', trigger: 'change' }],
    'quotationDemand.certificateType': [{ required: true, message: '请选择发票要求', trigger: 'change' }],
  };

  // 只在发标模式下且需要资格预审时添加时间范围验证
  if (!isInviteMode.value && projectDetail.value?.preQualification === 1) {
    baseRules['purchaseDateDemand.registerTimeRange'] = [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择报名起止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ];
    baseRules['purchaseDateDemand.auditTimeRange'] = [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择资质预审起止时间'));
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ];
  }

  // 发标模式下的报价时间和开标时间验证（不管是否需要资格预审都要验证）
  if (!isInviteMode.value) {
    baseRules['purchaseDateDemand.quoteTimeRange'] = [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择报价起止时间'));
          } else {
            // 当报价时间范围改变时，重新验证开标时间
            nextTick(() => {
              if (mainFormRef.value && announcementForm.purchaseDateDemand.bidOpenTime) {
                mainFormRef.value.validateField('purchaseDateDemand.bidOpenTime');
              }
            });
            callback();
          }
        },
        trigger: 'change',
      },
    ];
    baseRules['purchaseDateDemand.bidOpenTime'] = [
      { required: true, message: '请选择开标时间', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback();
            return;
          }

          const quoteTimeRange = announcementForm.purchaseDateDemand.quoteTimeRange;
          if (quoteTimeRange && quoteTimeRange[1]) {
            const quoteEndTime = new Date(quoteTimeRange[1]);
            const bidOpenTime = new Date(value);

            if (bidOpenTime < quoteEndTime) {
              callback(new Error('开标时间必须大于等于报价截止时间'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ];
  }

  // 通用验证规则
  baseRules['evaluationMethod'] = [{ required: true, message: '请选择评审规则', trigger: 'change' }];

  // 邀请函模式下的时间验证规则
  if (isInviteMode.value) {
    baseRules['purchaseDateDemand.quoteTimeRange'] = [
      {
        required: true,
        validator: (rule, value, callback) => {
          if (!value || !Array.isArray(value) || value.length !== 2 || !value[0] || !value[1]) {
            callback(new Error('请选择报价起止时间'));
          } else {
            // 当报价时间范围改变时，重新验证开标时间
            nextTick(() => {
              if (mainFormRef.value && announcementForm.purchaseDateDemand.bidOpenTime) {
                mainFormRef.value.validateField('purchaseDateDemand.bidOpenTime');
              }
            });
            callback();
          }
        },
        trigger: 'change',
      },
    ];
    baseRules['purchaseDateDemand.bidOpenTime'] = [
      { required: true, message: '请选择开标时间', trigger: 'change' },
      {
        validator: (rule, value, callback) => {
          if (!value) {
            callback();
            return;
          }

          const quoteTimeRange = announcementForm.purchaseDateDemand.quoteTimeRange;
          if (quoteTimeRange && quoteTimeRange[1]) {
            const quoteEndTime = new Date(quoteTimeRange[1]);
            const bidOpenTime = new Date(value);

            if (bidOpenTime < quoteEndTime) {
              callback(new Error('开标时间必须大于等于报价截止时间'));
            } else {
              callback();
            }
          } else {
            callback();
          }
        },
        trigger: 'change',
      },
    ];
  }

  baseRules['contactInfo.contactPerson'] = [{ required: true, message: '请输入采购联系人', trigger: 'blur' }];
  baseRules['contactInfo.contactPhone'] = [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码格式', trigger: 'blur' },
  ];
  baseRules['contactInfo.contactEmail'] = [{ type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }];
  
  // 审批相关验证规则 - 只在选择指定审批人时验证
  if (announcementForm.approvalData?.approvalType === 1) {
    baseRules['approvalData.specialProcessExecutorList'] = [{ 
      required: true, 
      message: '请选择审批人', 
      trigger: 'change'
    }];
  }

  return baseRules;
});

const initForm = () => {
  return {
    announcementInfo: {
      title: '',
      template: '',
      content: '',
    },
    purchaseDateDemand: {
      registerTimeRange: ['', ''], // 报名时间范围
      auditTimeRange: ['', ''], // 报名时间范围
      quoteTimeRange: ['', ''], // 报价时间范围
      documentObtainTimeRange: ['', ''], // 文件获取时间范围
      bidFeePaymentTimeRange: ['', ''], // 标书费缴纳时间范围
      bidOpenTime: '', // 开标时间
      bidOpener: '', // 开标人
      bidOpeningAddress: '', // 开标地点
    },
    quotationDemand: {
      province: '',
      city: '',
      district: '',
      address: '',
      fileSubmissionAddress: '',
      includeTax: 1,
      certificateType: 'SPECIAL',
      tenderWay: 'ONLINE',
      bidOpeningAddress: '',
    },
    bidsSegments: [],
    evaluationMethod: 'LOWEST_PRICE',
    noticeStatus: 'TO_APPROVE',
    quotationNotice: '',
    contactInfo: {
      contactPerson: '',
      contactPhone: '',
      contactFixedPhone: '',
      contactEmail: '',
    },
    publishMedia: [
      {
        id: '1',
        serialNumber: 1,
        mediaName: '中国采购与招标网',
        selected: false,
      },
      {
        id: '2',
        serialNumber: 2,
        mediaName: '中国招标投标公共服务平台',
        selected: false,
      },
    ],
    attachmentInfos: [],
    approvalData: {
      approvalType: 0, // 0: 系统自动发起, 1: 指定审批人
      specialProcessExecutorList: [], // 指定审批人列表
    },
  };
};

// 初始化表单数据
const announcementForm = reactive<any>({
  ...initForm(),
});

// 使用标段管理composable
const lotManagement = useLotManagement(
  computed(() => announcementForm.bidsSegments),
  lotList
);

// 费用设置更新函数
const updateFeeAmountSet = (value: boolean) => {
  const feeData = lotManagement.getCurrentFeeData.value;
  if (feeData) {
    feeData.amountSet = value;
  }
};

const updateFeeGuaranteeAmount = (value: string) => {
  const feeData = lotManagement.getCurrentFeeData.value;
  if (feeData && feeData.feeInfo) {
    feeData.feeInfo.guaranteeAmount = Number(value);
  }
};

const updateFeePayBank = (value: string) => {
  const feeData = lotManagement.getCurrentFeeData.value;
  if (feeData && feeData.feeInfo) {
    feeData.feeInfo.payBank = value;
  }
};

const updateFeePayAccount = (value: string) => {
  const feeData = lotManagement.getCurrentFeeData.value;
  if (feeData && feeData.feeInfo) {
    feeData.feeInfo.payAccount = value;
  }
};

const updateFeeOpenAccountBank = (value: string) => {
  const feeData = lotManagement.getCurrentFeeData.value;
  if (feeData && feeData.feeInfo) {
    feeData.feeInfo.openAccountBank = value;
  }
};

// 处理审批数据更新
const handleApprovalDataUpdate = (approvalData: any) => {
  console.log('Main page received approval data update:', approvalData);
  announcementForm.approvalData = approvalData;
};

// 处理地区选择变化
const handleAreaChange = (value: any) => {
  if (value && value.length >= 3) {
    // 根据选中的路径，从addressOptions中找到对应的省市区名称
    const findAreaNames = (options: any[], codes: string[], level: number = 0): string[] => {
      if (level >= codes.length || level >= 3) return [];

      const currentCode = codes[level];
      const currentOption = options.find((option) => option.value === currentCode);

      if (!currentOption) return [];

      const result = [currentOption.label];

      if (level < 2 && currentOption.children && codes[level + 1]) {
        const childNames = findAreaNames(currentOption.children, codes, level + 1);
        result.push(...childNames);
      }

      return result;
    };

    const areaNames = findAreaNames(addressOptions.value, value);

    if (areaNames.length >= 3) {
      announcementForm.quotationDemand.province = areaNames[0];
      announcementForm.quotationDemand.city = areaNames[1];
      announcementForm.quotationDemand.district = areaNames[2];
    }
  } else {
    // 清空选择
    announcementForm.quotationDemand.province = '';
    announcementForm.quotationDemand.city = '';
    announcementForm.quotationDemand.district = '';
  }
};

// 为每个API组件创建获取数据的方法
function fetchAddressOptions(options, query, formData) {
  let requestUrl = 'https://oss-public.yunlizhi.cn/frontend/yun-design/area.json';
  const method = 'GET';

  // 构建请求参数
  const requestParams = {};

  const token = Session.getToken();
  const tenantId = Session.getTenant();
  // 构建请求配置
  const requestConfig = {
    method,
    headers: {
      'Content-Type': 'application/json',
      'TENANT-ID': tenantId,
      AUTHORIZATION: `Bearer ${token}`,
    },
  };

  // 如果是GET请求，将参数附加到URL
  if (method === 'GET') {
    const queryParams = new URLSearchParams();
    Object.entries(requestParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        queryParams.append(key, String(value));
      }
    });
    const queryString = queryParams.toString();
    if (queryString) {
      requestUrl += (requestUrl.includes('?') ? '&' : '?') + queryString;
    }
  } else {
    // 非GET请求，将参数放在body中
    requestConfig.body = JSON.stringify(requestParams);
  }

  // 发送请求并处理响应
  fetch(requestUrl, requestConfig)
    .then((response) => {
      if (!response.ok) {
        throw new Error('API request failed');
      }
      return response.json();
    })
    .then((data) => {
      // 根据配置的路径获取数据
      let result = data;

      // 处理数据
      const processData = (items) => {
        return items.map((item) => ({
          label: item['areaName'],
          value: item['areaCode'],

          children: item['children'] ? processData(item['children']) : undefined,
        }));
      };

      options.value = Array.isArray(result) ? processData(result) : [];
    })
    .catch((error) => {
      console.error('Failed to fetch API options:', error);
      options.value = [];
    });
}

// 初始化时获取所有API数据
fetchAddressOptions(addressOptions);

// 根据默认的省市区信息设置级联选择器的值
const initializeAreaSelection = () => {
  const findAreaCodes = (options: any[], targetNames: string[], level: number = 0): string[] => {
    if (level >= targetNames.length || level >= 3) return [];

    const targetName = targetNames[level];
    const currentOption = options.find((option) => option.label === targetName);

    if (!currentOption) return [];

    const result = [currentOption.value];

    if (level < 2 && currentOption.children && targetNames[level + 1]) {
      const childCodes = findAreaCodes(currentOption.children, targetNames, level + 1);
      result.push(...childCodes);
    }

    return result;
  };

  // 等待地址数据加载完成后设置默认值
  const checkAndSetDefault = () => {
    if (addressOptions.value.length > 0) {
      const defaultNames = [
        announcementForm.quotationDemand.province,
        announcementForm.quotationDemand.city,
        announcementForm.quotationDemand.district,
      ];

      if (defaultNames.every((name) => name)) {
        const codes = findAreaCodes(addressOptions.value, defaultNames);
        if (codes.length >= 3) {
          selectedAreaPath.value = codes;
        }
      }
    } else {
      // 如果数据还没加载完，延迟重试
      setTimeout(checkAndSetDefault, 100);
    }
  };

  checkAndSetDefault();
};

const handelUploadFile = (file) => {
  announcementForm.attachmentInfos = attachmentInfos.value.map((item) => {
    return {
      fileName: item.name,
      filePath: item.url,
    };
  });
};

// 邀请供应商操作
function openSupplierModal() {
  currentInviteSuppliers.value = lotManagement.getCurrentInviteSuppliers.value;
  console.log(currentInviteSuppliers.value);
  supplierModalVisible.value = true;
}

function handleSupplierSelect(suppliers: InviteSupplier[]) {
  lotManagement.updateInviteSuppliers(suppliers);
  currentInviteSuppliers.value = suppliers;
}

// 证书相关方法
async function fetchCertificates(query = '') {
  try {
    certificateLoading.value = true;
    const searchParams = query ? { certName: query } : {};
    const response = await fetchCertificateList(searchParams, { current: 1, size: 100 });
    certificateOptions.value = response.data?.records || [];
    // 同时更新全部证书列表，用于选择后的数据匹配
    if (!query) {
      allCertificates.value = certificateOptions.value;
    }
  } catch (error) {
    console.error('获取证书列表失败:', error);
    certificateOptions.value = [];
  } finally {
    certificateLoading.value = false;
  }
}

async function handleCertificateSearch(query: string) {
  await fetchCertificates(query);
}

function handleCertificateChange(value: string, row: any) {
  // 从当前选项或全部证书中查找选中的证书
  let selectedCert = certificateOptions.value.find((cert) => cert.certName === value);
  if (!selectedCert) {
    selectedCert = allCertificates.value.find((cert) => cert.certName === value);
  }

  if (selectedCert) {
    // 选中证书后，如果补充说明为空，则填入证书的描述
    if (!row.requirementContent) {
      row.requirementContent = selectedCert.description || '';
    }
  }
}

// 锚点导航函数已移到composable中

// 表单操作
async function handleSubmit() {
  try {
    // 如果是查看模式，直接显示已有数据
    if (hasNotice.value && !isApproved.value) {
      // 查看公告：直接使用现有数据，不需要重新获取模板
      const apiData = formToApiData(announcementForm, projectId.value);
      emits('success', {
        ...apiData,
      });
      return;
    }

    // 生成公告模式：需要进行表单校验和模板处理
    // 首先进行 Element Plus 表单校验
    if (!mainFormRef.value) {
      ElMessage.error('表单引用未找到');
      return;
    }

    const isValid = await mainFormRef.value.validate().catch(() => false);
    if (!isValid) {
      ElMessage.error('请完善必填信息');
      return;
    }

    // 然后进行自定义数据校验（不能通过 Element Plus 校验的数据）
    const validation = validateFormData(announcementForm, lotList.value, isInviteMode.value);
    if (!validation.isValid) {
      // 显示第一个校验错误
      const firstError = validation.errors[0];
      ElMessage.error(firstError || '数据校验失败，请检查表单信息');
      return;
    }

    // 转换为API数据格式
    const apiData = formToApiData(announcementForm, projectId.value);

    btnLoading.value = true;

    // 生成公告时需要获取模板详情并替换变量
    if (announcementForm.announcementInfo.template) {
      const { data: templateDetail } = await getTemplateDetail(announcementForm.announcementInfo.template);
      announcementForm.announcementInfo.content = templateDetail.content;
    }

    // 将 API 数据转换为适合模板解析的嵌套循环格式
    const templateData = transformApiDataForTemplate(apiData, lotList.value);

    // 添加枚举转换，方便在模板中显示中文
    const enumTransforms = {
      // 发票要求枚举转换
      certificateTypeText: getCertificateTypeOptions().find((opt) => opt.value === apiData.quotationDemand?.certificateType)?.label || '-',

      // 评审规则枚举转换
      evaluationMethodText: getEvaluationRuleOptions().find((opt) => opt.value === apiData.evaluationMethod)?.label || '-',

      // 报价含税枚举转换
      includeTaxText: apiData.quotationDemand?.includeTax === 1 ? '是' : '否',

      // 在quotationDemand对象中也添加转换后的字段
      quotationDemand: {
        ...apiData.quotationDemand,
        certificateTypeText: getCertificateTypeOptions().find((opt) => opt.value === apiData.quotationDemand?.certificateType)?.label || '-',
        includeTaxText: apiData.quotationDemand?.includeTax === 1 ? '是' : '否',
      },

      // 保证金收取枚举转换 - 更新费用设置数组
      feeSettings:
        templateData.feeSettings?.map((feeSection) => ({
          ...feeSection,
          amountSetText: feeSection.amountSet ? '线下收取保证金' : '不收保证金',
        })) || [],
    };

    apiData.noticeContent = templateContentFormatter(
      announcementForm.announcementInfo.content,
      {
        ...templateData,
        // 添加枚举转换后的字段
        ...enumTransforms,
      },
      {
        defaultValue: '-',
        keepUndefinedVariables: false,
        removeEmptyLoops: true,
        removeEmptyTags: true, // 去除多余的空标签
        debug: true, // 开启调试模式
      }
    );

    // 调用保存接口
    // const {data} = await saveAnnouncementData(apiData);

    // apiData.id = data
    emits('success', {
      ...apiData,
    });
    // 显示成功提示
    // ElMessage.success('保存成功');

    // 这里可以添加页面跳转逻辑
  } catch (error) {
    ElMessage.error('保存失败，请稍后重试');
  } finally {
    btnLoading.value = false;
  }
}

const getSrmProcurement = async () => {
  try {
    lotList.value = projectDetail.value.projectSectionList || [];

    const user = userInfos.value.user
    // 初始化各个模块的默认选中标段
    lotManagement.initializeLotSelection(lotList.value);

    const data = projectDetail.value;
    announcementForm.purchaseDateDemand.registerTimeRange = [data.registerStartTime, data.registerEndTime];
    announcementForm.purchaseDateDemand.auditTimeRange = [data.preReviewStartTime, data.preReviewEndTime];
    announcementForm.purchaseDateDemand.quoteTimeRange = [data.quoteStartTime, data.quoteEndTime];
    announcementForm.purchaseDateDemand.bidOpenTime = data.bidOpenTime;
    announcementForm.purchaseDateDemand.documentObtainTimeRange = [data.tenderFileGainStartTime, data.tenderFileGainEndTime];
    announcementForm.purchaseDateDemand.bidFeePaymentTimeRange = [data.tenderFeePayStartTime, data.tenderFeePayEndTime];
    announcementForm.contactInfo.contactPerson = user.name || '';
    announcementForm.contactInfo.contactPhone = user.phone || '';

    // 确保审批数据被正确初始化
    if (!announcementForm.approvalData) {
      announcementForm.approvalData = {
        approvalType: 0,
        specialProcessExecutorList: []
      };
    }

    // 初始化每个标段的默认数据
    initializeBidsSegments();
  } catch (e) {
    console.log(e);
  }
};

// 初始化标段数据
function initializeBidsSegments() {
  const lotIds = lotList.value.map((lot) => lot.id);

  // 确保每个标段都有完整的数据结构
  announcementForm.bidsSegments = ensureCompleteBidsSegments(announcementForm.bidsSegments, lotIds, isInviteMode.value);

  // 如果是全新初始化，添加默认数据
  if (announcementForm.bidsSegments.length === 0) {
    const segments: BidsSegment[] = [];

    lotList.value.forEach((lot) => {
      // 为每个标段添加默认的资格要求
      segments.push(
        createBidsSegment('QUALIFICATION', lot.id, {
          requirementName: '营业执照',
          requirementContent: '',
        })
      );

      // 为每个标段添加默认的响应条件
      segments.push(
        createBidsSegment('CONDITION', lot.id, {
          requirementName: '*资格条件',
          requirementContent: '',
        })
      );

      // 为每个标段添加默认的费用设置
      segments.push(
        createBidsSegment('FEE', lot.id, {
          amountSet: true,
          feeInfo: {
            guaranteeAmount: 0,
            payAccount: '',
            payBank: '',
            openAccountBank: '',
          },
        })
      );

      // 如果是邀请函模式，为每个标段添加默认的邀请供应商
      if (isInviteMode.value) {
        segments.push(
          createBidsSegment('INVITE_SUPPLIERS', lot.id, {
            inviteSuppliers: [],
          })
        );
      }
    });

    announcementForm.bidsSegments = segments;
  }
}

const init = async () => {
  try {
    // 先获取项目信息和标段列表
    // 如果有ID，获取详情数据并转换为表单格式
    if (noticeId.value) {
      const { data } = await getAnnouncementData(noticeId.value);
      await getSrmProcurement();
      console.log('详情数据:', data);

      // 使用新的转换工具将详情数据转换为表单数据
      const formData = convertDetailToFormData(data, lotList.value);

      // 验证转换后的数据
      const validation = validateConvertedData(formData, lotList.value, isInviteMode.value);
      if (!validation.isValid) {
        console.warn('转换后的数据存在问题:', validation.errors);
      }

      // 更新表单数据
      Object.assign(announcementForm, formData);

      // 更新附件信息
      if (formData.attachmentInfos && formData.attachmentInfos.length > 0) {
        attachmentInfos.value = convertAttachmentsForUpload(formData.attachmentInfos);
      }

      // 等待地址数据加载完成后设置地区选择器
      setTimeout(() => {
        if (formData.quotationDemand.province && formData.quotationDemand.city && formData.quotationDemand.district) {
          const areaPath = convertAreaToPath(
            formData.quotationDemand.province,
            formData.quotationDemand.city,
            formData.quotationDemand.district,
            addressOptions.value
          );
          if (areaPath.length === 3) {
            selectedAreaPath.value = areaPath;
          }
        }
      }, 500); // 延迟500ms确保地址数据已加载
    } else {
      await getSrmProcurement();
      // 如果是新建，初始化默认数据
      // initializeBidsSegments();
    }

    // 获取模板列表
    const { data } = await getTemplateList({
      type: 'ANNOUNCEMENT',
    });
    templateOptions.value = data.records || [];

    // 获取默认证书列表
    await fetchCertificates();
  } catch (e) {
    console.error('初始化失败:', e);
  }
};

// 初始化
onMounted(() => {
  init();
  // 初始化地区选择器
  initializeAreaSelection();
  // 初始化锚点导航
  initAnchorNavigation();
});

// 组件卸载时移除滚动监听器
onUnmounted(() => {
  cleanupAnchorNavigation();
});
</script>

<style lang="scss" scoped>
@import '../../../styles/collapse-panel.scss';

.procurement-project-container {
  display: flex;
  gap: 20px;
  padding: 20px;

  // 响应式布局：小屏幕时改为垂直布局
  @media (max-width: 768px) {
    flex-direction: column;
    gap: 16px;
    padding: 16px;
  }

  // 左侧锚点导航
  .anchor-navigation {
    width: 120px; // 恢复固定宽度
    flex-shrink: 0; // 防止在空间不足时被压缩
    position: sticky;
    border-left: 1px solid #e6eaf0;
    top: 20px;
    height: fit-content;

    // 小屏幕时的样式调整
    @media (max-width: 768px) {
      width: 100%;
      position: relative;
      border-left: none;
      border-bottom: 1px solid #e6eaf0;
      padding-bottom: 12px;
      margin-bottom: 12px;

      .nav-list {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .nav-slider {
          display: none; // 小屏幕时隐藏滑块
        }

        .nav-item {
          padding: 4px 8px;
          border: 1px solid #e6eaf0;
          border-radius: 4px;
          font-size: 11px;

          &.active {
            border-color: var(--Color-Primary-color-primary, #0069ff);
            background-color: rgba(0, 105, 255, 0.1);
          }
        }
      }
    }

    .nav-list {
      list-style: none;
      padding: 0;
      margin: 0;
      position: relative; // 为滑块定位提供上下文

      // 高亮滑块
      .nav-slider {
        position: absolute;
        left: -1px;
        width: 1px;
        height: 16px;
        background-color: #0069ff;
        transition: transform 0.2s ease-out;
        will-change: transform;
        z-index: 1;

        // 淡入效果
        &[v-show] {
          transition: transform 0.2s ease-out, opacity 0.3s ease;
        }
      }

      .nav-item {
        padding: 2px 12px;
        cursor: pointer;
        border-radius: 4px;
        transition: all 0.3s;
        color: var(--Light-Light-el-text-color-primary, #1c2026);
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        position: relative; // 确保层级关系

        &.active {
          color: var(--Color-Primary-color-primary, #0069ff);
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 600;
          line-height: 20px;
        }
      }
    }
  }

  // 右侧内容区域
  .content-area {
    flex: 1;
    min-width: 0; // 关键！防止内容溢出，确保能够收缩
    padding-right: 20px;

    @media (max-width: 768px) {
      padding-right: 0;
    }

    // 确保内部表单元素也能正确自适应
    .section-form {
      // 响应式表单布局
      .el-row {
        @media (max-width: 768px) {
          .el-col {
            &:not(:last-child) {
              margin-bottom: 16px;
            }
          }
        }

        @media (max-width: 576px) {
          .el-col {
            &[span='12'],
            &[span='8'] {
              span: 24 !important; // 强制全宽
            }
          }
        }
      }

      // 表格容器响应式
      .el-table {
        @media (max-width: 768px) {
          font-size: 12px;
        }
      }

      // 输入框和选择器响应式
      .el-input,
      .el-select,
      .el-date-picker {
        @media (max-width: 576px) {
          width: 100% !important;
        }
      }
    }

    // 全局响应式处理
    @media (max-width: 768px) {
      // 强制表单列变为单列布局
      :deep(.el-col) {
        &.el-col-12,
        &.el-col-8 {
          max-width: 100% !important;
          flex: 0 0 100% !important;
        }
      }

      // 表格在小屏幕上的优化
      :deep(.el-table) {
        .el-table__body-wrapper {
          overflow-x: auto;
        }
      }

      // 折叠面板优化
      :deep(.el-collapse) {
        .el-collapse-item__header {
          padding: 12px 16px;
          font-size: 14px;
        }

        .el-collapse-item__content {
          padding: 16px;
        }
      }
    }

    // 超小屏幕优化
    @media (max-width: 576px) {
      :deep(.el-form-item) {
        margin-bottom: 16px;

        .el-form-item__label {
          font-size: 12px;
          padding-bottom: 4px;
        }

        .el-form-item__content {
          .el-input__inner,
          .el-textarea__inner {
            font-size: 14px;
          }
        }
      }

      // 表格操作按钮响应式
      :deep(.table-actions) {
        display: flex;
        flex-direction: column;
        gap: 4px;

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
        }
      }
    }
  }
}

.form-actions-wrapper {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  z-index: 1000;
  position: sticky;
  bottom: -20px;
  left: 0;
  right: 0;
  padding: 16px 24px;
  display: flex;
  gap: 12px;
  border-top: 1px solid var(--Color-Border-border-color-light, #e4e7ed);

  .form-actions {
    display: flex;
    justify-content: flex-start;
    gap: 12px;
    width: 100%;
  }
}

// 条件说明
.conditions-notice {
  margin-bottom: 16px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

// 上传区域
.upload-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 16px;

  span {
    font-size: 14px;
    color: #606266;
  }
}

// 标段信息样式
.bid-sections-container {
  .bid-section-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    .section-item {
      flex: 1;

      .section-label {
        display: block;
        margin-bottom: 6px;
        font-size: 14px;
        color: #606266;

        &.required::before {
          content: '*';
          color: #f56c6c;
          margin-right: 2px;
        }
      }

      .section-name-input,
      .section-number-input {
        width: 100%;
      }
    }

    .section-actions {
      flex-shrink: 0;

      .delete-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .add-section-btn-container {
    margin-top: 16px;
    text-align: left;

    .add-section-btn {
      border: 1px dashed #409eff;
      background-color: #fff;
      color: #409eff;

      &:hover {
        background-color: #ecf5ff;
      }
    }
  }
}

.lot-item {
  cursor: pointer;
  &.active {
    border-bottom: 2px solid var(--Color-Primary-color-primary, #0069ff);
  }
}

//:deep(.el-range-input) {
//  background: #fff;
//}
</style>
