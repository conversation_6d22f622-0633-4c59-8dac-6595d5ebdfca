<template>
  <el-dialog
    v-model="visible"
    :title="title"
    width="400px"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="project-transfer-confirm-dialog"
  >
    <div class="confirm-content">
      <div class="message">
        <span class="name">{{ transferName }}</span>
        <span>转交了</span>
        <span class="highlight">{{ projectCount }}</span>
        <span>个项目给您，请确认是否接收？</span>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button
          @click="handleCancel"
          class="cancel-btn"
        >
          取消
        </el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          class="confirm-btn"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineExpose } from 'vue';

interface ProjectTransferConfirmProps {
  transferName?: string;
  projectCount?: number;
  title?: string;
}

const props = withDefaults(defineProps<ProjectTransferConfirmProps>(), {
  transferName: '',
  projectCount: 0,
  title: '项目转交确认',
});

const emit = defineEmits<{
  confirm: [];
  cancel: [];
}>();

const visible = ref(false);

// 暴露给父组件的方法
const show = (data?: Partial<ProjectTransferConfirmProps>) => {
  if (data) {
    Object.assign(props, data);
  }
  visible.value = true;
};

const hide = () => {
  visible.value = false;
};

const handleConfirm = () => {
  emit('confirm');
  hide();
};

const handleCancel = () => {
  emit('cancel');
  hide();
};

// 暴露方法给父组件
defineExpose({
  show,
  hide,
});
</script>

<style lang="scss" scoped>
.project-transfer-confirm-dialog {
  :deep(.el-dialog__header) {
    padding: 20px 20px 0;
    border-bottom: none;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }

  :deep(.el-dialog__body) {
    padding: 20px;
  }

  :deep(.el-dialog__footer) {
    padding: 0 20px 20px;
    border-top: none;
  }
}

.confirm-content {
  text-align: center;

  .message {
    font-size: 14px;
    color: #333;
    line-height: 1.5;

    .name {
      font-weight: 500;
    }

    .highlight {
      color: #409eff;
      font-weight: 600;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 12px;
}
</style>
