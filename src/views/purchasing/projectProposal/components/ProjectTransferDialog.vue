<template>
  <el-dialog
    :model-value="visible"
    title="项目转交"
    width="520px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @update:model-value="(val: boolean) => emit('update:visible', val)"
  >
    <div class="transfer-dialog-content">
      <!-- 确认信息 -->
      <div class="confirm-message">您将发起{{ selectedCount }}个项目的转交，转交后项目负责人将变更为接受人，请确认是否转交？</div>

      <!-- 项目接手人选择 -->
      <div class="recipient-section">
        <div class="label">项目接手人:</div>
        <div class="input-wrapper">
          <UserSelector
            v-model="selectedUserId"
            placeholder="选择项目转交人"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          :loading="loading"
          :disabled="!selectedUserId"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import UserSelector from '@/components/UserSelector/index.vue';

interface User {
  userId: string;
  username: string;
  deptName?: string;
  departmentPath?: string;
}

interface Props {
  visible: boolean;
  selectedCount: number;
  loading: boolean;
}

interface Emits {
  (e: 'update:visible', value: boolean): void;
  (e: 'confirm', userId: string): void;
  (e: 'cancel'): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 选中的用户ID
const selectedUserId = ref('');

// 处理确认
const handleConfirm = () => {
  emit('confirm', selectedUserId.value);
};

// 处理取消
const handleCancel = () => {
  selectedUserId.value = '';
  emit('cancel');
};
</script>

<style lang="scss" scoped>
.transfer-dialog-content {
  .confirm-message {
    margin-bottom: 20px;
    color: #606266;
    line-height: 1.5;
  }

  .recipient-section {
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .label {
      white-space: nowrap;
      color: #606266;
      min-width: 90px;
      font-weight: 500;
      text-align: right;
    }

    .input-wrapper {
      flex: 1;
      min-width: 0;
    }
  }
}

.dialog-footer {
  text-align: center;
}
</style>
