import Pagination from '@ylz-material/pagination';
import '@ylz-material/pagination/lib/style.css';
import Ellipsis from '@ylz-material/ellipsis';
import '@ylz-material/ellipsis/lib/style.css';
import BatchOperation from '@ylz-material/batch-operation';
import '@ylz-material/batch-operation/lib/style.css';
import Descriptions from '@ylz-material/descriptions';
import '@ylz-material/descriptions/lib/style.css';
import Dialog from '@ylz-material/dialog';
import '@ylz-material/dialog/lib/style.css';
import Drawer from '@ylz-material/drawer';
import '@ylz-material/drawer/lib/style.css';
import Filter from '@ylz-material/filter';
import '@ylz-material/filter/lib/style.css';
import Import from '@ylz-material/import';
import '@ylz-material/import/lib/style.css';
import Detail from '@ylz-material/pro-detail';
import '@ylz-material/pro-detail/lib/style.css';
import Form from '@ylz-material/pro-form';
import '@ylz-material/pro-form/lib/style.css';
import PageTabs from '@ylz-material/page-tabs';
import '@ylz-material/page-tabs/lib/style.css';
import ProTable from '@ylz-material/pro-table';
import '@ylz-material/pro-table/lib/style.css';
import Table from '@ylz-material/table';
import '@ylz-material/table/lib/style.css';
import TableV2 from '@ylz-material/table-v2';
import '@ylz-material/table-v2/lib/style.css';
import Rest from '@ylz-material/rest';
import '@ylz-material/rest/lib/style.css';
import Task from '@ylz-material/task';
import '@ylz-material/task/lib/style.css';
import Upload from '@ylz-material/upload';
import '@ylz-material/upload/lib/style.css';
import i18n from '@ylz-material/i18n';
import '@ylz-material/i18n/lib/style.css';
import ProSelect from '@ylz-material/pro-select';
import '@ylz-material/pro-select/lib/style.css';
import AreaPicker from '@ylz-material/area-picker';
import '@ylz-material/area-picker/lib/style.css';
import YunIconPicker from '@ylz-material/icon-picker';
import '@ylz-material/icon-picker/lib/style.css';
// import YunChat from '@ylz-material/yun-chat';
// import '@ylz-material/yun-chat/lib/style.css';

const components = [
	BatchOperation,
	Descriptions,
	Dialog,
	Drawer,
	Filter,
	Import,
	Pagination,
	Ellipsis,
	Detail,
	Form,
	PageTabs,
	ProTable,
	Table,
	TableV2,
	Rest,
	Task,
	Upload,
	// i18n,
	ProSelect,
	AreaPicker,
  YunIconPicker,
	// YunChat,
  YunIconPicker,
];

interface Component {
	install: (app: any) => void;
}

const install = (app: any): void => {
	components.forEach((item: Component) => {
		app.use(item);
	});

	app.use(i18n, { namespace: ['materialSystem'] });
};

export default {
	install,
};
