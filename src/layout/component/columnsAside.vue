<template>
  <div class="layout-columns-aside">
    <img
      v-show="themeConfig.isCollapse && !isCurrentHome()"
      src="@/assets/icons/fanfold.svg"
      alt=""
      class="zhedieanniu"
      @click="onThemeConfigChange"
    />
    <el-scrollbar>
      <ul @mouseleave="onColumnsAsideMenuMouseleave()">
        <li
          v-for="(v, k) in state.columnsAsideList"
          :key="k"
          @click="onColumnsAsideMenuClick(v, k)"
          @mouseenter="onColumnsAsideMenuMouseenter(v, k)"
          :ref="
            (el) => {
              if (el) columnsAsideOffsetTopRefs[k] = el;
            }
          "
          :class="{
            'layout-columns-active': state.liIndex === k,
            'layout-columns-hover': state.liHoverIndex === k && state.liIndex !== k,
          }"
          style="height: 56px"
          :title="$t(v.name)"
        >
          <div
            :class="themeConfig.columnsAsideLayout"
            class="itemSize"
            v-if="!v.meta.isLink || (v.meta.isLink && v.meta.isIframe)"
          >
            <i
              :class="[`yun-iconfont iconSize ${v.meta?.icon}`]"
              style="font-size: 24px; line-height: 24px"
              v-if="!v.meta?.icon?.includes('iconfont')"
            ></i>
            <SvgIcon
              class="iconSize"
              v-else
              style="font-size: 24px; line-height: 24px"
              :name="v.meta.icon"
            />
            <div class="columns-vertical-title font10">
              {{
                $t(v.name) && $t(v.name).length >= 4
                  ? $t(v.name).substr(
                      0,
                      themeConfig.columnsAsideLayout === 'columns-vertical' ? 4 : 3
                    )
                  : $t(v.name)
              }}
            </div>
          </div>
          <div :class="themeConfig.columnsAsideLayout" v-else>
            <a :href="v.meta.isLink" target="_blank">
              <i
                :class="[`yun-iconfont iconSize ${v.meta?.icon}`]"
                style="font-size: 24px; line-height: 24px"
                v-if="!v.meta?.icon?.includes('iconfont')"
              ></i>
              <SvgIcon
                v-else
                class="iconSize"
                style="font-size: 24px; line-height: 24px"
                :name="v.meta.icon"
              />
              <div class="columns-vertical-title font10">
                {{
                  $t(v.name) && $t(v.name).length >= 4
                    ? $t(v.name).substr(
                        0,
                        themeConfig.columnsAsideLayout === 'columns-vertical' ? 4 : 3
                      )
                    : $t(v.name)
                }}
              </div>
            </a>
          </div>
        </li>
        <div ref="columnsAsideActiveRef" :class="themeConfig.columnsAsideStyle"></div>
      </ul>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts" name="layoutColumnsAside">
import { RouteRecordRaw } from 'vue-router';
import pinia from '/@/stores/index';
import { useRoutesList } from '/@/stores/routesList';
import { useThemeConfig } from '/@/stores/themeConfig';
import mittBus from '/@/utils/mitt';
import { watch } from 'vue';

// 定义变量内容
const columnsAsideOffsetTopRefs = ref<RefType>([]);
const columnsAsideActiveRef = ref();
const stores = useRoutesList();
const storesThemeConfig = useThemeConfig();
const { routesList, isColumnsMenuHover, isColumnsNavHover } = storeToRefs(stores);
const { themeConfig } = storeToRefs(storesThemeConfig);
const route = useRoute();
const router = useRouter();
const state = reactive<ColumnsAsideState>({
  columnsAsideList: [],
  liIndex: 0,
  liOldIndex: null,
  liHoverIndex: null,
  liOldPath: null,
  difference: 4,
  routeSplit: [],
});

const isCurrentHome = () => {
  const route = useRoute();
  //如果该路由是首页，返回true
  if (route.path === '/home') {
    return true;
  } else {
    return false;
  }
};

// logo 点击实现菜单展开/收起
const onThemeConfigChange = () => {
  if (themeConfig.value.layout === 'transverse') return false;
  themeConfig.value.isCollapse = !themeConfig.value.isCollapse;
};

// 设置菜单高亮位置移动
const setColumnsAsideMove = (k: number) => {
  state.liIndex = k;
  console.log(
    columnsAsideOffsetTopRefs.value[k].offsetTop,
    'columnsAsideOffsetTopRefs.value[k].offsetTop'
  );
  console.log(columnsAsideOffsetTopRefs.value[k], 'columnsAsideOffsetTopRefs.value[k]');
  columnsAsideActiveRef.value.style.top = `${
    columnsAsideOffsetTopRefs.value[k].offsetTop + state.difference
  }px`;
};
// 菜单高亮点击事件
const onColumnsAsideMenuClick = (v: RouteItem, k: number) => {
  setColumnsAsideMove(k);
  let { path, redirect } = v;
  if (redirect) router.push(redirect);
  else router.push(path);
  mittBus.emit('yijiclick');
};
// 鼠标移入时，显示当前的子级菜单
const onColumnsAsideMenuMouseenter = (v: RouteRecordRaw, k: number) => {
  if (!themeConfig.value.isColumnsMenuHoverPreload) return false;
  let { path } = v;
  state.liOldPath = path;
  state.liOldIndex = k;
  state.liHoverIndex = k;
  console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa1');
  mittBus.emit('setSendColumnsChildren', setSendChildren(path));
  stores.setColumnsMenuHover(false);
  stores.setColumnsNavHover(true);
};
// 鼠标移走时，显示原来的子级菜单
const onColumnsAsideMenuMouseleave = async () => {
  await stores.setColumnsNavHover(false);
  // 添加延时器，防止拿到的 store.state.routesList 值不是最新的
  setTimeout(() => {
    if (!isColumnsMenuHover && !isColumnsNavHover) mittBus.emit('restoreDefault');
  }, 100);
};
// 设置高亮动态位置
const onColumnsAsideDown = (k: number) => {
  nextTick(() => {
    setColumnsAsideMove(k);
  });
};
// 设置/过滤路由（非静态路由/是否显示在菜单中）
const setFilterRoutes = () => {
  state.columnsAsideList = filterRoutesFun(routesList.value);
  const resData: MittMenu = setSendChildren(route.path);
  if (Object.keys(resData).length <= 0) return false;
  onColumnsAsideDown(resData.item?.k);
  mittBus.emit('setSendColumnsChildren', resData);
};
// 传送当前子级数据到菜单中
const setSendChildren = (path: string) => {
  const parentRoute = searchParent(routesList.value, path) as any;
  let currentData: MittMenu = { children: [] };

  // 检查 parentRoute 是否为 undefined
  if (!parentRoute) {
    return currentData;
  }

  state.columnsAsideList.map((v: RouteItem, k: number) => {
    if (v.path === parentRoute.path) {
      v['k'] = k;
      currentData['item'] = { ...v };
      currentData['children'] = [{ ...v }];
      if (v.children) currentData['children'] = v.children;
    }
  });
  // 关键：同步到 pinia
  stores.setCurrentColumnsChildren(currentData);
  return currentData;
};
// 路由过滤递归函数
const filterRoutesFun = <T extends RouteItem>(arr: T[]): T[] => {
  return arr
    .filter((item: T) => !item.meta?.isHide)
    .map((item: T) => {
      item = Object.assign({}, item);
      if (item.children) item.children = filterRoutesFun(item.children);
      return item;
    });
};
// tagsView 点击时，根据路由查找下标 columnsAsideList，实现左侧菜单高亮
const setColumnsMenuHighlight = (path: string) => {
  const parentRoute = searchParent(routesList.value, path) as any;
  const currentSplitRoute = state.columnsAsideList.find(
    (v: RouteItem) => v.path === parentRoute?.path
  );
  if (!currentSplitRoute) return false;
  // 延迟拿值，防止取不到
  setTimeout(() => {
    onColumnsAsideDown(currentSplitRoute.k);
  }, 0);
};

// 使用递归查询对应的父级路由
const searchParent = (routesList: any, path: string) => {
  let route = undefined;
  routesList.forEach((item) => {
    if (item.path === path) {
      route = item;
      return;
    }
    if (item.children && searchParent(item.children, path)) {
      route = item;
      return;
    }
  });
  return route;
};

// 页面加载时
onMounted(() => {
  setFilterRoutes();
  // 销毁变量，防止鼠标再次移入时，保留了上次的记录
  // 	mittBus.on('tenantChanged' as keyof MittType<any>, () => {
  //     setFilterRoutes();
  //   });
  mittBus.on('restoreDefault', () => {
    state.liOldIndex = null;
    state.liOldPath = null;
  });
});
// 页面卸载时
onUnmounted(() => {
  // mittBus.off('tenantChanged' as keyof MittType<any>);
  mittBus.off('restoreDefault', () => {});
});

mittBus.on('asideMonted', () => {
  mittBus.emit('setSendColumnsChildren', setSendChildren(route.path));
});
watch(
  () => route.path,
  (val) => {
    nextTick(() => {
      setColumnsMenuHighlight(val);
      mittBus.emit('setSendColumnsChildren', setSendChildren(val));
    });
  },
  {
    immediate: true,
  }
);

// 监听 stores 中与路由相关的数据变化
watch(
  () => stores.routesList, // 监听 stores 中的 routesList 变化
  (newRoutesList) => {
    // 数据变化时，更新 columnsAsideList
    state.columnsAsideList = filterRoutesFun(newRoutesList);
    if (newRoutesList.length > 0) {
      onColumnsAsideDown(0);
    }
    const resData: MittMenu = setSendChildren(route.path);
    if (Object.keys(resData).length <= 0) return;
    console.log('aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa6');
    mittBus.emit('setSendColumnsChildren', resData);

    //菜单回到首页
  },
  { deep: true } // 深度监听，确保嵌套对象变化也能被捕获
);
</script>

<style scoped lang="scss">
.iconSize {
  font-size: 24px;
}
.itemSize {
  height: 48px;
  width: 48px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin: auto;
}
.layout-columns-aside {
  width: 66px;
  height: 100%;
  background: var(--next-bg-columnsMenuBar);
  // background: #ffffff;
  border-right: 2px solid var(--next-border-color-light);
  position: relative;
  .zhedieanniu {
    position: absolute;
    bottom: 64px;
    right: 10px;
    width: 30px;
    height: 30px;
    z-index: 1000;
  }
  ul {
    position: relative;

    .layout-columns-active {
      // color: var(--next-bg-columnsMenuBarColor) !important;
      color: #ffffff;

      transition: 0.3s ease-in-out;
      // 提高选中状态的层级
      z-index: 2;
      // 明确覆盖 hover 背景色
      background: transparent !important;
    }

    .layout-columns-hover {
      //color: var(--el-color-primary);//此处颜色跟active北京颜色一致了，感觉有问题
      //color: var(--next-bg-columnsMenuBarColor) !important;//
      background: var(---el-color-normal-bg, RGB(242, 243, 245));
      a {
        //color: var(--el-color-primary);
      }
    }

    li {
      color: var(--next-bg-columnsMenuBarColor);
      // color: #1C2026;
      width: 100%;
      height: 50px;
      text-align: center;
      display: flex;
      cursor: pointer;
      position: relative;
      z-index: 1;

      &:hover {
        @extend .layout-columns-hover;
      }

      .columns-vertical {
        margin: auto;

        .columns-vertical-title {
          // padding-top: 1px;
          line-height: 16px;
          // margin-top:-10px;
          // margin-bottom:3px;
        }
      }

      .columns-horizontal {
        display: flex;
        height: 50px;
        width: 100%;
        align-items: center;
        padding: 0 5px;

        i {
          margin-right: 3px;
        }

        a {
          display: flex;

          .columns-horizontal-title {
            padding-top: 1px;
          }
        }
      }

      a {
        text-decoration: none;
        color: var(--next-bg-columnsMenuBarColor);
      }
    }

    .columns-round {
      background: var(--el-color-primary);
      color: var(--el-color-white);
      position: absolute;
      left: 50%;
      top: 2px;
      height: 48px;
      width: 48px;
      transform: translateX(-50%);
      z-index: 0;
      //transition: 0.3s ease-in-out;
      border-radius: 5px;
      // z-index:1;
    }

    .columns-card {
      @extend .columns-round;
      top: 0;
      height: 50px;
      width: 100%;
      border-radius: 0;
    }
  }
}
</style>
