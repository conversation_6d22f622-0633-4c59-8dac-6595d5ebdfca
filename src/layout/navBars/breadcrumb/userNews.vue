<template>
	<div class="layout-navbars-breadcrumb-user-news">
		<div class="head-box">
			<div class="head-box-title text-[#1C2026] font-medium text-[18px] leading-[26px]">{{ $t('user.newTitle') }}</div>
			<div
				class="head-box-btn"
				@click="openDrawer"
			>
				{{ $t('user.newBtn') }}
			</div>
		</div>
		<div class="content-box">
			<template v-if="newsList.length > 0">
				<div class="flex flex-col gap-[8px]">
					<div
						class="content-box-item"
						v-for="(v, k) in newsList"
						:key="k"
						@click="openContentDrawer(v)"
					>
						<div class="text-[#1C2026] font-medium text-[16px] leading-[22px]">
							{{ v.title }}
						</div>
						<div class="text-[#505762] font-medium text-[14px] leading-[22px]">
							{{ v.content }}
						</div>
						<button class="text-[#7E8694] font-medium text-[14px] leading-[22px] text-end">
							{{ v.createTime }}
						</button>
					</div>
				</div>
			</template>
			<el-empty
				:description="$t('user.newDesc')"
				v-else
			></el-empty>
		</div>
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbUserNews">
import { fetchUserMessageList } from '/@/api/admin/message';

const newsList = ref([]);
const emits = defineEmits(['openDrawer', 'openContentDrawer']);

const openDrawer = () => {
	emits('openDrawer');
};
const openContentDrawer = (v) => {
	emits('openContentDrawer', v);
};
// 获取用户的信息
const getUserMessage = () => {
	// 取前五条数据
	return fetchUserMessageList({ current: 1, size: 5, category: '1', readFlag: '0' }).then((res) => {
		newsList.value = res.data.records;
	});
};

onMounted(() => {
	getUserMessage();
});
defineExpose({
	getUserMessage,
});
</script>

<style scoped lang="scss">
.layout-navbars-breadcrumb-user-news {
	.head-box {
		display: flex;
		padding: 20px 20px 10px 20px;
		border-bottom: 1px solid var(--el-border-color-lighter);
		box-sizing: border-box;
		color: var(--el-text-color-primary);
		justify-content: space-between;
		height: 35px;
		align-items: center;

		.head-box-btn {
			color: var(--el-color-primary);
			font-size: 13px;
			cursor: pointer;
			opacity: 0.8;

			&:hover {
				opacity: 1;
			}
		}
	}

	.content-box {
		padding: 0px 12px 16px 12px;

		.content-box-item {
			display: flex;
			flex-direction: column;
			padding: 12px 8px 4px 8px;
			gap: 8px;
			border-bottom: 1px solid #e6eaf0;
			overflow-y: auto;
			&:hover {
				background-color: #e6eaf0;
			}
		}
	}

	.foot-box {
		height: 35px;
		color: var(--el-color-primary);
		font-size: 13px;
		cursor: pointer;
		opacity: 0.8;
		display: flex;
		align-items: center;
		justify-content: center;
		border-top: 1px solid var(--el-border-color-lighter);

		&:hover {
			opacity: 1;
		}
	}

	:deep(.el-empty__description p) {
		font-size: 13px;
	}
}
</style>
