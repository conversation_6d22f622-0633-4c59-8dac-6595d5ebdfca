<template>
	<div class="layout-search-dialog">
		<el-dialog
			v-model="state.isShowSearch"
			destroy-on-close
			:show-close="false"
			width="560"
		>
			<div class="" style="height:280px">
				<el-autocomplete
					v-if="state.isShowSearch"
					v-model="state.menuQuery"
					:fetch-suggestions="menuSearch"
					:placeholder="$t('user.searchPlaceholder')"
					ref="layoutMenuAutocompleteRef"
					@select="onHandleSelect"
					:fit-input-width="true"
				>
					<template #prefix>
						<el-icon class="el-input__icon">
							<ele-Search />
						</el-icon>
					</template>
					<template #default="{ item }">
						<div>
							<SvgIcon
								:name="item.meta.icon"
								class="mr5"
							/>
							{{ $t(item.name) }}
						</div>
					</template>
				</el-autocomplete>
				<p style="margin-top:16px;line-height: 28px;font-size:12px;color:#7E8694">最近搜索</p>
				<ul>
					<!-- @click="selectRecentSearch(record)" -->
					<li 
						v-for="(record, index) in state.recentSearches" 
						:key="index" 
						@click="selectRecentSearch(record)"
						style="cursor: pointer;"
						class="recent-search-item"
					>
						<SvgIcon
							:name="record.icon"
							class="mr5"
						/>
						{{ $t(record.name) }}
					</li>
				</ul>
			</div>
		</el-dialog>
	</div>
</template>

<script setup lang="ts" name="layoutBreadcrumbSearch">
import { useI18n } from 'vue-i18n';
import { useTagsViewRoutes } from '/@/stores/tagsViewRoutes';
import mittBus from '/@/utils/mitt';
// import { ref, reactive, nextTick, storeToRefs } from 'vue';
// import { useRouter } from 'vue-router';

// 定义 RouteItem 类型
interface RouteItem {
	path: string;
	name: string;
	meta: {
		icon: string;
		isHide?: boolean;
		isLink?: string;
		isIframe?: boolean;
	};
	redirect?: string;
}

// 定义 SearchState 类型
// interface SearchState {
// 	isShowSearch: boolean;
// 	menuQuery: string;
// 	tagsViewList: RouteItem[];
// 	recentSearches: string[];
// }

const storesTagsViewRoutes = useTagsViewRoutes();
const { tagsViewRoutes } = storeToRefs(storesTagsViewRoutes);
const layoutMenuAutocompleteRef = ref();
const { t } = useI18n();
const router = useRouter();

// 定义最近搜索记录的类型
interface RecentSearchItem {
	name: string;
	icon: string;
}

// 定义 SearchState 类型
interface SearchState {
	isShowSearch: boolean;
	menuQuery: string;
	tagsViewList: RouteItem[];
	recentSearches: RecentSearchItem[];
}

const state = reactive<SearchState>({
	isShowSearch: false,
	menuQuery: '',
	tagsViewList: [],
	// 从本地存储读取最近搜索记录，若没有则初始化为空数组
	recentSearches: JSON.parse(localStorage.getItem('recentSearches') || '[]')
});

// 保存最近搜索记录到本地存储
const saveRecentSearches = () => {
	localStorage.setItem('recentSearches', JSON.stringify(state.recentSearches));
};

// 添加最近搜索记录
const addRecentSearch = (item: RouteItem) => {
	const record: RecentSearchItem = {
		name: item.name,
		icon: item.meta.icon
	};
	const existingIndex = state.recentSearches.findIndex(r => r.name === record.name);
	if (existingIndex > -1) {
		state.recentSearches.splice(existingIndex, 1);
	}
	state.recentSearches.unshift(record);
	// 只保留最近 5 条记录
	state.recentSearches = state.recentSearches.slice(0, 5);
	saveRecentSearches();
};

// 选择最近搜索记录
const selectRecentSearch = (record: RecentSearchItem) => {
	state.menuQuery = record.name;
	// 触发搜索
	menuSearch(record.name, (results: RouteItem[]) => {
		const selectedResult = results.find(item => item.name === record.name);
		if (selectedResult) {
			onHandleSelect(selectedResult);
		}
	})
};

// 当前菜单选中时
const onHandleSelect = (item: RouteItem) => {
	
	console.log(item,'item')
	let { path, redirect } = item;
	mittBus.emit('yijiclick')
	if (item.meta?.isLink && !item.meta?.isIframe) window.open(item.meta?.isLink);
	else if (redirect) router.push(redirect);
	else router.push(path);
	// 添加到最近搜索
	addRecentSearch(item);
	closeSearch();
};

// 搜索弹窗打开
const openSearch = () => {
	state.menuQuery = '';
	state.isShowSearch = true;
	initTageView();
	nextTick(() => {
		setTimeout(() => {
			// layoutMenuAutocompleteRef.value.focus();
			// 在此处获取最近搜索数据
			state.recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
		});
	});
};

// 搜索弹窗关闭
const closeSearch = () => {
	state.isShowSearch = false;
};

// 菜单搜索数据过滤
const menuSearch = (queryString: string, cb: Function) => {
	queryString = t(queryString)
	let results = queryString ? state.tagsViewList.filter(createFilter(queryString)) : state.tagsViewList;	
	console.log(cb,'cb')
	cb(results);
};

// 菜单搜索过滤
const createFilter = (queryString: string) => {
	return (restaurant: RouteItem) => {
		return restaurant.path.toLowerCase().indexOf(queryString.toLowerCase()) > -1 || t(restaurant!.name!).indexOf(queryString.toLowerCase()) > -1;
	};
};

// 初始化菜单数据
const initTageView = () => {
	if (state.tagsViewList.length > 0) return false;
	tagsViewRoutes.value.map((v: RouteItem) => {
		if (!v.meta?.isHide) state.tagsViewList.push({ ...v });
	});
};

// 暴露变量
defineExpose({
	openSearch,
});
</script>

<style scoped lang="scss">
:deep(.el-overlay .el-overlay-dialog .el-dialog) {
	overflow: visible;
}
.layout-search-dialog {
	position: relative;
	:deep(.el-dialog) {
	  width: 560px;
	  .el-dialog__header{
	    display: none;
	  }
	  .el-dialog__body {
	    padding: 32px !important;
	    // height: 280px;
	    // overflow: auto;	
	  }
	  // .el-dialog__footer {
	  //   position: absolute;
	  //   left: 50%;
	  //   transform: translateX(-50%);
	  //   top: -53vh;
	  // }
	}
	:deep(.el-autocomplete) {
		width: 100%;
	}
	:deep(.el-input__inner) {
		border-radius:20px;
	}
}
.recent-search-item{
  font-size: 14px;
  height: 32px;
  line-height: 32px;
  padding-left:12px;
}
</style>
