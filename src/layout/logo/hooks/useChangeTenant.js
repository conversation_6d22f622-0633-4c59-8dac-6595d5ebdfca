// import { useRouter } from 'vue-router';
import { storeToRefs } from 'pinia';
// import Cookies from 'js-cookie';
// import { useMessage } from '/@/hooks/message';
import { resetDict } from '/@/hooks/dict';
import pinia from '/@/stores/index';
import { useRoutesList } from '/@/stores/routesList';
import { useUserInfo } from '/@/stores/userInfo';
import mittBus from '/@/utils/mitt';
import { Session } from '/@/utils/storage';
// import { initBackEndControlRoutes } from '/@/router/backEnd';
// import { logout } from '/@/api/login';
import { smsCode } from '/@/api/admin/tenant';
import { dynamicRoutes } from '/@/router/route';

export default function () {
	// const router = useRouter();

	// 请求手机验证码
	const stores = useUserInfo();
	const { userInfos } = storeToRefs(stores);

	const changeTenant = async (newTenantId) => {
		const username = userInfos.value.user.username;
		const data = {
			tenantId: newTenantId,
			username,
		};
		const res = await smsCode(data);
		const { data: secret } = res;
		try {
			// mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router, changeTenant: true }));
			// 登出
			// await logout();
			// 清除缓存/token等
			Session.clear();
			// 设置新tenantId
			Session.set('tenantId', newTenantId);
			// Local.set('tenantId', newTenantId);
			// 登录
			await stores.loginBySecret({
				username,
				secret,
			});
      resetDict();
			// mittBus.emit('onCurrentContextmenuClick', Object.assign({}, { contextMenuClickId: 3, ...router, changeTenant: true }));
			// console.log('token', Session.getToken());
			const storesRoutesList = useRoutesList(pinia);
			storesRoutesList.setRoutesList([]);
			const { component, ...rest } = dynamicRoutes.find((v) => v.path === '/home');
			mittBus.emit(
				'onCurrentContextmenuClick',
				Object.assign(
					{},
					{
						contextMenuClickId: 3,
						...rest,
						url: '/home',
						query: {
							time: `${+new Date()}`,
						},
						changeTenant: true,
					}
				)
			);
			// router.push({
			// 	path: '/home',
			// 	query: {
			// 		time: `${+new Date()}`,
			// 	},
			// });
			// const isNoPower = await initBackEndControlRoutes();
			// if (isNoPower) {
			// 	useMessage().warning('抱歉，您没有登录权限');
			// 	Session.clear();
			// } else {
			// 	router.push('/');
			// }
		} catch (error) {
			Promise.reject(error);
			Session.clear();
			window.location.reload();
		}
	};
	return {
		changeTenant,
	};
}
