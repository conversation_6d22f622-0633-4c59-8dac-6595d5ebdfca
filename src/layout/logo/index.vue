<template>
	<!-- v-if="setShowLogo" -->
	<div class="layout-logo">
		<img
			:src="logoMini"
			style="width: 120px; height: 32px; margin-right: 20px"
			@click="onThemeConfigChange"
			class="layout-logo-size-img"
		/>
		<el-select
			:modelValue="tenantId"
			style="width: 220px; margin-right: 20px"
			filterable
			@change="onChangeTenant"
			@focus="getTenantList"
		>
			<el-option
				v-for="item in tenantInfos"
				:key="item.id"
				:label="item.name"
				:value="item.id"
        style="min-width: 220px"
			></el-option>
		</el-select>
		<el-select
			v-model="identityId"
			style="width: 220px; margin-right: 20px"
			filterable
			@change="handleIdentityChange"
			v-if="isMutiIdentity"
		>
			<el-option
        style="min-width: 220px"
				v-for="item in identityList"
				:key="item.value"
				:label="item.label"
				:value="item.value"
			></el-option>
		</el-select>
		<!-- <span :style="{ color: setFontColor }">{{ themeConfig.globalTitle }}</span> -->
	</div>
  <!-- {{ identityId }} -->
	<!-- <div class="layout-logo-size" v-else @click="onThemeConfigChange">
		<img :src="logoMini" class="layout-logo-size-img" />
	</div> -->
</template>

<script setup lang="ts" name="layoutLogo">
import { storeToRefs } from 'pinia';
import { ElMessage } from 'yun-design';
import { Session } from '/@/utils/storage';
import { appLoading } from '/@/hooks/useAppLoading';
import { useUserInfo } from '/@/stores/userInfo';
import { useThemeConfig } from '/@/stores/themeConfig';
import useChangeTenant from './hooks/useChangeTenant';
// import logoMini from '/@/assets/favicon.ico';
import logoMini from '/@/assets/logoimg.png';
import mittBus from '/@/utils/mitt';
import { refreshMenusAfterIdentityChange } from '/@/router/backEnd';

const stores = useUserInfo();
const { userInfos } = storeToRefs(stores);

const getCurTenantInfo = ()=>{
  const tenantId = ref(Session.getTenant()?.toString());
  const tenantInfos = userInfos.value.tenantInfos || []
  return tenantInfos.find(item=>{
    return item.id?.toString() === tenantId.value
  }) ||null
}
const isMutiIdentity = ref()
const changeIsMutiIndentity = ()=>{
  const curTenantInfo = getCurTenantInfo()
  if(curTenantInfo){
    isMutiIdentity.value = curTenantInfo.multipleIdentities
  }
}
onMounted(()=>{
  changeIsMutiIndentity()
  mittBus.on('tenantChanged' as keyof MittType<any>, () => {
    changeIsMutiIndentity()
    // 重新拉取身份ID和身份列表
    identityId.value = Session.getIdentityId()?.toString();
    // 如果有必要，也可以重新拉取用户信息
    // stores.setUserInfos(true);
  });
})
onUnmounted(() => {
  mittBus.off('tenantChanged' as keyof MittType<any>);
});

// 定义变量内容
const storesThemeConfig = useThemeConfig();
const { themeConfig } = storeToRefs(storesThemeConfig);

// 设置 logo 的显示。classic 经典布局默认显示 logo
const setShowLogo = computed(() => {
  let { isCollapse, layout } = themeConfig.value;
  return !isCollapse || layout === 'classic' || document.body.clientWidth < 1000;
});

// 设置 title 的显示颜色。根据布局模式自动显示
const setFontColor = computed(() => {
	let { layout } = themeConfig.value;
  return layout === 'classic' || layout === 'transverse'
    ? `var(--next-bg-topBarColor)`
    : 'var(--el-color-primary)';
});

// logo 点击实现菜单展开/收起
const onThemeConfigChange = () => {
  if (themeConfig.value.layout === 'transverse') return false;
  themeConfig.value.isCollapse = !themeConfig.value.isCollapse;
};

const tenantInfos = ref<{id: string; name: string; status?: string}[]>([]);
const tenantId = ref(Session.getTenant()?.toString());
const identityId = ref(Session.getIdentityId()?.toString());
const identityList = computed(() =>
	(userInfos.value.sysIdentityList?.map((item: any) => ({
		label: item.identityName,
		value: item.id
	})) || [])
);

// 切换身份时的处理逻辑
function handleIdentityChange(val: string | number) {
	identityId.value = val;
	Session.set('identityId', val);
	// 切换身份后刷新菜单
	refreshMenusAfterIdentityChange();
}
// 切换租户

const { changeTenant } = useChangeTenant();
async function onChangeTenant(v) {
  appLoading.value = true;
  try {
    await changeTenant(v);
    tenantId.value = v;
    // 触发菜单刷新事件
    mittBus.emit('tenantChanged' as keyof MittType<any>);
    // ElMessage.success('切换成功');
    // window.location.reload();
  } finally {
    appLoading.value = false;
  }
}
function getTenantList() {
  // 取消请求租户
  // stores.setUserInfos();
}
watch(
  userInfos,
  (v) => {
    tenantInfos.value = v.tenantInfos.filter((e) => e.status !== '9') || [];
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.layout-logo {
  // width: 365px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 20px;
  box-shadow: rgb(0 21 41 / 2%) 0px 1px 4px;
  font-size: 16px;
  cursor: pointer;
  animation: logoAnimation 0.3s ease-in-out;

  span {
    white-space: nowrap;
    display: inline-block;
    font-size: 21.5px;
    font-weight: 700;
    white-space: nowrap;
  }

  &:hover {
    span {
      color: var(--color-primary-light-2);
    }
  }
}

.layout-logo-size {
  width: 100%;
  height: 50px;
  display: flex;
  cursor: pointer;
  animation: logoAnimation 0.3s ease-in-out;

  &-img {
    width: 20px;
    margin: auto;
  }

  &:hover {
    img {
      animation: logoAnimation 0.3s ease-in-out;
    }
  }
}
</style>
