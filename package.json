{"name": "saas-monomer-admin", "version": "5.7.0", "description": "CPC PAAS平台", "author": "", "license": "", "scripts": {"dev": "NODE_OPTIONS='--no-deprecation' vite --force", "qa": "vite --force", "preview": "vite preview", "build": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:qa": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build", "build:docker": "cross-env NODE_OPTIONS=--max-old-space-size=8192 vite build --outDir ./docker/dist/", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "prettier": "prettier --write .", "postinstall": "node scripts/apply-bpmn-patch.js"}, "dependencies": {"@axolo/json-editor-vue": "^0.3.2", "@chenfengyuan/vue-qrcode": "^2.0.0", "@popperjs/core": "2.11.8", "@rollup/plugin-commonjs": "^28.0.3", "@thales/cli": "^2.1.2", "@thales/track": "1.0.55", "@vitejs/plugin-vue-jsx": "3.0.1", "@vueuse/core": "^10.4.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "@ylz-material/area-picker": "1.1.2", "@ylz-material/batch-operation": "1.3.1", "@ylz-material/button-group": "1.2.18", "@ylz-material/descriptions": "1.1.3", "@ylz-material/dialog": "1.2.0", "@ylz-material/drawer": "1.2.0", "@ylz-material/ellipsis": "1.0.3", "@ylz-material/filter": "1.3.4", "@ylz-material/header-menu": "1.2.1", "@ylz-material/i18n": "^1.1.12", "@ylz-material/icon-picker": "^1.1.6", "@ylz-material/import": "1.2.6", "@ylz-material/page-tabs": "1.2.0", "@ylz-material/pagination": "1.2.2", "@ylz-material/pro-detail": "1.3.3", "@ylz-material/pro-form": "1.6.11", "@ylz-material/pro-select": "1.4.6", "@ylz-material/pro-table": "1.3.11", "@ylz-material/rest": "1.2.8", "@ylz-material/side-menu": "1.1.37", "@ylz-material/table": "1.2.28", "@ylz-material/table-v2": "1.1.14", "@ylz-material/task": "^1.1.2", "@ylz-material/theme": "1.1.3", "@ylz-material/upload": "1.3.2", "@ylz-material/yun-chat": "1.1.0", "@ylz-use/core": "^1.2.23", "@yun-design/icons-vue": "^1.0.0", "ace-builds": "^1.4.12", "autoprefixer": "^10.4.7", "axios": "^1.3.3", "bpmn-js": "^8.10.0", "bpmn-js-task-resize": "^1.2.0", "bpmnlint": "^8.3.2", "bpmnlint-loader": "^0.1.6", "clipboard": "^2.0.11", "codemirror": "5.65.5", "codemirror-editor-vue3": "^2.0.6", "core-js": "^3.42.0", "crypto-js": "^3.1.9-1", "dayjs": "^1.11.13", "decimal.js": "^10.6.0", "diagram-js": "^12.2.0", "diagram-js-minimap": "^2.1.1", "disable-devtool": "^0.3.8", "docxtemplater": "^3.63.2", "driver.js": "^0.9.8", "echarts": "^5.4.1", "element-plus": "2.5.5", "exceljs": "^4.4.0", "file-saver": "2.0.5", "form-designer-plus": "^0.1.5", "fuse.js": "6.6.2", "github-markdown-css": "^5.8.1", "gsap": "^3.13.0", "highlight.js": "^11.7.0", "html2canvas": "^1.4.1", "js-audio-recorder": "^1.0.7", "js-base64": "^3.7.7", "js-cookie": "^3.0.1", "json-editor-vue3": "^1.1.1", "jspdf": "^3.0.1", "jszip": "^3.10.1", "lamejs": "^1.2.1", "lodash": "^4.17.21", "marked": "^15.0.7", "mitt": "^3.0.1", "monaco-editor": "^0.52.2", "nanoid": "^5.0.9", "nprogress": "^0.2.0", "pinia": "2.0.32", "pizzip": "^3.2.0", "postcss": "8.4.40", "qrcode": "1.5.1", "qs": "^6.11.0", "screenfull": "^6.0.2", "sm-crypto": "^0.3.12", "sortablejs": "^1.15.6", "splitpanes": "^3.1.5", "tailwindcss": "3.4.6", "v-calendar": "3.1.2", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.5.13", "vue-clipboard3": "^2.0.0", "vue-draggable-next": "^2.2.1", "vue-echarts": "6.6.1", "vue-i18n": "9.2.2", "vue-router": "^4.1.6", "vue3-quill": "^0.3.1", "vue3-tree-org": "^4.2.2", "vue3-video-play": "1.3.1-beta.6", "vuedraggable": "^4.1.0", "vxe-table": ">=4.5.10", "x2js": "^3.4.4", "ylz-bpmn-js-token-simulation": "^0.11.0", "yun-design": "1.1.77"}, "devDependencies": {"@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@types/markdown-it": "^14.1.1", "@types/node": "^18.14.0", "@types/nprogress": "^0.2.0", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.0", "@typescript-eslint/eslint-plugin": "^5.53.0", "@typescript-eslint/parser": "^5.53.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/compiler-sfc": "^3.2.47", "consola": "^2.15.3", "cross-env": "7.0.3", "daisyui": "4.11.1", "eslint": "^8.34.0", "eslint-plugin-vue": "^9.9.0", "patch-package": "^8.0.0", "pinia-plugin-persist": "^1.0.0", "postinstall-postinstall": "^2.1.0", "prettier": "2.8.4", "sass": "1.58.3", "terser": "^5.31.1", "typescript": "^4.9.5", "unplugin-auto-import": "^0.13.0", "vite": "4.3.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-style-import": "^2.0.0", "vite-plugin-top-level-await": "^1.3.0", "vite-plugin-vue-setup-extend": "^0.4.0", "vue-eslint-parser": "^9.1.0"}, "resolutions": {"css-selector-tokenizer": "https://registry.npmmirror.com/css-selector-tokenizer/-/css-selector-tokenizer-0.8.0.tgz"}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "bugs": {"url": "https://xxx.com"}, "engines": {"node": ">=16.0.0", "npm": ">= 7.0.0", "yarn": ">= 1.22.0"}, "keywords": ["vue", "vue3", "vuejs/vue-next", "element-ui", "element-plus"], "overrides": {"@swc/core": "1.6.13"}}