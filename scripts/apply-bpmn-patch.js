const fs = require('fs');
const path = require('path');
const modifications = require('./bpmn-modifications');

const bpmnJsonPath = path.join(__dirname, '../node_modules/bpmn-moddle/resources/bpmn/json/bpmn.json');

// 读取原始的 bpmn.json
const bpmnJson = JSON.parse(fs.readFileSync(bpmnJsonPath, 'utf8'));

// 修改 bpmn.json
const modifiedBpmnJson = {
  ...bpmnJson,
  types: bpmnJson.types.map(type => {
    const typeModification = modifications.typeModifications[type.name];
    if (typeModification) {
      return {
        ...type,
        // 合并父类
        superClass: typeModification.superClass 
          ? [...type.superClass, ...typeModification.superClass]
          : type.superClass,
        // 合并属性
        properties: typeModification.properties
          ? [...type.properties, ...typeModification.properties]
          : type.properties
      };
    }
    return type;
  })
};

// 写回修改后的文件
fs.writeFileSync(bpmnJsonPath, JSON.stringify(modifiedBpmnJson, null, 2)); 