// bpmn.json 的修改配置
module.exports = {
  // 类型修改
  typeModifications: {
    // Interface 类型的修改
    Interface: {
      // 添加的父类
      superClass: ['BaseElement'],
      // 添加的属性
      properties: [
        {
          name: 'dataObjects',
          type: 'DataObject',
          isMany: true
        }
      ]
    },
    // Process 类型的修改
    Process: {
      properties: [
        {
          name: 'customProperty',
          type: 'String',
          isAttr: true
        },
        {
          name: 'dataObjects',
          type: 'DataObject',
          isMany: true
        }
      ]
    },
    // DataObject 类型的修改
    DataObject: {
      properties: [
        {
          name: 'id',
          isAttr: true,
          type: 'String',
          isId: true
        },
        {
          name: 'name',
          isAttr: true,
          type: 'String'
        },
        {
          name: 'itemSubjectRef',
          isAttr: true,
          type: 'String'
        },
        {
          name: 'extensionElements',
          type: 'ExtensionElements'
        }
      ]
    },
    // ExtensionElements 类型的修改
    ExtensionElements: {
      // properties: [
      //   {
      //     name: 'values',
      //     type: 'Element',
      //     isMany: true
      //   }
      // ]
    }
  }
}; 